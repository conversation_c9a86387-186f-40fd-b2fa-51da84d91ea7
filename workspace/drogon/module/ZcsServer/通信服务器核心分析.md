# ZcsServer - 通信服务器核心分析

## 概述

ZcsServer是Dragon项目的核心通信服务器模块，负责处理电力系统中各种通信协议，实现数据采集、命令下发、状态监控等功能。该模块采用分层架构设计，支持多种电力行业标准协议。

## 模块架构设计

```
ZcsServer/
├── common/                          # 通用组件库
│   ├── 基础工具类和公共定义
│   ├── 线程管理、消息处理组件
│   ├── 观察者模式组件
│   └── API接口定义
├── server/                          # 服务器核心组件
│   ├── ZcsMain/                     # 主服务器程序
│   ├── ZcsFrontMgr/                 # 前置管理器
│   ├── ZcsFrontSrv/                 # 前置服务器
│   ├── ZcsCliMngr/                  # 客户端管理器
│   ├── ZcsDataHdl/                  # 数据处理中心
│   ├── ZcsSrvMgrMain/               # 服务端管理器主程序
│   ├── ZcsSrvMngr/                  # 服务器管理器动态库
│   └── ZcsSsbMngr/                  # 订阅中心管理器
└── pro/                             # 协议实现层
    ├── common/                      # 协议通用组件
    ├── Zcs104VlanSrv/              # IEC 104 VLAN协议实现
    ├── ZcsPro61850Cli/             # IEC 61850客户端协议
    ├── ZcsProMainFlow/             # 主通信流程处理
    └── [其他协议实现...]
```

## 核心组件详细分析

### 1. ZcsMain - 主服务器程序

**功能**：作为整个通信系统的统一入口和控制中心

**核心职责**：
- 加载和启动所有必要的动态库组件
- 协调各个子系统的运行
- 管理组件生命周期
- 处理系统级异常和关闭

**启动流程**：
```cpp
bool CServerEngine::Start()
{
    // 1. 初始化配置和日志
    if (!Init()) {
        return false;
    }
    
    // 2. 启动订阅中心（消息总线）
    if (StartSubscribeCenter() == 0) {
        m_LogFile.Add("StartSubscribeCenter successfully");		
    }
    
    // 3. 启动数据处理中心
    if (StartDataHandler() == 0) {
        m_LogFile.Add("StartDataHandler successfully");		
    }
    
    // 4. 启动服务器管理器
    if (StartServerMgr() == 0) {
        m_LogFile.Add("startServerMgr successfully");		
    }
    
    // 5. 启动客户端管理器（可选）
    if (m_bStartClientManager && (m_nServerRole != 2 )) {
        if (StartClientMgr() == 0) {
            m_LogFile.Add("StartClientMgr successfully");		
        }
        
        // 6. 启动在线管理器
        if (StartSrvOnlineManager() == 0) {
            m_LogFile.Add("StartSrvOnlineManager successfully");		
        }
    }
    
    return true;	
}
```

**组件加载机制**：
```cpp
class CServerEngine {
private:
    // 动态库句柄
    HMODULE m_hSubscribeCenterLib;     // 订阅中心库
    HMODULE m_hDataHandlerLib;         // 数据处理库
    HMODULE m_hServerMgrLib;           // 服务器管理库
    HMODULE m_hClientMgrLib;           // 客户端管理库
    HMODULE m_hOnlineMgrLib;           // 在线管理库
    
    // 组件接口指针
    ISubscribeCenter* m_pSubscribeCenter;
    IDataHandler* m_pDataHandler;
    IServerManager* m_pServerMgr;
    IClientManager* m_pClientMgr;
    IOnlineManager* m_pOnlineMgr;
    
public:
    // 加载组件
    int LoadComponent(const string& libName, const string& createFuncName) {
        HMODULE hLib = LoadLibrary(libName.c_str());
        if (hLib == NULL) {
            WriteLog("加载动态库失败: %s", libName.c_str());
            return -1;
        }
        
        // 获取创建函数
        typedef void* (*CreateFunc)();
        CreateFunc createFunc = (CreateFunc)GetProcAddress(hLib, createFuncName.c_str());
        if (createFunc == NULL) {
            WriteLog("获取创建函数失败: %s", createFuncName.c_str());
            FreeLibrary(hLib);
            return -1;
        }
        
        // 创建组件实例
        void* pComponent = createFunc();
        if (pComponent == NULL) {
            WriteLog("创建组件实例失败");
            FreeLibrary(hLib);
            return -1;
        }
        
        return 0;
    }
};
```

### 2. ZcsFrontMgr - 前置管理器

**功能**：作为独立的进程管理中心，负责管理和监控各个子站的前置通信进程

**核心特性**：
- 可作为Windows服务或Linux守护进程运行
- 管理多个ZcsFrontSrv进程实例
- 进程生命周期监控和自动重启
- 配置动态加载和热更新

**进程管理架构**：
```cpp
class CFrontProcessManager {
private:
    struct PROCESS_INFO {
        string processName;      // 进程名称
        string stationId;       // 对应厂站ID
        PROCESS_HANDLE handle;  // 进程句柄
        DWORD processId;        // 进程ID
        time_t startTime;       // 启动时间
        int restartCount;       // 重启次数
        PROCESS_STATUS status;  // 进程状态
    };
    
    map<string, PROCESS_INFO> m_processMap;
    HANDLE m_monitorThread;
    
public:
    // 启动厂站前置进程
    int StartStationProcess(const string& stationId) {
        STATION_CONFIG config = GetStationConfig(stationId);
        
        // 构造进程启动参数
        string cmdLine = BuildCommandLine(config);
        
        // 启动进程
        PROCESS_INFORMATION pi;
        STARTUPINFO si = {0};
        si.cb = sizeof(si);
        
        if (CreateProcess(NULL, (LPSTR)cmdLine.c_str(), NULL, NULL, 
                         FALSE, 0, NULL, NULL, &si, &pi)) {
            
            // 记录进程信息
            PROCESS_INFO processInfo;
            processInfo.processName = "ZcsFrontSrv_" + stationId;
            processInfo.stationId = stationId;
            processInfo.handle = pi.hProcess;
            processInfo.processId = pi.dwProcessId;
            processInfo.startTime = time(NULL);
            processInfo.restartCount = 0;
            processInfo.status = PROCESS_RUNNING;
            
            m_processMap[stationId] = processInfo;
            
            return 0;
        }
        
        return -1;
    }
    
    // 进程监控线程
    static DWORD WINAPI MonitorThreadProc(LPVOID param) {
        CFrontProcessManager* pThis = (CFrontProcessManager*)param;
        
        while (pThis->m_running) {
            for (auto& pair : pThis->m_processMap) {
                PROCESS_INFO& processInfo = pair.second;
                
                // 检查进程是否还在运行
                if (processInfo.handle != NULL) {
                    DWORD exitCode;
                    GetExitCodeProcess(processInfo.handle, &exitCode);
                    
                    if (exitCode != STILL_ACTIVE) {
                        // 进程已退出，记录日志
                        pThis->WriteLog("进程 %s 异常退出，准备重启", 
                                       processInfo.processName.c_str());
                        
                        // 关闭句柄
                        CloseHandle(processInfo.handle);
                        processInfo.handle = NULL;
                        processInfo.status = PROCESS_STOPPED;
                        
                        // 自动重启（如果重启次数未超限）
                        if (processInfo.restartCount < MAX_RESTART_COUNT) {
                            pThis->RestartStationProcess(processInfo.stationId);
                            processInfo.restartCount++;
                        }
                    }
                }
            }
            
            Sleep(MONITOR_INTERVAL);
        }
        
        return 0;
    }
};
```

### 3. ZcsFrontSrv - 前置服务器

**功能**：与具体子站设备进行直接通信的核心前置通信进程

**特点**：
- 每个子站对应一个独立的进程实例
- 支持多种通信协议(104, 103, 61850等)
- 具备断线重连和故障恢复能力
- 支持协议库动态加载

**通信流程示例(以IEC 104为例)**：
```
子站设备 (IEC 104)
    ↓ TCP Socket (端口102)
push::create_listensock() 
    ↓ 监听连接
push::select_loop()
    ↓ 接收数据
push::rsock() 
    ↓ RFC1006解析
push::filterTpkt()
    ↓ TPDU处理
vMsg队列
    ↓ 消息转换
CXJ104APCIHandler::OnReceiveData()
    ↓ APCI层处理
CHuNTc103ASDUHandler
    ↓ ASDU层处理
STTP消息生成
    ↓ 消息总线
_sendSttp2Msgbus()
    ↓ 动态库接口
ZxBusSwapLib
    ↓ 跨进程通信
ZcsMain (消息总线中心)
```

**协议处理架构**：
```cpp
class CProtocolHandler {
private:
    IProtocolProcessor* m_pProcessor;  // 协议处理器
    CMessageQueue m_sendQueue;         // 发送队列
    CMessageQueue m_recvQueue;         // 接收队列
    HANDLE m_processThread;            // 处理线程
    
public:
    // 初始化协议处理器
    int Initialize(const PROTOCOL_CONFIG& config) {
        // 根据协议类型加载相应的处理器
        switch (config.protocolType) {
            case PROTOCOL_IEC104:
                m_pProcessor = new C104Processor();
                break;
            case PROTOCOL_IEC103:
                m_pProcessor = new C103Processor();
                break;
            case PROTOCOL_IEC61850:
                m_pProcessor = new C61850Processor();
                break;
        }
        
        // 初始化协议处理器
        return m_pProcessor->Initialize(config);
    }
    
    // 数据接收处理
    void OnDataReceived(const char* data, int length) {
        // 将数据放入接收队列
        MESSAGE_ITEM item;
        item.data = new char[length];
        memcpy(item.data, data, length);
        item.length = length;
        item.timestamp = GetCurrentTime();
        
        m_recvQueue.Push(item);
    }
    
    // 消息处理线程
    static DWORD WINAPI ProcessThreadProc(LPVOID param) {
        CProtocolHandler* pThis = (CProtocolHandler*)param;
        
        while (pThis->m_running) {
            // 处理接收队列
            MESSAGE_ITEM recvItem;
            if (pThis->m_recvQueue.Pop(recvItem, 100)) { // 100ms超时
                // 协议解析和处理
                pThis->m_pProcessor->ProcessReceivedData(recvItem.data, recvItem.length);
                delete[] recvItem.data;
            }
            
            // 处理发送队列
            MESSAGE_ITEM sendItem;
            if (pThis->m_sendQueue.Pop(sendItem, 10)) { // 10ms超时
                // 发送数据到设备
                pThis->SendToDevice(sendItem.data, sendItem.length);
                delete[] sendItem.data;
            }
        }
        
        return 0;
    }
};
```

### 4. ZcsCliMngr - 客户端管理器

**功能**：会话池管理器，维护所有客户端会话的生命周期

**核心功能**：
- 会话池管理：维护所有客户端会话
- 协议库加载：动态加载不同通信协议库
- 数据库交互：读取站点配置信息
- 命令处理：处理来自上层的控制命令
- 在线管理：配合服务器在线管理器实现主备切换

**会话管理架构**：
```cpp
class CClientSessionManager {
private:
    struct SESSION_INFO {
        string sessionId;           // 会话ID
        string stationId;          // 厂站ID
        SOCKET clientSocket;       // 客户端套接字
        time_t lastActivity;       // 最后活动时间
        SESSION_STATUS status;     // 会话状态
        IProtocolHandler* pHandler; // 协议处理器
    };
    
    map<string, SESSION_INFO> m_sessionMap;
    HANDLE m_sessionThread;
    CSessionPool m_sessionPool;
    
public:
    // 创建客户端会话
    string CreateClientSession(const string& stationId, const CLIENT_CONFIG& config) {
        // 生成会话ID
        string sessionId = GenerateSessionId();
        
        // 从会话池获取会话对象
        SESSION_INFO sessionInfo;
        sessionInfo.sessionId = sessionId;
        sessionInfo.stationId = stationId;
        sessionInfo.lastActivity = time(NULL);
        sessionInfo.status = SESSION_CONNECTING;
        
        // 创建协议处理器
        sessionInfo.pHandler = CreateProtocolHandler(config.protocolType);
        
        // 建立到设备的连接
        if (ConnectToDevice(sessionInfo, config) == 0) {
            sessionInfo.status = SESSION_CONNECTED;
            m_sessionMap[sessionId] = sessionInfo;
            
            WriteLog("成功创建到厂站 %s 的会话 %s", stationId.c_str(), sessionId.c_str());
            return sessionId;
        }
        
        // 连接失败，清理资源
        CleanupSession(sessionInfo);
        return "";
    }
    
    // 会话监控线程
    static DWORD WINAPI SessionMonitorProc(LPVOID param) {
        CClientSessionManager* pThis = (CClientSessionManager*)param;
        
        while (pThis->m_running) {
            time_t currentTime = time(NULL);
            
            for (auto it = pThis->m_sessionMap.begin(); it != pThis->m_sessionMap.end();) {
                SESSION_INFO& session = it->second;
                
                // 检查会话超时
                if (currentTime - session.lastActivity > SESSION_TIMEOUT) {
                    pThis->WriteLog("会话 %s 超时，准备清理", session.sessionId.c_str());
                    pThis->CleanupSession(session);
                    it = pThis->m_sessionMap.erase(it);
                }
                else {
                    // 发送心跳保活
                    pThis->SendKeepAlive(session);
                    ++it;
                }
            }
            
            Sleep(SESSION_CHECK_INTERVAL);
        }
        
        return 0;
    }
};
```

### 5. ZcsDataHdl - 数据处理中心

**功能**：作为数据中枢，承担数据流转、消息分发和持久化存储的关键职责

**核心能力**：
- 接收和分类处理来自各子站的STTP协议消息
- 数据验证、转换和格式化
- 实时数据和历史数据的存储
- 数据分发到各个订阅者
- 数据质量检查和异常处理

**数据处理流水线**：
```cpp
class CDataProcessor {
private:
    CMessageQueue m_inputQueue;      // 输入队列
    CMessageQueue m_outputQueue;     // 输出队列
    CDataValidator m_validator;      // 数据校验器
    CDataConverter m_converter;      // 数据转换器
    CDataStorage m_storage;         // 数据存储器
    vector<HANDLE> m_workerThreads; // 工作线程池
    
public:
    // 数据处理工作线程
    static DWORD WINAPI WorkerThreadProc(LPVOID param) {
        CDataProcessor* pThis = (CDataProcessor*)param;
        
        while (pThis->m_running) {
            STTP_MESSAGE message;
            if (pThis->m_inputQueue.Pop(message, 100)) {
                // 1. 数据校验
                if (!pThis->m_validator.Validate(message)) {
                    pThis->WriteLog("数据校验失败: MsgId=%d", message.head.msgid);
                    continue;
                }
                
                // 2. 数据转换
                PROCESSED_DATA processedData;
                if (pThis->m_converter.Convert(message, processedData) != 0) {
                    pThis->WriteLog("数据转换失败: MsgId=%d", message.head.msgid);
                    continue;
                }
                
                // 3. 数据存储
                pThis->m_storage.Store(processedData);
                
                // 4. 数据分发
                pThis->DistributeData(processedData);
                
                // 5. 放入输出队列供进一步处理
                pThis->m_outputQueue.Push(processedData);
            }
        }
        
        return 0;
    }
    
    // 数据分发
    void DistributeData(const PROCESSED_DATA& data) {
        // 根据数据类型分发到不同的处理器
        switch (data.dataType) {
            case DATA_TYPE_REALTIME:
                DistributeToRealtimeSubscribers(data);
                break;
            case DATA_TYPE_ALARM:
                DistributeToAlarmSubscribers(data);
                break;
            case DATA_TYPE_EVENT:
                DistributeToEventSubscribers(data);
                break;
            case DATA_TYPE_HISTORY:
                DistributeToHistorySubscribers(data);
                break;
        }
    }
};
```

### 6. ZcsSsbMngr - 订阅中心管理器

**功能**：实现经典的发布者-观察者模式，作为动态链接库提供消息分发中心服务

**架构特点**：
- 支持主题订阅和取消订阅
- 消息路由和过滤
- 多线程并发分发
- 订阅者状态管理

**订阅中心实现**：
```cpp
class CSubscribeCenter {
private:
    struct SUBSCRIBER_INFO {
        string subscriberId;         // 订阅者ID
        vector<string> topics;      // 订阅主题列表
        CALLBACK_FUNC callback;     // 回调函数
        void* userData;             // 用户数据
        time_t lastActivity;       // 最后活动时间
    };
    
    map<string, SUBSCRIBER_INFO> m_subscribers;        // 订阅者映射
    map<string, vector<string>> m_topicSubscribers;   // 主题-订阅者映射
    CMessageQueue m_messageQueue;                     // 消息队列
    HANDLE m_dispatchThread;                         // 分发线程
    
public:
    // 订阅主题
    int Subscribe(const string& subscriberId, const vector<string>& topics, 
                  CALLBACK_FUNC callback, void* userData) {
        
        // 创建或更新订阅者信息
        SUBSCRIBER_INFO& subscriber = m_subscribers[subscriberId];
        subscriber.subscriberId = subscriberId;
        subscriber.topics = topics;
        subscriber.callback = callback;
        subscriber.userData = userData;
        subscriber.lastActivity = time(NULL);
        
        // 更新主题-订阅者映射
        for (const auto& topic : topics) {
            auto& subscribers = m_topicSubscribers[topic];
            if (find(subscribers.begin(), subscribers.end(), subscriberId) == subscribers.end()) {
                subscribers.push_back(subscriberId);
            }
        }
        
        WriteLog("订阅者 %s 订阅了 %d 个主题", subscriberId.c_str(), topics.size());
        return 0;
    }
    
    // 发布消息
    int PublishMessage(const string& topic, const MESSAGE_DATA& messageData) {
        // 查找该主题的订阅者
        auto it = m_topicSubscribers.find(topic);
        if (it == m_topicSubscribers.end()) {
            return 0; // 没有订阅者
        }
        
        // 创建分发消息
        DISPATCH_MESSAGE dispatchMsg;
        dispatchMsg.topic = topic;
        dispatchMsg.data = messageData;
        dispatchMsg.subscribers = it->second;
        
        // 放入分发队列
        m_messageQueue.Push(dispatchMsg);
        return 0;
    }
    
    // 消息分发线程
    static DWORD WINAPI DispatchThreadProc(LPVOID param) {
        CSubscribeCenter* pThis = (CSubscribeCenter*)param;
        
        while (pThis->m_running) {
            DISPATCH_MESSAGE dispatchMsg;
            if (pThis->m_messageQueue.Pop(dispatchMsg, 100)) {
                
                // 分发给所有订阅者
                for (const auto& subscriberId : dispatchMsg.subscribers) {
                    auto subscriberIt = pThis->m_subscribers.find(subscriberId);
                    if (subscriberIt != pThis->m_subscribers.end()) {
                        SUBSCRIBER_INFO& subscriber = subscriberIt->second;
                        
                        try {
                            // 调用订阅者回调
                            subscriber.callback(dispatchMsg.topic.c_str(), 
                                              &dispatchMsg.data, 
                                              subscriber.userData);
                            subscriber.lastActivity = time(NULL);
                        }
                        catch (...) {
                            pThis->WriteLog("订阅者 %s 回调异常", subscriberId.c_str());
                        }
                    }
                }
            }
        }
        
        return 0;
    }
};
```

## 系统配置和部署

### 1. 配置文件结构
```ini
[SERVER_CONFIG]
server_id=SERVER001              # 服务器ID
server_role=1                    # 服务器角色: 1-主服务器, 2-备份服务器
log_level=3                      # 日志级别
log_path=./logs                  # 日志路径

[DATABASE_CONFIG]  
db_server=192.168.1.100         # 数据库服务器
db_name=scada_db                # 数据库名
connection_pool_size=20         # 连接池大小

[COMMUNICATION_CONFIG]
listen_port=8001                # 监听端口
max_connections=1000            # 最大连接数
timeout=30                      # 连接超时时间

[STATION_CONFIG]
station_config_table=tb_station # 厂站配置表
protocol_config_table=tb_protocol # 协议配置表
```

### 2. 启动顺序和依赖关系
```
1. ZcsMain 主程序启动
   ↓
2. 初始化配置和日志系统
   ↓  
3. StartSubscribeCenter() - 启动消息总线
   ↓
4. StartDataHandler() - 启动数据处理中心
   ↓
5. StartServerMgr() - 启动服务器管理器
   ↓
6. StartClientMgr() - 启动客户端管理器 (可选)
   ↓
7. StartSrvOnlineManager() - 启动在线管理器
   ↓
8. ZcsFrontMgr 启动各厂站前置进程
   ↓
9. 各 ZcsFrontSrv 进程连接对应设备
   ↓
10. 系统进入正常运行状态
```

## 异常处理和容错机制

### 1. 组件故障处理
```cpp
class CComponentFailureHandler {
public:
    void HandleComponentFailure(COMPONENT_TYPE componentType, const string& error) {
        switch (componentType) {
            case COMPONENT_SUBSCRIBE_CENTER:
                // 订阅中心故障 - 尝试重启
                RestartSubscribeCenter();
                break;
                
            case COMPONENT_DATA_HANDLER:
                // 数据处理中心故障 - 切换到备份处理器
                SwitchToBackupDataHandler();
                break;
                
            case COMPONENT_FRONT_PROCESS:
                // 前置进程故障 - 自动重启
                RestartFrontProcess();
                break;
        }
    }
};
```

### 2. 数据一致性保障
```cpp
class CDataConsistencyManager {
public:
    // 数据校验和修复
    void ValidateAndRepairData() {
        // 1. 检查数据完整性
        if (!CheckDataIntegrity()) {
            // 2. 从备份恢复数据
            RestoreFromBackup();
        }
        
        // 3. 验证数据一致性
        if (!CheckDataConsistency()) {
            // 4. 数据同步和修复
            SynchronizeData();
        }
    }
};
```

这个通信服务器核心是整个Dragon系统的中枢，负责协调各个组件的协作，确保电力系统通信的可靠性和实时性。
