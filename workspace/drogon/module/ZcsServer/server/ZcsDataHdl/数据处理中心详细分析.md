# ZcsDataHdl - 数据处理中心详细分析

## 模块概述
- **模块名称**: ZcsDataHdl (数据处理中心)
- **主要功能**: 集中处理所有STTP数据，包括数据校验、存储、比较和分发
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为通信服务器的数据处理核心，负责数据的统一处理和存储

## 文件结构

```
ZcsDataHdl/
├── ZcsDataHdl.h/cpp             # 模块接口定义和实现
├── DataHandler.h/cpp            # 数据处理器核心类
├── ZcsDataHdlPublisher.h/cpp    # 数据发布者类
├── resource.h                   # 资源定义
└── ZcsDataHdl_update_history.cpp # 更新历史
```

## 核心类分析

### 1. CDataHandler - 数据处理器核心类

#### 类定义
```cpp
class CDataHandler {
private:
    // 状态控制
    bool m_bExit;                                    // 退出标志
    bool m_bLogMsg;                                  // 是否记录报文
    
    // 发布者对象
    CXJDataHandlePublisher* m_pPublisher;            // 数据发布者
    
    // 线程管理
    THREAD_ID m_hSaveThreadId;                       // 保存线程ID
    THREAD_HANDLE m_hSaveThreadHandle;               // 保存线程句柄
    THREAD_ID m_hAutoActThreadId;                    // 自动动作线程ID
    THREAD_HANDLE m_hAutoActThreadHandle;            // 自动动作线程句柄
    THREAD_ID m_hCommuMsgThreadId;                   // 通信消息线程ID
    THREAD_HANDLE m_hCommuMsgThreadHandle;           // 通信消息线程句柄
    THREAD_ID m_hOtherMsgThreadId;                   // 其他消息线程ID
    THREAD_HANDLE m_hOtherMsgThreadHandle;           // 其他消息线程句柄
    
    // 配置参数
    int m_nLogLevel;                                 // 日志级别
    int m_nLogDay;                                   // 日志保存天数
    int m_nServerRole;                               // 服务器角色
    char m_szLogPath[FILE_NAME_MAX_LEN];             // 日志路径
    
    // 日志对象
    CMessageLog m_LogFile;                           // 日志文件
    
    // 数据队列及锁
    STTP_MSG_BUFFER m_queueSaveMsg;                  // 待存数据队列
    CXJLock m_LockOfSave;                           // 保存队列锁
    STTP_MSG_BUFFER m_queueSaveRltMsg;               // 结果消息队列
    CXJLock m_LockOfRltMsg;                         // 结果消息锁
    STTP_MSG_BUFFER m_queueSaveAutoActMsg;           // 自动动作队列
    CXJLock m_LockOfAutoActMsg;                      // 自动动作锁
    STTP_MSG_BUFFER m_queueSaveAutoDiMsg;            // 自动诊断队列
    CXJLock m_LockOfAutoDiMsg;                       // 自动诊断锁
    STTP_MSG_BUFFER m_queueSaveCommuMsg;             // 通信消息队列
    CXJLock m_LockOfCommuMsg;                        // 通信消息锁
    STTP_MSG_BUFFER m_queueSaveOtherMsg;             // 其他消息队列
    CXJLock m_LockOfOtherMsg;                        // 其他消息锁
    
    // 总线和配置
    BUS_TYPE m_eBusType;                             // 总线类型
    bool m_bFrontSrv;                               // 是否前置服务
    CWaveCfgBindDevice m_CFGModifierWrapper;         // CFG文件修改器
    bool m_bModifyCFGFile;                          // 是否修改CFG文件
    CXJDBFacade* m_pDBFacade;                       // 数据库接口
    bool m_bSendMsgToOther;                         // 是否转发消息
    bool m_bSetDevOffByStnOff;                      // 厂站断开时设备是否断开
};
```

#### 常量定义
```cpp
const int MAX_SETTING_WAIT_TIME = 90;      // 定值比较等待时间(秒)
const int MAX_COMPARE_QUEUE_SIZE = 5000;   // 比较队列最大容量
const int MAX_SAVE_QUEUE_SIZE = 20000;     // 保存队列最大容量
const int MAX_VIMESSAGE_QUEUE_SIZE = 200;  // 重要信息队列容量

// 比较规则
const int RULER_NO_CMP = 0;                // 不比较
const int RULER_CMP_LAST = 1;              // 与前一次比较
const int RULER_CMP_STANDARD = 2;          // 与基准值比较
```

#### 数据结构

##### SETTING_COMPARE - 定值比较结构
```cpp
typedef struct _SETTING_COMPARE {
    STTP_FULL_DATA* data;      // 定值数据
    int iMsgCount;             // 报文帧数
    bool bFull;                // 是否收全
    time_t nSaveTime;          // 保存时间
} SETTING_COMPARE;
```

### 2. 主要功能实现

#### 启动流程
```cpp
int Start(const STRUCT_XJDATAHANDLER& pDataHandler) {
    // 1. 初始化日志
    InitLogFile();
    
    // 2. 初始化数据库访问
    InitDBFacade();
    
    // 3. 初始化发布者
    InitPublisher();
    
    // 4. 初始化CFG修改器
    InitCFGModify();
    
    // 5. 启动各处理线程
    StartSaveDataThread();     // 数据保存线程
    StartAutoActThread();      // 自动动作线程
    StartCommuMsgThread();     // 通信消息线程
    StartOtherMsgThread();     // 其他消息线程
    
    return 0;
}
```

#### 数据插入接口
```cpp
void InsertData(const STTPMSG& pMsg, bool bCompare = true) {
    // 数据分发到不同队列
    _SttpDistribution(pMsg);
}

void _SttpDistribution(const STTPMSG& pMsg) {
    // 根据消息类型分发到不同队列
    switch (pMsg.msgID) {
        // 结果类消息
        case 0x00901:  // 结果确认通知
        case 0x50053:  // 定值修改执行响应
            _ResultList(pMsg);
            break;
            
        // 自动动作类消息
        case 0x50030:  // 保护动作信息
        case 0x50034:  // 录波简报
            _AutoActList(pMsg);
            break;
            
        // 通信状态类消息
        case 0x50002:  // 保护通讯状态
        case 0x50144:  // 厂站通讯状态
            _CommuList(pMsg);
            break;
            
        // 其他类型消息
        default:
            _OtherList(pMsg);
            break;
    }
}
```

#### 数据保存线程
```cpp
int MainSaveLoop() {
    while (!m_bExit) {
        STTPMSG msg;
        
        // 从队列取数据
        {
            CCsLocker locker(&m_LockOfSave);
            if (m_queueSaveMsg.empty()) {
                OS_Sleep(10);
                continue;
            }
            msg = m_queueSaveMsg.front();
            m_queueSaveMsg.pop_front();
        }
        
        // 保存数据
        SaveSttpData(msg);
    }
    return 0;
}

bool SaveSttpData(STTPMSG& msg) {
    // 1. 解析STTP消息
    STTP_FULL_DATA* pFullData = ParseSttpMessage(msg);
    
    // 2. 数据校验
    if (!ValidateData(pFullData)) {
        return false;
    }
    
    // 3. 保存到数据库
    bool bRet = SaveToDatabase(pFullData);
    
    // 4. 发布自动上送
    if (bRet) {
        PublishAutoUpMessage(msg, msg.msgID);
    }
    
    // 5. 清理资源
    delete pFullData;
    
    return bRet;
}
```

#### 自动动作处理线程
```cpp
int AutoActLoop() {
    while (!m_bExit) {
        STTPMSG msg;
        
        // 从自动动作队列取数据
        {
            CCsLocker locker(&m_LockOfAutoActMsg);
            if (m_queueSaveAutoActMsg.empty()) {
                OS_Sleep(10);
                continue;
            }
            msg = m_queueSaveAutoActMsg.front();
            m_queueSaveAutoActMsg.pop_front();
        }
        
        // 处理自动动作
        ProcessAutoAction(msg);
        
        // 保存到数据库
        SaveSttpData(msg);
        
        // 立即发布（重要信息）
        PublishAutoUpMessage(msg, msg.msgID);
    }
    return 0;
}
```

#### 通信消息处理线程
```cpp
int CommuMsgLoop() {
    while (!m_bExit) {
        STTPMSG msg;
        
        // 从通信消息队列取数据
        {
            CCsLocker locker(&m_LockOfCommuMsg);
            if (m_queueSaveCommuMsg.empty()) {
                OS_Sleep(10);
                continue;
            }
            msg = m_queueSaveCommuMsg.front();
            m_queueSaveCommuMsg.pop_front();
        }
        
        // 处理通信状态变化
        if (msg.msgID == 0x50144) {  // 厂站通信状态
            // 如果厂站断开，生成设备断开通知
            if (m_bSetDevOffByStnOff) {
                MakeDevOfflineNoticeByStnOffline(stationId);
            }
        }
        
        // 保存并发布
        SaveSttpData(msg);
        PublishAutoUpMessage(msg, msg.msgID);
    }
    return 0;
}
```

### 3. 特殊功能实现

#### 录波文件CFG修改
```cpp
void WaveCfgModify(const STTPMSG& pMsg) {
    if (!m_bModifyCFGFile) {
        return;
    }
    
    // 仅处理录波文件下载成功通知
    if (pMsg.msgID != 0x50043) {
        return;
    }
    
    // 获取CFG文件路径
    string cfgPath = GetComtradePath() + fileName + ".cfg";
    
    // 修改CFG文件中的厂站和设备信息
    m_CFGModifierWrapper.ModifyCFGFile(cfgPath, stationId, deviceId);
}
```

#### 厂站断开时设备状态处理
```cpp
int MakeDevOfflineNoticeByStnOffline(const char* cStnId) {
    CMemSet deviceList;
    
    // 1. 获取厂站下所有设备
    if (!GetDeviceListByStation(deviceList, cStnId)) {
        return -1;
    }
    
    // 2. 为每个设备生成断开通知
    for (int i = 0; i < deviceList.GetRecordCount(); i++) {
        // 构造设备断开消息
        STTPMSG offlineMsg;
        FillDeviceOfflineMsg(offlineMsg, deviceId);
        
        // 插入处理队列
        InsertData(offlineMsg, false);
    }
    
    return 0;
}
```

### 4. 数据发布机制

#### 发布自动上送消息
```cpp
void PublishAutoUpMessage(STTPMSG& pMsg, UINT uMsgId) {
    STTP_PUBLISH_MSG publishMsg;
    
    // 设置观察者ID
    strcpy(publishMsg.observerId, "DataHandler");
    
    // 复制消息内容
    memcpy(&publishMsg.msg, &pMsg, sizeof(STTPMSG));
    
    // 发布到订阅中心
    m_pPublisher->PostAutoUploadMsg(publishMsg);
}
```

## 数据流程图

```
输入数据(InsertData)
    |
    v
数据分发(_SttpDistribution)
    |
    +----> 结果队列 -----> 普通处理
    |
    +----> 动作队列 -----> 优先处理
    |
    +----> 通信队列 -----> 状态处理
    |
    +----> 其他队列 -----> 延迟处理
    |
    v
数据保存(SaveSttpData)
    |
    v
自动发布(PublishAutoUpMessage)
```

## 线程模型

系统采用多线程并行处理模型：

1. **主线程**: 接收数据，分发到各队列
2. **保存线程**: 处理普通数据保存
3. **动作线程**: 优先处理动作事件
4. **通信线程**: 处理通信状态
5. **其他线程**: 处理低优先级数据

## 队列管理策略

### 1. 队列大小限制
- 普通队列: 20000条
- 重要信息: 200条
- 比较队列: 5000条

### 2. 溢出处理
- 记录告警日志
- 丢弃最旧数据
- 保留重要信息

### 3. 优先级机制
- 动作事件最高优先级
- 通信状态次优先级
- 普通数据正常优先级
- 其他数据最低优先级

## 数据比较机制

### 定值比较流程
1. 收集同装置同CPU的所有定值
2. 等待最长90秒确保收全
3. 与历史值或基准值比较
4. 仅保存变化的数据

### 比较规则
- **RULER_NO_CMP**: 不比较，直接保存
- **RULER_CMP_LAST**: 与最近一次比较
- **RULER_CMP_STANDARD**: 与基准值比较

## 性能优化

### 1. 多线程并行
- 数据接收与处理分离
- 不同类型数据并行处理
- 减少锁竞争

### 2. 批量处理
- 队列批量取数
- 数据库批量写入
- 减少IO操作

### 3. 内存管理
- 对象池技术
- 智能指针使用
- 及时释放资源

## 配置项说明

### 必需配置
- 日志路径和级别
- 服务器角色
- 数据库连接

### 可选配置
- 是否记录报文
- 是否修改CFG文件
- 是否转发消息到其他区
- 厂站断开时设备处理策略

## 注意事项

1. **队列管理**: 监控队列大小，避免内存溢出
2. **线程同步**: 正确使用锁，避免死锁
3. **数据完整性**: 确保定值数据收全再比较
4. **性能监控**: 关注处理延迟和吞吐量
5. **异常处理**: 完善的错误处理和恢复机制
