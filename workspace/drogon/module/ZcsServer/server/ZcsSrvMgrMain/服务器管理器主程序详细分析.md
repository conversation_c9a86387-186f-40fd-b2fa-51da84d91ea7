# ZcsSrvMgrMain - 服务器管理器主程序详细分析

## 模块概述
- **模块位置**: module/ZcsServer/server/ZcsSrvMgrMain
- **主要功能**: 服务器管理器的独立运行版本，提供完整的服务器管理功能
- **设计目的**: 作为独立进程运行的服务器管理器，支持103和STTP协议服务

## 核心文件分析

### 1. main.cpp - 程序入口

#### 全局变量
```cpp
CMessageLog g_logfile;           // 全局日志文件
bool g_bExit = false;           // 主程序退出标志
CZcsSrvMngr g_ZasFltSceKeeper;  // 服务器管理器主框架对象
```

#### 信号处理
```cpp
// 正常退出信号处理
void sigroutine(int nsig) {
    g_logfile.FormatAdd(CLogFile::trace, 
        "the process of PID=%d recv a signal of value=%d", 
        getpid(), nsig);
    g_bExit = true;
    g_logfile.Add("the Exit Flag is Setted, begine exit");
}

// 段错误信号处理
void dump(int nsig) {
    char buf[1024] = "";
    char cmd[1024] = "";
    
    // 获取进程命令行
    sprintf(buf, "/proc/%d/cmdline", getpid());
    FILE* fh = fopen(buf, "r");
    if (fh && fgets(buf, sizeof(buf), fh)) {
        fclose(fh);
        // 调用gdb调试
        sprintf(cmd, "gdb %s %d", buf, getpid());
        system(cmd);
    }
    g_bExit = true;
}

// 信号注册
void Signale_Handle() {
    signal(SIGINT, sigroutine);
    signal(SIGTERM, sigroutine);
    
#ifdef OS_LINUX
    signal(SIGQUIT, sigroutine);
    signal(SIGPIPE, SIG_IGN);  // 忽略管道破裂信号
    signal(SIGHUP, SIG_IGN);   // 忽略终端挂起
    signal(SIGFPE, SIG_IGN);   // 忽略浮点异常
#endif
}
```

#### 主函数
```cpp
int main(int argc, char* argv[]) {
    // 1. 信号处理注册
    Signale_Handle();
    
    // 2. 初始化日志
    g_logfile.SetLogPath("./log/ZcsSrvMgrMain/");
    g_logfile.SetLogLevel(CLogFile::trace);
    g_logfile.Open("ZcsSrvMgrMain");
    
    // 3. 初始化服务器管理器
    if (g_ZasFltSceKeeper.Init(&g_logfile, &g_bExit) != 0) {
        g_logfile.Add("Init ZcsSrvMngr failed!", CLogFile::error);
        return -1;
    }
    
    // 4. 启动服务器管理器
    if (g_ZasFltSceKeeper.Start() != 0) {
        g_logfile.Add("Start ZcsSrvMngr failed!", CLogFile::error);
        return -1;
    }
    
    // 5. 主循环
    while (!g_bExit) {
        OS_Sleep(1000);
    }
    
    // 6. 停止并清理
    g_ZasFltSceKeeper.Stop();
    g_ZasFltSceKeeper.End();
    
    return 0;
}
```

### 2. ZcsSrvMngr.h/cpp - 服务器管理器主类

#### 类定义
```cpp
class CZcsSrvMngr {
private:
    CMessageLog* m_pLogFile;           // 日志对象
    bool* m_pbExit;                    // 退出标志
    
    CServerManager* m_pServerManager;   // 服务器管理器
    CSttpBusItf* m_pSttpBusItf;        // STTP总线接口
    CDBOper* m_pDBOper;                // 数据库操作
    
    // 配置参数
    int m_nListenPort103;              // 103监听端口
    int m_nListenPortSttp;             // STTP监听端口
    string m_strStationID;             // 厂站ID
    
public:
    // 生命周期管理
    int Init(CMessageLog* pLogFile, bool* pbExit);
    int Start();
    int Stop();
    int End();
    
    // 配置管理
    int ReadConfig();
    int LoadConfigFromINI();
    int LoadConfigFromDB();
    
    // 服务管理
    int InitServerManager();
    int InitSttpBusInterface();
    int InitDatabaseConnection();
};
```

#### 初始化流程
```cpp
int CZcsSrvMngr::Init(CMessageLog* pLogFile, bool* pbExit) {
    m_pLogFile = pLogFile;
    m_pbExit = pbExit;
    
    // 1. 读取配置
    if (ReadConfig() != 0) {
        return -1;
    }
    
    // 2. 初始化数据库连接
    m_pDBOper = new CDBOper(m_pLogFile);
    if (m_pDBOper->Init() != 0) {
        return -1;
    }
    
    // 3. 初始化STTP总线接口
    m_pSttpBusItf = new CSttpBusItf();
    if (m_pSttpBusItf->Init(m_pLogFile, "./log/") != 0) {
        return -1;
    }
    
    // 4. 初始化服务器管理器
    m_pServerManager = new CServerManager(m_pLogFile, m_pbExit);
    m_pServerManager->SetDBOper(m_pDBOper);
    m_pServerManager->SetSttpBusItf(m_pSttpBusItf);
    
    return 0;
}
```

### 3. ServerManager.h/cpp - 服务器管理器核心

#### 扩展功能
```cpp
class CServerManager {
private:
    // 线程管理
    THREAD_HANDLE m_h103ListenThread;     // 103监听线程
    THREAD_HANDLE m_hSttpListenThread;    // STTP监听线程
    THREAD_HANDLE m_hCommandThread;       // 命令处理线程
    
    // 会话管理
    map<int, CLIENT_BASE*> m_mapClients;  // 客户端映射
    list<SERVERSESSION*> m_listSessions;  // 会话列表
    
    // 命令缓存
    list<STTP_CMD_CACHE> m_listCmdCache;  // 命令缓存列表
    CXJLock m_lockCmdCache;               // 命令缓存锁
    
public:
    // 监听服务
    int Start103Listen(int nPort);
    int StartSttpListen(int nPort);
    
    // 线程函数
    static THREAD_FUNC Listen103Thread(LPVOID pParam);
    static THREAD_FUNC ListenSttpThread(LPVOID pParam);
    static THREAD_FUNC CommandProcessThread(LPVOID pParam);
    
    // 客户端管理
    int AcceptClient(SOCKET sock, int nProtocolType);
    int RemoveClient(int nClientID);
    CLIENT_BASE* FindClient(int nClientID);
    
    // 命令处理
    int ProcessCommand(STTP_CMD_CACHE& cmd);
    int SendResponse(int nClientID, STTPMSG* pMsg);
    
    // RII管理
    int AllocateRII(const string& strStation);
    void ReleaseRII(int nRII);
};
```

### 4. SttpBusItf.h/cpp - STTP总线接口

#### 类定义
```cpp
class CSttpBusItf {
private:
    CMessageLog* m_pLogFile;              // 日志对象
    
    // 总线交换对象
    CZXLoadBusSwapLib* m_pLoadBusSwapLib; // 总线库加载器
    IZxBusSwap* m_pBusSwap;               // 总线交换接口
    
    // 消息分析器
    CSttpMsgAnalyze* m_pSttpAnalyzer;     // STTP消息分析器
    
    // 接收队列
    LIST_STTP_DATA* m_plSttpRecvList;     // STTP接收队列
    CXJLock* m_pSttpCacheLock;            // 队列锁
    
    // 自动上送队列
    LIST_STTP_DATA* m_plAutoUpRecvList;   // 自动上送队列
    CXJLock* m_pAutoUpCacheLock;          // 队列锁
    
public:
    // 初始化和释放
    int Init(CMessageLog* pLogFile, const char* cLogPath);
    int End();
    
    // 队列设置
    int SetSttpMsgRecvList(LIST_STTP_DATA* plSttpFUllData, 
                          CXJLock* pSttpCacheLock);
    int SetAutoUpSttpMsgRecvList(LIST_STTP_DATA* plSttpFUllData, 
                                 CXJLock* pSttpCacheLock);
    
    // 消息发送
    int SendSttpCmd(STTP_FULL_DATA& SttpData);
    int SendSttpResp(STTP_FULL_DATA& SttpData);
    
    // 回调函数
    static int OnRecvBusLinkStatusChg(LPVOID pRegObj, int nStatus, 
                                      void* pReserved);
    static int OnRecvSttpDataFromBus(LPVOID pRegObj, 
                                     STTP_FULL_DATA& sttp_data,
                                     string& strAppNodeName,
                                     BUS_ADDITION_INFO& addition_info);
    
private:
    // 内部方法
    bool _InitSttpBusSwapObj();
    void _ReadIni();
    int _WriteLog(char* cFunName, char* cLog, int nLevel);
};
```

#### 总线接口实现
```cpp
int CSttpBusItf::Init(CMessageLog* pLogFile, const char* cLogPath) {
    m_pLogFile = pLogFile;
    
    // 1. 读取配置
    _ReadIni();
    
    // 2. 加载总线库
    m_pLoadBusSwapLib = new CZXLoadBusSwapLib(m_pLogFile);
    if (!m_pLoadBusSwapLib->LoadShareLib(STTP_BUSSWAP_TYPE)) {
        return -1;
    }
    
    // 3. 创建总线交换实例
    m_pBusSwap = m_pLoadBusSwapLib->CreateBusIns(m_pLogFile, 
                                                 (char*)cLogPath);
    if (!m_pBusSwap) {
        return -1;
    }
    
    // 4. 初始化应用节点
    APP_NODE_INFO nodeInfo;
    // ... 填充节点信息
    if (m_pBusSwap->InitAppNode(nodeInfo) < 0) {
        return -1;
    }
    
    // 5. 注册回调
    m_pBusSwap->RegRecvSttpMsgCallBak(OnRecvSttpDataFromBus, this);
    m_pBusSwap->RegBusLinkStatusChgCallBak(OnRecvBusLinkStatusChg, this);
    
    // 6. 启动总线交换
    if (m_pBusSwap->StartBusSwap() < 0) {
        return -1;
    }
    
    // 7. 创建STTP分析器
    m_pSttpAnalyzer = new CSttpMsgAnalyze();
    
    return 0;
}
```

### 5. DBOper.h/cpp - 数据库操作

#### 类定义
```cpp
class CDBOper {
private:
    CMessageLog* m_pLogFile;          // 日志对象
    CXJDBFacade* m_pDBFacade;        // 数据库访问接口
    
    // 数据库配置
    string m_strDBType;               // 数据库类型
    string m_strDBHost;               // 数据库主机
    string m_strDBName;               // 数据库名
    string m_strDBUser;               // 用户名
    string m_strDBPass;               // 密码
    
public:
    // 初始化和连接
    int Init();
    int Connect();
    int Disconnect();
    
    // 配置读取
    int ReadServerConfig(SERVER_CONFIG& config);
    int ReadStationConfig(STATION_CONFIG& config);
    int ReadProtocolConfig(PROTOCOL_CONFIG& config);
    
    // 数据操作
    int SaveCommandLog(const COMMAND_LOG& log);
    int UpdateServerStatus(int nServerID, int nStatus);
    int GetClientList(list<CLIENT_INFO>& listClients);
};
```

## 执行流程

### 1. 程序启动流程
```
main()
    ├── Signale_Handle()          // 注册信号处理
    ├── 初始化日志系统
    ├── CZcsSrvMngr::Init()
    │   ├── ReadConfig()          // 读取配置
    │   ├── InitDatabase()        // 初始化数据库
    │   ├── InitSttpBusItf()      // 初始化总线接口
    │   └── InitServerManager()   // 初始化管理器
    ├── CZcsSrvMngr::Start()
    │   ├── Start103Listen()      // 启动103监听
    │   ├── StartSttpListen()     // 启动STTP监听
    │   └── StartCommandProcess() // 启动命令处理
    └── 主循环
        └── OS_Sleep(1000)
```

### 2. 103协议服务流程
```
Listen103Thread()
    ├── socket()                  // 创建套接字
    ├── bind()                    // 绑定端口
    ├── listen()                  // 开始监听
    └── 循环
        ├── accept()              // 接受连接
        ├── CreateClient()        // 创建客户端对象
        ├── AddToClientMap()      // 加入客户端映射
        └── StartClientSession()  // 启动客户端会话
```

### 3. STTP消息处理流程
```
OnRecvSttpDataFromBus()
    ├── 解析STTP消息
    ├── 判断消息类型
    │   ├── 命令消息
    │   │   └── PushToCommandQueue()
    │   ├── 响应消息
    │   │   └── MatchWithCache()
    │   └── 自动上送消息
    │       └── PushToAutoUpQueue()
    └── 触发处理
```

## 关键特性

### 1. 多协议支持
- 同时支持103和STTP协议
- 协议独立处理
- 统一的客户端管理

### 2. 总线集成
- 与D5K/STTP总线集成
- 支持消息订阅发布
- 总线状态监控

### 3. 命令缓存机制
```cpp
struct STTP_CMD_CACHE {
    int nRII;                     // 请求标识
    string strStation;            // 厂站标识
    STTPMSG sttpMsg;             // STTP消息
    time_t tCreateTime;          // 创建时间
    int nRetryCount;             // 重试次数
};
```

### 4. 会话管理
```cpp
struct SERVERSESSION {
    int nSessionID;              // 会话ID
    int nClientID;               // 客户端ID
    int nProtocolType;           // 协议类型
    SOCKET sock;                 // 套接字
    THREAD_HANDLE hThread;       // 处理线程
    bool bActive;                // 活动状态
};
```

## 配置文件

### ZcsSrvMngr.ini
```ini
[Server]
ListenPort103=2404
ListenPortSttp=8080
MaxClients=100
SessionTimeout=60

[Database]
Type=Oracle
Host=*************
Port=1521
Name=SCADA
User=admin
Pass=admin123

[BusInterface]
BusType=STTP
NodeName=SrvMngr
GroupID=1

[Log]
Level=3
SaveDays=30
MaxFileSize=100
```

## 错误处理

### 1. 初始化错误
```cpp
enum InitError {
    INIT_SUCCESS = 0,
    INIT_CONFIG_ERROR = -1,
    INIT_DB_ERROR = -2,
    INIT_BUS_ERROR = -3,
    INIT_SOCKET_ERROR = -4
};
```

### 2. 运行时错误处理
```cpp
// 客户端异常处理
void HandleClientException(int nClientID, int nError) {
    // 记录日志
    m_pLogFile->FormatAdd(CLogFile::error, 
        "Client %d error: %d", nClientID, nError);
    
    // 移除客户端
    RemoveClient(nClientID);
    
    // 清理资源
    CleanupClientResource(nClientID);
}
```

## 性能优化

### 1. 线程池
- 预创建工作线程
- 避免频繁创建销毁
- 任务队列管理

### 2. 连接池
- 数据库连接池
- 套接字复用
- 连接状态管理

### 3. 缓存机制
- 命令缓存
- 配置缓存
- 客户端信息缓存

## 使用注意事项

1. **端口配置**: 确保103和STTP端口不冲突
2. **数据库连接**: 配置正确的数据库参数
3. **总线配置**: 正确配置总线节点信息
4. **日志管理**: 定期清理日志文件
5. **资源监控**: 监控内存和CPU使用
