# ZcsMain - 通信服务器主进程详细分析

## 模块概述
- **模块名称**: ZcsMain
- **主要功能**: 通信服务器的主控制进程，负责启动和管理各个子模块
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为整个通信服务器的入口点和管理中心

## 文件结构

```
ZcsMain/
├── Main.cpp                     # 程序入口点
├── ServerEngine.h               # 服务引擎类定义
├── ServerEngine.cpp             # 服务引擎实现
├── resource.h                   # 资源定义
└── ZcsServer_update_history.cpp # 更新历史
```

## 核心类分析

### CServerEngine - 服务引擎类

#### 类定义
```cpp
class CServerEngine {
public:
    CServerEngine();
    virtual ~CServerEngine();
    bool Start();    // 启动服务
    bool End();      // 停止服务
    
public:
    CLogFile m_LogFile;  // 日志记录对象
    
private:
    // 配置参数
    bool m_bStartClientManager;      // 是否启动客户端管理器
    bool m_bAutoStartClientDll;      // 是否自动启动客户端规约库
    int m_nLogMsg;                   // 是否记录报文
    int m_nLogLevel;                 // 日志级别
    int m_nServerRole;               // 服务器角色 0-主站 1-子站
    int m_nCallall;                  // 是否定时总招
    int m_nCheckTime;                // 是否对时
    int m_nLogDay;                   // 日志记录天数
    char m_szLogPath[FILE_NAME_MAX_LEN];  // 日志路径
    char m_szLocalIP[19];            // 本地IP
    char m_chServerID[41];           // 服务器ID
    int m_nWorkPart;                 // 工作区号(安全分区)
    vector<string> m_vStnList;       // 本进程管辖的厂站列表
    
    // 动态库句柄
    XJHANDLE m_hLibXJDBFacade;       // 数据库访问库
    XJHANDLE m_hLibXJDataHandle;     // 数据处理中心库
    XJHANDLE m_hLibXJSubscribeCenter;// 订阅中心库
    XJHANDLE m_hLibXJClientManager;  // 客户端管理器库
    XJHANDLE m_hLibXJServerManager;  // 服务端管理器库
    
    // 功能函数指针
    PFUNCONNECT m_pFunConnect;
    PFUNSETLOGCONFIG m_pFunSetLogConfig;
    PFUNSELECT m_pFunSelect;
    PFUNSETCONNNUM m_pFunSetConnNum;
    PFUNSetRtdbConnFlag m_pFunSetRtdbConnFlag;
    PFUNSTARTDATAHANDLER m_pFunStartDataHandler;
    PFUNSTOPDATAHANDLER m_pFunStopDataHandler;
    PFUNSTARTSUBSCRIBECENTER m_pFunStartSubscribeCenter;
    PFUNSTOPSUBSCRIBECENTER m_pFunStopSubscribeCenter;
    PFUNSTARTSERVERMGR m_pFunStartServerMgr;
    PFUNSTOPSERVERMGR m_pFunStopServerMgr;
    PFUNSTARTCLIENTMGR m_pFunStartClientMgr;
    PFUNSTOPCLIENTMGR m_pFunStopClientMgr;
    
    // 在线管理器
    CXJSrvOnlineManager m_SrvOnlineManager;
};
```

#### 初始化流程

##### 1. Init() - 主初始化函数
```cpp
bool Init() {
    // 1. 初始化日志系统
    InitLogFile();
    
    // 2. 加载配置文件
    LoadConfigFile();
    
    // 3. 加载本地配置
    LoadLocalConfig();
    
    // 4. 加载动态库
    LoadLib();
    
    // 5. 连接数据库
    ConnectDBFlat();
    
    return true;
}
```

##### 2. LoadLib() - 加载动态库
```cpp
bool LoadLib() {
    // 按顺序加载各个模块的动态库
    LoadDBFacadeLib();         // 数据库访问接口
    LoadDataHandlerLib();      // 数据处理中心
    LoadSubscribeCenterLib();  // 订阅中心
    LoadClientManagerLib();    // 客户端管理器
    LoadServerManagerLib();    // 服务端管理器
}
```

##### 3. 各模块加载示例 - LoadDataHandlerLib()
```cpp
bool LoadDataHandlerLib() {
    // 加载动态库
    m_hLibXJDataHandle = OS_LoadLibrary(LIB_DATAHANDLER_NAME);
    
    // 获取函数地址
    m_pFunStartDataHandler = (PFUNSTARTDATAHANDLER)
        OS_GetProcAddress(m_hLibXJDataHandle, "StartDataHandler");
    
    m_pFunStopDataHandler = (PFUNSTOPDATAHANDLER)
        OS_GetProcAddress(m_hLibXJDataHandle, "StopDataHandler");
    
    return true;
}
```

#### 服务启动流程

##### Start() - 启动服务
```cpp
bool Start() {
    // 1. 初始化
    if (!Init()) {
        return false;
    }
    
    // 2. 启动各个子模块
    StartDataHandler();        // 启动数据处理中心
    StartSubscribeCenter();    // 启动订阅中心
    StartServerMgr();         // 启动服务端管理器
    
    if (m_bStartClientManager) {
        StartClientMgr();     // 启动客户端管理器
    }
    
    StartSrvOnlineManager();  // 启动在线管理器
    
    return true;
}
```

##### 子模块启动示例 - StartDataHandler()
```cpp
int StartDataHandler() {
    STRUCT_XJDATAHANDLER config;
    
    // 设置配置参数
    strcpy(config.szLog_path, m_szLogPath);
    config.iLog_level = m_nLogLevel;
    config.iServerRole = m_nServerRole;
    config.bRecordMsg = (m_nLogMsg == 1);
    config.iLogDay = m_nLogDay;
    
    // 调用动态库函数启动模块
    return m_pFunStartDataHandler(config);
}
```

#### 服务停止流程

##### End() - 停止服务
```cpp
bool End() {
    // 按相反顺序停止各模块
    StopSrvOnlineManager();
    StopClientMgr();
    StopServerMgr();
    StopSubscribeCenter();
    StopDataHandler();
    
    // 释放动态库
    FreeLib();
    
    return true;
}
```

## Main.cpp - 程序入口分析

### 程序运行模式

#### 1. 服务模式运行 (Windows)
```cpp
int APIENTRY WinMain(HINSTANCE hInstance, ...) {
    SERVICE_TABLE_ENTRY ste[] = {
        {servicename, KServiceMain},
        {NULL, NULL}
    };
    
    if (!StartServiceCtrlDispatcher(ste)) {
        // 作为普通程序运行
        hServerEngine.Start();
        while (!bExit) {
            Sleep(1000);
        }
        hServerEngine.End();
    }
}
```

#### 2. 守护进程模式 (Linux)
```cpp
int main(int argc, char* argv[]) {
    // 注册信号处理
    Signale_Handle();
    
    // 启动服务
    hServerEngine.Start();
    
    // 主循环
    while (!bExit) {
        sleep(1);
    }
    
    // 停止服务
    hServerEngine.End();
}
```

### 信号处理

#### Linux信号处理
```cpp
void sigroutine(int nsig) {
    // 记录日志
    hServerEngine.m_LogFile.FormatAdd(CLogFile::trace,
        "the process of PID=%d recv a signal of value=%d",
        getpid(), nsig);
    
    // 设置退出标志
    bExit = true;
}

void Signale_Handle() {
    signal(SIGINT, sigroutine);   // Ctrl+C
    signal(SIGTERM, sigroutine);  // kill命令
    signal(SIGSEGV, dump);        // 段错误
    signal(SIGBUS, dump);         // 总线错误
}
```

#### 段错误处理
```cpp
void dump(int nsig) {
    char cmd[1024];
    
    // 获取进程命令行
    sprintf(cmd, "gdb %s %d", buf, getpid());
    
    // 调用gdb进行调试
    system(cmd);
    
    // 设置退出标志
    bExit = true;
}
```

## 配置文件管理

### LoadConfigFile() - 加载配置
```cpp
void LoadConfigFile() {
    // 从ini文件读取配置
    GetPrivateProfileString("LOCAL", "LOGPATH", "", 
                          m_szLogPath, sizeof(m_szLogPath), 
                          CONFIG_FILE);
    
    m_nLogLevel = GetPrivateProfileInt("LOCAL", "LOGLEVEL", 3, CONFIG_FILE);
    m_nServerRole = GetPrivateProfileInt("LOCAL", "SERVERROLE", 0, CONFIG_FILE);
    m_bStartClientManager = GetPrivateProfileInt("LOCAL", 
                                               "STARTCLIENTMANAGER", 1, 
                                               CONFIG_FILE);
    // ... 其他配置项
}
```

### LoadLocalConfig() - 加载本地配置
```cpp
bool LoadLocalConfig() {
    SQL_DATA sqlData;
    CMemSet memSet;
    
    // 查询服务器配置
    sqlData.sql_str = "SELECT * FROM t_servers WHERE server_id = ?";
    sqlData.sql_par.push_back(m_chServerID);
    
    if (m_pFunSelect(XJ_DBSYS_TABLE_SERVERS, sqlData, error, &memSet)) {
        // 解析查询结果
        // 设置工作区号、厂站列表等
    }
    
    return true;
}
```

## 数据库连接管理

### ConnectDBFlat() - 连接数据库
```cpp
int ConnectDBFlat(char* pError) {
    // 设置日志配置
    m_pFunSetLogConfig(m_szLogPath, m_nLogLevel, m_nLogDay);
    
    // 设置连接数
    _SetDBConnNum();
    
    // 连接数据库
    return m_pFunConnect(m_nWorkPart, pError);
}
```

### _SetDBConnNum() - 设置连接数
```cpp
int _SetDBConnNum() {
    int nConnNum = 10;  // 默认连接数
    
    // 根据模块需求计算连接数
    if (m_bStartClientManager) {
        nConnNum += 5;
    }
    
    // 设置数据库连接数
    m_pFunSetConnNum(nConnNum);
    
    // 设置实时库连接标志
    m_pFunSetRtdbConnFlag(1);
    
    return 0;
}
```

## 安全分区支持

系统支持安全分区部署：
- 0: 不分区部署（所有模块在一个安全区）
- 1: 部署在安全I区
- 2: 部署在安全II区
- 3: 部署在安全III区

## 关键特性

### 1. 模块化设计
- 各功能模块以动态库形式存在
- 支持按需加载和卸载
- 模块间解耦，便于维护

### 2. 配置灵活性
- 支持ini文件配置
- 支持数据库配置
- 运行时可动态调整

### 3. 高可用性
- 支持服务器在线管理
- 支持主备切换
- 异常处理机制完善

### 4. 跨平台支持
- Windows服务模式
- Linux守护进程模式
- 统一的接口封装

### 5. 日志管理
- 分级日志记录
- 日志文件轮转
- 报文记录可配置

## 启动顺序依赖

1. 数据库访问接口（必须最先）
2. 数据处理中心
3. 订阅中心
4. 服务端管理器
5. 客户端管理器（可选）
6. 在线管理器（最后）

## 注意事项

1. **启动顺序**: 严格按照定义的顺序启动各模块
2. **停止顺序**: 按照启动的相反顺序停止
3. **资源释放**: 确保所有动态库正确释放
4. **信号处理**: Linux下需要正确处理各种信号
5. **配置检查**: 启动前验证配置文件的完整性
