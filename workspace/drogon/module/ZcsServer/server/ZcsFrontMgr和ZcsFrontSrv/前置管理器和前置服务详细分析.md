# ZcsFrontMgr和ZcsFrontSrv - 前置管理器和前置服务详细分析

## 模块概述

### ZcsFrontMgr - 前置管理器
- **主要功能**: 管理多个前置服务进程，实现进程监控和负载均衡
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为前置服务的管理者，负责启动、监控和管理多个前置服务进程

### ZcsFrontSrv - 前置服务
- **主要功能**: 实际的前置采集服务，负责与子站通信和数据采集
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为独立的采集进程，支持103和61850等多种协议

## 文件结构

```
ZcsFrontMgr/                      # 前置管理器
├── Main.cpp                      # 程序入口
├── ServerEngine.h/cpp            # 管理引擎
├── resource.h                    # 资源定义
└── ZcsFrontMgr_update_history.cpp # 更新历史

ZcsFrontSrv/                      # 前置服务
├── Main.cpp                      # 程序入口
├── ServerEngine.h/cpp            # 服务引擎
├── resource.h                    # 资源定义
└── ZcsFrontSrv_update_history.cpp # 更新历史
```

## ZcsFrontMgr - 前置管理器详细分析

### 1. CServerEngine类（管理器版本）

#### 类定义
```cpp
class CServerEngine {
public:
    CLogFile m_LogFile;              // 日志对象
    bool m_bExit;                    // 退出标志
    
private:
    // 配置参数
    int m_nLogMsg;                   // 是否记录报文
    int m_nLogLevel;                 // 日志级别
    int m_nServerRole;               // 服务器角色
    int m_nCallall;                  // 是否定时总招
    int m_nCheckTime;                // 是否对时
    int m_nLogDay;                   // 日志保存天数
    char m_szLogPath[FILE_NAME_MAX_LEN];  // 日志路径
    char m_szLocalIP[19];            // 本地IP
    char m_chServerID[41];           // 服务器ID
    int m_nWorkPart;                 // 工作区号
    string m_strServerId;            // 服务器ID字符串
    
    // 数据库接口
    XJHANDLE m_hLibXJDBFacade;       // 数据库库句柄
    PFUNCONNECT m_pFunConnect;       // 连接函数
    PFUNSETLOGCONFIG m_pFunSetLogConfig; // 日志配置函数
    PFUNSELECT m_pFunSelect;         // 查询函数
    PFUNSETCONNNUM m_pFunSetConnNum; // 设置连接数函数
    
    // 进程管理
    MAP_STN_PROCESS m_map61850Stn;   // 61850厂站进程映射
    vector<string> m_vProcess;       // 进程列表
    string m_strProcess103;          // 103进程名
    MAP_STN_SERVER m_mapStnServer;   // 厂站-服务器映射
};
```

#### 核心数据结构

##### STN_PROCESS - 厂站进程信息
```cpp
typedef struct _STN_PROCESS {
    string strStnId;                 // 厂站ID
    string strProcessName;           // 进程名
    THREAD_HANDLE pProcessHandle;    // 进程句柄
    bool bRunning;                   // 运行状态
    time_t tExitTime;                // 退出时间
    
    _STN_PROCESS() {
        bRunning = false;
        tExitTime = time(NULL) - 600;
    }
} STN_PROCESS;
```

#### 主要功能实现

##### 1. 启动流程
```cpp
bool Start(bool* bExit) {
    m_bExit = false;
    
    // 1. 初始化
    if (!Init()) {
        return false;
    }
    
    // 2. 加载本地配置
    LoadLocalConfig();
    
    // 3. 获取61850厂站列表
    _Get61850StnId();
    
    // 4. 获取厂站-服务器映射关系
    GetStnServerIdMap();
    
    // 5. 复制前置进程文件
    _CopyFrontProcess();
    
    // 6. 启动所有进程
    _StartAllProcess();
    
    // 7. 进入监控循环
    while (!*bExit && !m_bExit) {
        CheckProcessState();
        OS_Sleep(5000);  // 5秒检查一次
    }
    
    // 8. 结束所有进程
    EndAllProcess();
    
    return true;
}
```

##### 2. 进程创建和管理
```cpp
void _CopyFrontProcess() {
    // 为每个61850厂站创建独立的进程文件
    for (auto& stn : m_map61850Stn) {
        string srcPath = "./ZcsFrontSrv";
        string dstPath = "./ZcsFrontSrv_" + stn.first;
        
        // 复制可执行文件
        CopyFile(srcPath, dstPath);
        
        stn.second.strProcessName = dstPath;
    }
}

void _StartAllProcess() {
    // 1. 启动103进程（如果需要）
    if (Have103StnOnThisServer()) {
        char* argv[] = {"ZcsFrontSrv", "103", NULL};
        THREAD_HANDLE handle;
        
        if (create_process("./ZcsFrontSrv", argv, &handle) == 0) {
            m_LogFile.Add("启动103前置进程成功");
        }
    }
    
    // 2. 启动61850进程
    for (auto& stn : m_map61850Stn) {
        // 检查该厂站是否属于本服务器
        if (m_mapStnServer[stn.first] != m_strServerId) {
            continue;
        }
        
        char* argv[] = {
            (char*)stn.second.strProcessName.c_str(),
            (char*)stn.first.c_str(),  // 厂站ID作为参数
            NULL
        };
        
        if (create_process(stn.second.strProcessName.c_str(), 
                          argv, &stn.second.pProcessHandle) == 0) {
            stn.second.bRunning = true;
            m_LogFile.FormatAdd(CLogFile::trace, 
                "启动61850前置进程成功: %s", stn.first.c_str());
        }
    }
}
```

##### 3. 进程监控
```cpp
void CheckProcessState() {
    // 检查每个进程的运行状态
    for (auto& stn : m_map61850Stn) {
        if (!stn.second.bRunning) {
            continue;
        }
        
        // 检查进程是否还在运行
        if (!IsProcessRunning(stn.second.pProcessHandle)) {
            stn.second.bRunning = false;
            stn.second.tExitTime = time(NULL);
            
            m_LogFile.FormatAdd(CLogFile::warn, 
                "检测到进程退出: %s", stn.first.c_str());
            
            // 尝试重启（如果退出时间超过10分钟）
            if (time(NULL) - stn.second.tExitTime > 600) {
                RestartProcess(stn);
            }
        }
    }
    
    // 清理野生前置进程
    CleanWildFront();
}

int CleanWildFront() {
    vector<string> procList;
    
    // 获取系统中所有ZcsFrontSrv进程
    _StatProcNum("ZcsFrontSrv", procList);
    
    // 检查是否为受管理的进程
    for (const auto& proc : procList) {
        bool isManaged = false;
        
        // 检查是否在管理列表中
        for (const auto& stn : m_map61850Stn) {
            if (proc.find(stn.second.strProcessName) != string::npos) {
                isManaged = true;
                break;
            }
        }
        
        // 杀死非受管进程
        if (!isManaged) {
            KillProcess(proc);
            m_LogFile.FormatAdd(CLogFile::warn, 
                "清理野生前置进程: %s", proc.c_str());
        }
    }
    
    return 0;
}
```

##### 4. 多机部署支持
```cpp
bool IfMultiFront() {
    // 查询服务器配置
    SQL_DATA sqlData;
    sqlData.sql_str = "SELECT COUNT(*) FROM t_servers WHERE server_role = ?";
    sqlData.sql_par.push_back("1");  // 前置服务器角色
    
    CMemSet result;
    if (m_pFunSelect(TABLE_SERVERS, sqlData, error, &result)) {
        int count = atoi(result.GetValue(0, 0));
        return count > 1;
    }
    
    return false;
}

bool GetStnServerIdMap() {
    // 获取厂站与服务器的映射关系
    SQL_DATA sqlData;
    sqlData.sql_str = "SELECT station_id, server_id FROM t_station_server_map";
    
    CMemSet result;
    if (m_pFunSelect(TABLE_STATION_SERVER, sqlData, error, &result)) {
        for (int i = 0; i < result.GetRecordCount(); i++) {
            string stnId = result.GetValue(i, 0);
            string srvId = result.GetValue(i, 1);
            m_mapStnServer[stnId] = srvId;
        }
    }
    
    return true;
}
```

## ZcsFrontSrv - 前置服务详细分析

### 1. CServerEngine类（服务版本）

#### 类定义
```cpp
class CServerEngine {
public:
    CLogFile m_LogFile;              // 日志对象
    
private:
    // 配置参数（与管理器类似）
    bool m_bStartClientManager;      // 是否启动客户端管理器
    bool m_bAutoStartClientDll;      // 是否自动启动客户端DLL
    int m_nRunType;                  // 运行类型(0-103, 1-61850)
    string m_strStnProcess;          // 厂站进程标识
    vector<string> m_vStnList;       // 管辖的厂站列表
    
    // 动态库管理
    XJHANDLE m_hLibXJDBFacade;       // 数据库访问库
    XJHANDLE m_hLibXJDataHandle;     // 数据处理中心库
    XJHANDLE m_hLibXJSubscribeCenter;// 订阅中心库
    XJHANDLE m_hLibXJClientManager;  // 客户端管理器库
    XJHANDLE m_hLibXJServerManager;  // 服务器管理器库
    
    // 功能模块管理
    CXJSrvOnlineManager m_SrvOnlineManager; // 在线管理器
    
    // 总线相关
    int m_nBusType;                  // 总线类型
    PFUNSTARTMSGBUSSERVER m_pFunStartBusItf;
    PFUNSTOPMSGBUSSERVER m_pFunStopBusItf;
};
```

#### 主要功能实现

##### 1. 启动流程
```cpp
bool Start(string& strStnProcessType) {
    m_strStnProcess = strStnProcessType;
    
    // 1. 确定运行类型
    if (strStnProcessType == "103") {
        SetRunType(0);  // 103前置
    } else {
        SetRunType(1);  // 61850前置
        m_vStnList.push_back(strStnProcessType);
    }
    
    // 2. 初始化
    if (!Init()) {
        return false;
    }
    
    // 3. 启动各功能模块
    StartDataHandler();      // 数据处理中心
    StartSubscribeCenter();  // 订阅中心
    StartBusItf();          // 总线接口
    StartClientMgr();       // 客户端管理器
    StartSrvOnlineManager(); // 在线管理器
    
    return true;
}
```

##### 2. 模块启动实现
```cpp
int StartDataHandler() {
    STRUCT_XJDATAHANDLER config;
    
    // 设置配置参数
    strcpy(config.szLog_path, m_szLogPath);
    config.iLog_level = m_nLogLevel;
    config.iServerRole = m_nServerRole;
    config.bRecordMsg = (m_nLogMsg == 1);
    config.iLogDay = m_nLogDay;
    
    // 调用动态库函数
    return m_pFunStartDataHandler(config);
}

int StartClientMgr() {
    STRUCT_XJCLIENTMANAGER config;
    
    // 设置配置参数
    strcpy(config.szLog_path, m_szLogPath);
    config.iLog_level = m_nLogLevel;
    config.iServerRole = m_nServerRole;
    config.bRecordMsg = (m_nLogMsg == 1);
    config.iLogDay = m_nLogDay;
    config.iReserved = m_nWorkPart;
    config.pvMyStn = &m_vStnList;  // 传递管辖厂站列表
    
    // 针对不同运行类型设置参数
    if (m_nRunType == 0) {
        // 103前置模式
        strcpy(config.reservedStr, "103");
    } else {
        // 61850单站模式
        strcpy(config.reservedStr, m_strStnProcess.c_str());
    }
    
    return m_pFunStartClientMgr(config);
}

int StartBusItf() {
    STRUCT_XJPROMSGBUSSERVER config;
    
    // 设置配置参数
    strcpy(config.szLog_path, m_szLogPath);
    config.iLog_level = m_nLogLevel;
    config.iLogDay = m_nLogDay;
    config.bRecordMsg = (m_nLogMsg == 1);
    config.pvMyStn = &m_vStnList;
    
    // 设置通道号
    if (m_nBusType == 1) {  // D5K总线
        config.iChannelUp = GetD5kChannelUp();
        config.iChannelDown = GetD5kChannelDown();
    } else {  // STTP总线
        // STTP总线配置
    }
    
    return m_pFunStartBusItf(config);
}
```

##### 3. 运行类型管理
```cpp
void SetRunType(int nType) {
    m_nRunType = nType;
    
    if (nType == 0) {
        m_LogFile.Add("设置运行类型: 103前置");
        // 103前置可能管理多个厂站
        m_bStartClientManager = true;
    } else {
        m_LogFile.Add("设置运行类型: 61850前置");
        // 61850通常单站运行
        m_bStartClientManager = true;
        m_bAutoStartClientDll = true;
    }
}
```

## 架构设计特点

### 1. 进程隔离
- 每个61850厂站独立进程
- 103厂站可共享进程
- 进程间通过总线通信

### 2. 负载均衡
- 多机部署支持
- 厂站动态分配
- 故障自动切换

### 3. 监控机制
- 进程状态监控
- 自动重启机制
- 野生进程清理

### 4. 灵活配置
- 支持103/61850混合
- 动态厂站分配
- 多机热备支持

## 部署模式

### 1. 单机模式
```
ZcsFrontMgr
├── ZcsFrontSrv (103)
├── ZcsFrontSrv_STN001 (61850)
├── ZcsFrontSrv_STN002 (61850)
└── ZcsFrontSrv_STN003 (61850)
```

### 2. 多机模式
```
服务器1:
ZcsFrontMgr
├── ZcsFrontSrv (103)
└── ZcsFrontSrv_STN001

服务器2:
ZcsFrontMgr
├── ZcsFrontSrv_STN002
└── ZcsFrontSrv_STN003
```

## 通信流程

### 1. 数据采集流程
```
子站 -> ZcsFrontSrv -> 客户端管理器 -> 数据处理中心 -> 总线
```

### 2. 命令下发流程
```
总线 -> 订阅中心 -> 客户端管理器 -> ZcsFrontSrv -> 子站
```

## 故障处理

### 1. 进程异常
- 自动检测进程退出
- 延时重启机制
- 日志记录和告警

### 2. 通信异常
- 连接状态监控
- 自动重连机制
- 故障隔离

### 3. 系统异常
- 优雅关闭流程
- 资源清理
- 状态保存

## 性能优化

### 1. 进程管理
- 预创建进程池
- 延迟启动策略
- 资源复用

### 2. 内存管理
- 共享内存使用
- 内存池技术
- 定期垃圾回收

### 3. CPU优化
- 多核负载均衡
- 进程亲和性设置
- 空闲时休眠

## 配置说明

### ZcsFrontMgr配置
- 监控周期
- 重启延迟
- 最大进程数
- 日志级别

### ZcsFrontSrv配置
- 协议类型
- 厂站列表
- 总线类型
- 模块启动选项

## 注意事项

1. **进程命名**: 确保进程名唯一，避免冲突
2. **资源限制**: 注意系统进程数和内存限制
3. **权限管理**: 需要足够权限创建和管理进程
4. **日志管理**: 多进程日志需要合理组织
5. **版本兼容**: 确保各模块版本匹配
