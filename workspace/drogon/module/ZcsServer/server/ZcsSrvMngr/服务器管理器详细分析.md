# ZcsSrvMngr - 服务器管理器详细分析

## 模块概述
- **模块名称**: ZcsSrvMngr/ZcsSrvMgrMain (服务器管理器)
- **主要功能**: 管理服务端连接，处理客户端登录验证，协议转换和会话管理
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为通信服务器的服务端管理模块，负责接受客户端连接并管理会话

## 文件结构

```
ZcsSrvMgrMain/
├── main.cpp                     # 程序入口
├── ZcsSrvMngr.h/cpp            # 主控制类
├── ServerManager.h/cpp          # 服务器管理核心类
├── DBOper.h/cpp                # 数据库操作类
├── SttpBusItf.h/cpp            # STTP总线接口
├── ZxSrvMngrPublisher.h/cpp    # 发布者实现
├── resource.h                   # 资源定义
└── ZcsSrvMngr_update_history.cpp # 更新历史
```

## 核心类分析

### 1. CZcsSrvMngr - 主控制类

#### 类定义
```cpp
class CZcsSrvMngr {
private:
    // 日志和退出控制
    CMessageLog* m_pLogFile;           // 日志对象指针
    bool* m_pExit;                     // 退出标志指针
    
    // 核心组件
    CServerManager m_SrvMgr;           // 服务器管理器
    CSttpBusItf m_ObjSttpBusItf;      // STTP总线接口
    CDBOper m_ObjDbOper;               // 数据库操作
    
    // 配置参数
    int m_nServerRole;                 // 服务器角色
    string m_strLogRootPath;           // 日志根路径
    int m_nLogDay;                     // 日志保留天数
    int m_nLogLevel;                   // 日志级别
    int m_nLogSize;                    // 日志大小
    SCE_SYS_CFG m_SysCfg;             // 系统配置
    
    // 动态库管理
    XJHANDLE m_hLibXJSubscribeCenter;  // 订阅中心库句柄
    PFUNSTARTSUBSCRIBECENTER m_pFunStartSubscribeCenter;
    PFUNSTOPSUBSCRIBECENTER m_pFunStopSubscribeCenter;
    
    XJHANDLE m_hLibXJDataHandle;       // 数据处理中心库句柄
    PFUNSTARTDATAHANDLER m_pFunStartDataHandler;
    PFUNSTOPDATAHANDLER m_pFunStopDataHandler;
    
    // 消息队列
    LIST_STTP_DATA m_lRsltMsg;         // 结果消息缓存
    CXJLock m_LockCacheRsltSttp;       // 结果消息锁
    LIST_STTP_DATA m_lAutoUpMsg;       // 自动上送消息缓存
    CXJLock m_LockCacheAutoUpSttp;     // 自动上送消息锁
};
```

#### 启动流程
```cpp
int Start(CMessageLog* pLogFile, bool* pExit) {
    m_pLogFile = pLogFile;
    m_pExit = pExit;
    
    // 1. 读取配置
    _ReadIni();
    
    // 2. 从数据库读取配置
    _ReadCfgFromDb();
    
    // 3. 重置日志
    _ResetLog();
    
    // 4. 启动总线接口
    m_ObjSttpBusItf.Start(&m_SysCfg, m_pLogFile, m_pExit);
    
    // 5. 加载并启动订阅中心
    _LoadSubscribeCenterLib();
    _StartSubscribeCenter();
    
    // 6. 加载并启动数据处理中心
    _LoadDataHandlerLib();
    _StartDataHandler();
    
    // 7. 设置服务器管理器的总线接口和消息队列
    m_SrvMgr.SetSttpBusItfObj(&m_ObjSttpBusItf);
    m_SrvMgr.SetSttpMsgRecvList(&m_lRsltMsg, &m_LockCacheRsltSttp);
    m_SrvMgr.SetAutoUpSttpMsgRecvList(&m_lAutoUpMsg, &m_LockCacheAutoUpSttpA);
    
    // 8. 启动服务器管理器
    STRUCT_XJSERVERMANAGER config;
    // 填充配置...
    m_SrvMgr.Start(config);
    
    return 0;
}
```

### 2. CServerManager - 服务器管理核心类

#### 类定义
```cpp
class CServerManager {
private:
    // 状态标志
    bool m_bLogMsg;                    // 是否记录报文
    bool m_bInit;                      // 初始化成功标志
    bool m_bExit;                      // 退出标志
    bool m_bListenSTTPSC;              // 是否监听STTP短连接
    bool m_bRunMsgBus;                 // 是否运行消息总线
    bool m_bDataHdlSrvConnect;         // 数据处理模块连接状态
    
    // 会话管理
    u_short m_iSessionExists;          // 当前会话数
    SERVERSESSION m_SessionList[MAX_SESSION]; // 会话数组
    CXJLock m_lockSessionPool;         // 会话池锁
    
    // 客户端管理
    CLIENTBASEQUEUE m_viewClient;      // 客户端列表
    CXJLock m_lockViewClient;          // 客户端列表锁
    map<string,int> m_mapClientModle;  // 客户端编号-名称映射
    
    // 网络监听
    CXJNet_Acceptor m_103Acceptor;     // 103协议监听器
    CXJNet_Acceptor m_sttpAcceptor;    // STTP协议监听器
    char m_szLocalIP[19];              // 本地IP
    int m_n103Port;                    // 103监听端口
    int m_nSttpPort;                   // STTP监听端口
    
    // 线程管理
    THREAD_ID m_hSttpThreadId;         // STTP监听线程
    THREAD_HANDLE m_hSttpThreadHandle;
    THREAD_ID m_h103ThreadId;          // 103监听线程
    THREAD_HANDLE m_h103ThreadHandle;
    THREAD_ID m_CommandHandleThreadId; // 命令处理线程
    THREAD_HANDLE m_hCommandHandleThreadHandle;
    THREAD_ID m_RecvMsgThreadId;       // 接收消息线程
    THREAD_HANDLE m_hRecvMsgThreadHandle;
    THREAD_ID m_AutoUpMsgThreadId;     // 自动上送线程
    THREAD_HANDLE m_hAutoUpMsgThreadHandle;
    THREAD_ID m_CommandCatheThreadId;  // 命令缓存线程
    THREAD_HANDLE m_hCommandCatheThreadHandle;
    
    // 配置和资源
    CMessageLog m_LogFile;             // 日志对象
    CXJDBFacade* m_pDBFacade;          // 数据库接口
    TPROTOCOLTABLE m_tProtocolConfigs; // 协议配置表
    TIMEOUTS m_tTimeOut;               // 超时配置
    
    // 动态库管理
    XJHANDLE m_hLibXJProSttpSCServer;  // STTP短连接库
    PFUNSTARTSTTPSCSERVER m_pFunStartSttpSCServer;
    PFUNSTOPSTTPSCSERVER m_pFunStopSttpSCServer;
    
    XJHANDLE m_hLibXJProMsgBusServer;  // 消息总线库
    PFUNSTARTMSGBUSSERVER m_pFunStartMsgBusServer;
    PFUNSTOPMSGBUSSERVER m_pFunStopMsgBusServer;
    
    // 发布者和总线接口
    CZxSrvMngrPublisher* m_pMsgHandler; // 消息处理发布者
    CSttpBusItf* m_pSttpBusItfObj;     // 总线接口对象
    
    // 消息队列
    LIST_STTP_DATA* m_plSttpData;      // STTP数据队列
    CXJLock* m_pLockSttpCache;         // STTP队列锁
    LIST_STTP_DATA* m_plAutoUpSttpData; // 自动上送队列
    CXJLock* m_pLockAutoUpSttpCache;   // 自动上送队列锁
    
    // 命令缓存
    map<u_short,STTP_CMD_CACHE> m_mapCachePool; // 命令缓存池
    CXJLock m_lockMsgCachePool;        // 缓存池锁
    map<int,bool> m_mRii;              // RII映射表
    CXJLock m_lockRiiCachePool;        // RII锁
};
```

#### 会话结构体
```cpp
struct SERVERSESSION {
    int iNO;                           // 会话索引
    int iType;                         // 客户端类型
    int iDevNum;                       // 装置个数
    int iAddr;                         // 地址
    u_short nProper;                   // 会话属性(STTP/103)
    bool bUsed;                        // 使用状态
    bool bExit;                        // 退出标志
    bool bLocalSave;                   // 网络异常时是否本地保存
    CXJNet_Stream* pNetStream;         // 网络连接
    void* pOperation;                  // 操作对象
    THREAD_ID threadId;                // 线程ID
    THREAD_HANDLE hThreadHandle;       // 线程句柄
    HNSEM hWaitSem;                   // 信号量
    XJHANDLE hProLib;                 // 协议库句柄
    PFUNSERVERRUN pProRun;            // 运行函数
    PFUNSERVERRUN_WEB pProRun_Web;   // Web运行函数
    char szIp[20];                    // 客户端IP
    char szClientID[13];              // 客户端ID
    char szDevIDList[ONE_FRONT_MAX_DEV_NUM][13]; // 装置列表
    bool bWeb;                        // 是否Web客户端
};
```

#### 客户端基本信息
```cpp
typedef struct _CLIENT_BASE {
    bool bConnected;                   // 连接状态
    bool bLocalSave;                   // 本地保存标志
    u_short uProId;                    // 协议ID
    int nType;                         // 客户端类型
    int nAddr;                         // 地址
    char chClientID[13];               // 客户端ID
    char chIp[20];                     // 客户端IP
    char chIpReserve[20];              // 备用IP
    char chLibFileName[90];            // 协议库文件名
} CLIENT_BASE;
```

#### 命令缓存结构
```cpp
typedef struct _sttp_cmd_cache {
    unsigned long wSttpCmdId;          // 命令号
    char szStationId[13];              // 厂站ID
    char szEquipId[13];                // 装置ID
    int iCpu;                          // CPU号
    unsigned int uRII;                 // 上级RII
    unsigned int uMsgId;               // 消息ID
    char szObserver[13];               // 观察者ID
    STTPMSG SttpCmd;                   // 命令报文
    BYTE byIsFull;                     // 完整结果标志
    int nCmdStatus;                    // 命令状态(-1:未处理,0:处理中,1:完成)
    unsigned int uTaskTime;            // 任务超时时间
    time_t t_CmdStartime;              // 命令开始时间
    time_t t_TOliveStarttime;          // 超时心跳时间
    float fReserved;                   // 保留字段
    int nMsgSource;                    // 命令来源
} STTP_CMD_CACHE;
```

### 3. 主要功能实现

#### 监听服务启动
```cpp
bool InitNetAcceptor() {
    // 1. 读取监听配置
    ReadListenOption();
    
    // 2. 打开STTP监听
    if (!OpenSttpAcceptor()) {
        return false;
    }
    
    // 3. 打开103监听
    if (!Open103Acceptor()) {
        return false;
    }
    
    return true;
}

bool OpenSttpAcceptor() {
    // 设置监听地址
    CXJNetAddr addr(m_nSttpPort, m_szLocalIP);
    
    // 开始监听
    if (m_sttpAcceptor.open(addr) < 0) {
        m_LogFile.FormatAdd(CLogFile::error, 
            "STTP监听失败，端口:%d", m_nSttpPort);
        return false;
    }
    
    m_LogFile.FormatAdd(CLogFile::trace, 
        "STTP监听成功，端口:%d", m_nSttpPort);
    return true;
}
```

#### STTP监听线程
```cpp
int MainSttpLoop() {
    CXJNet_Stream stream;
    CXJNetAddr clientAddr;
    
    while (!m_bExit) {
        // 接受客户端连接
        if (m_sttpAcceptor.accept(stream, &clientAddr) < 0) {
            if (!m_bExit) {
                OS_Sleep(100);
            }
            continue;
        }
        
        // 获取客户端信息
        string clientIP = clientAddr.get_host_addr();
        
        m_LogFile.FormatAdd(CLogFile::trace, 
            "接受STTP客户端连接: %s", clientIP.c_str());
        
        // 获取空闲会话
        SERVERSESSION* pSession = GetFreeSession();
        if (pSession == NULL) {
            m_LogFile.Add("无空闲会话，拒绝连接");
            stream.close();
            continue;
        }
        
        // 设置会话参数
        pSession->pNetStream = new CXJNet_Stream(stream.get_handle());
        strcpy(pSession->szIp, clientIP.c_str());
        pSession->nProper = CLIENT_STTP;
        pSession->bUsed = true;
        
        // 登录验证
        if (!LoginVerify(pSession)) {
            ResetSession(pSession);
            continue;
        }
        
        // 启动协议处理
        StartProtocol(pSession);
    }
    
    return 0;
}
```

#### 登录验证
```cpp
bool LoginVerify(SERVERSESSION* pSession) {
    STTPMSG loginMsg;
    
    // 1. 接收登录报文
    if (RecvSttpMsg(&loginMsg, pSession->pNetStream) <= 0) {
        m_LogFile.Add("接收登录报文失败");
        return false;
    }
    
    // 2. 验证是否为登录报文(00101)
    if (loginMsg.msgID != 0x00101) {
        m_LogFile.FormatAdd(CLogFile::warn, 
            "非登录报文，消息ID: %04X", loginMsg.msgID);
        return false;
    }
    
    // 3. 解析登录信息
    char clientId[13];
    char userName[32];
    char password[32];
    // 解析报文内容...
    
    // 4. 查找客户端配置
    CLIENT_BASE* pClient = GetClientNode(pSession->szIp, CLIENT_STTP);
    if (pClient == NULL) {
        m_LogFile.FormatAdd(CLogFile::warn, 
            "未配置的客户端: %s", pSession->szIp);
        RefuseLogin(pSession);
        return false;
    }
    
    // 5. 验证用户名密码
    // ...
    
    // 6. 设置会话信息
    strcpy(pSession->szClientID, pClient->chClientID);
    pSession->uProId = pClient->uProId;
    
    // 7. 发送登录响应
    SendLoginResponse(pSession, 0);  // 0表示成功
    
    return true;
}
```

#### 命令处理
```cpp
int CommandHandleLoop() {
    STTP_PUBLISH_MSG cmdMsg;
    
    while (!m_bExit) {
        // 从发布者拉取命令
        if (!PullCommandFromPublisher(cmdMsg)) {
            OS_Sleep(10);
            continue;
        }
        
        // 处理命令
        CommandHandle(cmdMsg);
    }
    
    return 0;
}

void CommandHandle(const STTP_PUBLISH_MSG& pCmdMsg) {
    // 1. 获取可用的RII
    int newRii = _GetAvailableRii();
    
    // 2. 创建命令缓存
    STTP_CMD_CACHE cmdCache;
    cmdCache.wSttpCmdId = newRii;
    cmdCache.uRII = GetRiiFromMsg(pCmdMsg.msg);
    cmdCache.uMsgId = pCmdMsg.msg.msgID;
    strcpy(cmdCache.szObserver, pCmdMsg.observerId);
    memcpy(&cmdCache.SttpCmd, &pCmdMsg.msg, sizeof(STTPMSG));
    cmdCache.nCmdStatus = -1;  // 未处理
    cmdCache.t_CmdStartime = time(NULL);
    
    // 3. 加入缓存池
    {
        CCsLocker locker(&m_lockMsgCachePool);
        m_mapCachePool[newRii] = cmdCache;
    }
    
    // 4. 修改报文RII并转发
    SetSttpMsgRII(&pCmdMsg.msg, newRii);
    
    // 5. 根据目标查找会话并发送
    SERVERSESSION* pSession = FindSessionByTarget(cmdCache.szStationId);
    if (pSession != NULL) {
        SendSttpMsg(&pCmdMsg.msg, pSession->pNetStream);
    }
}
```

#### 结果分发
```cpp
int DispatchSttpResult(STTP_FULL_DATA SttpData) {
    // 1. 查找对应的命令缓存
    u_short cmdRii = GetRiiFromFullData(SttpData);
    
    STTP_CMD_CACHE* pCache = NULL;
    {
        CCsLocker locker(&m_lockMsgCachePool);
        auto it = m_mapCachePool.find(cmdRii);
        if (it != m_mapCachePool.end()) {
            pCache = &it->second;
        }
    }
    
    if (pCache == NULL) {
        // 非命令响应，可能是主动上送
        return AutoUpHdl(SttpData);
    }
    
    // 2. 恢复原始RII
    SetFullDataRII(&SttpData, pCache->uRII);
    
    // 3. 构造响应消息
    STTP_PUBLISH_MSG resultMsg;
    strcpy(resultMsg.observerId, pCache->szObserver);
    ConvertFullDataToMsg(&SttpData, &resultMsg.msg);
    
    // 4. 发送给原始请求者
    PushResultMsgToPublisher(resultMsg, pCache->uMsgId);
    
    // 5. 更新缓存状态
    pCache->nCmdStatus = 1;  // 完成
    
    return 0;
}
```

### 4. 线程模型

系统采用多线程架构：

1. **STTP监听线程**: 监听STTP端口，接受连接
2. **103监听线程**: 监听103端口，接受连接
3. **命令处理线程**: 处理来自发布者的命令
4. **接收消息线程**: 处理接收到的STTP消息
5. **自动上送线程**: 处理自动上送消息
6. **命令缓存线程**: 管理命令缓存和超时
7. **会话线程池**: 每个客户端连接一个线程

### 5. 协议支持

#### 动态加载协议库
```cpp
bool LoadLibFunRun(SERVERSESSION* pSession, const char* pLibName, bool bWeb) {
    // 1. 加载动态库
    pSession->hProLib = OS_LoadLibrary(pLibName);
    if (!pSession->hProLib) {
        m_LogFile.FormatAdd(CLogFile::error, 
            "加载协议库失败: %s", pLibName);
        return false;
    }
    
    // 2. 获取运行函数
    if (bWeb) {
        pSession->pProRun_Web = (PFUNSERVERRUN_WEB)
            OS_GetProcAddress(pSession->hProLib, "Run");
    } else {
        pSession->pProRun = (PFUNSERVERRUN)
            OS_GetProcAddress(pSession->hProLib, "Run");
    }
    
    return true;
}
```

#### 协议运行接口
```cpp
int RunProtocol(SERVERSESSION* pSession) {
    SERVER_PRO_INTERFACE proInterface;
    
    // 填充接口参数
    proInterface.iNO = pSession->iNO;
    proInterface.p_bExit = &pSession->bExit;
    proInterface.pIp = pSession->szIp;
    strcpy(proInterface.chClientID, pSession->szClientID);
    proInterface.pNetStream = pSession->pNetStream;
    strcpy(proInterface.log_path, m_szLogPath);
    proInterface.log_level = m_nLogLevel;
    proInterface.bRecordMsg = m_bLogMsg;
    proInterface.iServerRole = m_nServerRole;
    proInterface.iDevNum = pSession->iDevNum;
    proInterface.pDevList = pSession->szDevIDList;
    
    // 调用协议库
    if (pSession->bWeb && pSession->pProRun_Web) {
        return pSession->pProRun_Web(&proInterface, m_pMsgHandler);
    } else if (pSession->pProRun) {
        return pSession->pProRun(&proInterface);
    }
    
    return -1;
}
```

## 关键特性

### 1. 多协议支持
- STTP协议
- IEC 103协议
- 支持扩展其他协议

### 2. 会话管理
- 会话池预分配
- 动态会话分配
- 会话状态跟踪

### 3. 命令缓存机制
- RII映射管理
- 命令状态跟踪
- 超时处理

### 4. 高并发设计
- 多线程架构
- 异步消息处理
- 队列缓冲

### 5. 安全机制
- 登录验证
- IP白名单
- 权限控制

## 配置说明

### 必需配置
- 监听端口（STTP/103）
- 本地IP地址
- 客户端列表
- 协议库路径

### 可选配置
- 最大会话数
- 超时参数
- 日志级别
- 报文记录

## 性能优化

### 1. 连接管理
- 连接池复用
- 快速连接释放
- 异常连接检测

### 2. 消息处理
- 批量消息处理
- 优先级队列
- 并行处理

### 3. 资源管理
- 内存池技术
- 线程池管理
- 动态资源分配

## 故障处理

### 1. 连接异常
- 自动断线检测
- 会话清理
- 资源回收

### 2. 协议异常
- 异常报文过滤
- 错误日志记录
- 自动恢复机制

### 3. 系统异常
- 优雅关闭
- 状态保存
- 快速恢复

## 注意事项

1. **端口冲突**: 确保监听端口未被占用
2. **协议库版本**: 保证协议库接口兼容
3. **会话数限制**: 注意最大会话数配置
4. **内存管理**: 及时释放无用会话
5. **日志维护**: 定期清理日志文件
