# ZcsCliMngr - 客户端管理器详细分析

## 模块概述
- **模块名称**: ZcsCliMngr (客户端管理器)
- **主要功能**: 管理与子站的客户端连接，实现协议转换和数据采集
- **创建时间**: 2008年
- **作者**: qingch
- **设计目的**: 作为通信服务器的客户端管理模块，负责主动连接子站并进行数据交互

## 文件结构

```
ZcsCliMngr/
├── ZcsCliMngr.h/cpp             # 模块接口定义和实现
├── ClientManager.h/cpp          # 客户端管理器核心类
├── ZxClientSession.h/cpp        # 客户端会话类
├── ZcsCliMngrPublisher.h/cpp    # 发布者模式实现
├── resource.h                   # 资源定义
└── ZcsCliMngr_update_history.cpp # 更新历史
```

## 核心类分析

### 1. CClientManager - 客户端管理器核心类

#### 类定义
```cpp
class CClientManager {
private:
    // 数据库访问
    CXJDBFacade* m_pDBFacade;                    // 数据库接口
    
    // 配置参数
    bool m_bLogMsg;                              // 是否记录报文
    bool m_bExit;                                // 退出标志
    int m_nLogLevel;                             // 日志级别
    int m_nLogDay;                               // 日志保存天数
    int m_nSessionIndex;                         // 会话索引
    char m_szLogPath[FILE_NAME_MAX_LEN];         // 日志路径
    
    // 线程管理
    THREAD_ID m_threadId;                        // 线程ID
    THREAD_HANDLE m_hThreadHandle;               // 线程句柄
    
    // 日志和发布者
    CMessageLog m_LogFile;                       // 日志对象
    CXJClientManagerPublisher m_iPublisher;      // 发布者对象
    
    // 会话池和配置
    TCSESSIONPOOL m_pClientSessionPool;          // 客户端会话池
    TPROTOCOLTABLE m_tProtocolConfig;            // 协议配置表
    
    // 在线管理
    CXJSrvOnlineManager m_SrvOnlineManager;      // 服务器在线管理
    
    // 工作参数
    int m_nWorkPart;                             // 安全区号(0-1区,1-2区)
    MAP_SUBSTN_CHANNEL_INFO m_mSubStnChlInf;     // 子站通道信息
    MAP_SUBSTN_CHANNEL_INFO m_mJunStnChInf;      // 分站通道信息
    int m_nCylSstnStatus;                        // 子站状态上送周期
    CGetDataType* m_pGetDataType;                // 数据类型获取对象
    
    // 服务器配置
    set<string> m_PermitCtrlStationSet;          // 许可管理的子站集合
    string m_strMySrvId;                         // 本机服务器ID
    int m_nSrvRunType;                           // 运行方式(0-单机,1-主备,2-多机)
    bool m_bLbNetMstn;                           // 是否录波联网主站
    string m_strProcessType;                     // 进程类型(103或61850)
    vector<string>* m_pvStn;                     // 管辖的子站列表
    int m_nIsTakeOverStn;                        // 是否接管故障服务器子站
};
```

#### 主要功能函数

##### 1. 启动和停止
```cpp
int Start(const STRUCT_XJCLIENTMANAGER& pClientMgr) {
    // 1. 初始化日志
    InitLogFile();
    
    // 2. 加载数据库接口
    LoadDBFacadeLib();
    
    // 3. 初始化数据类型对象
    InitGetDataTypeObj();
    
    // 4. 加载本地服务器配置
    LoadLocalServeConfig();
    
    // 5. 初始化协议配置表
    InitProtocolCFGTable();
    
    // 6. 初始化会话池
    InitSessionPool();
    
    // 7. 加载协议库
    LoadProtocolLib();
    
    // 8. 初始化在线管理器
    InitOnlineManager();
    
    // 9. 启动命令处理线程
    StartCommandThread();
    
    // 10. 启动所有会话
    StartAllSession();
}

int End() {
    // 按相反顺序停止
    EndAllSession();
    EndCommandThread();
    m_SrvOnlineManager.StopSrvOnLineManager();
    ClearSessionPool();
}
```

##### 2. 会话池管理
```cpp
bool InitSessionPool() {
    CMemSet memSet;
    
    // 1. 读取子站列表
    ReadSubStationList(memSet, error);
    
    // 2. 读取分站列表
    ReadJuniorMasterList(memSet, error);
    
    // 3. 整合子站通道信息
    __MakeSubStnChlInfo();
    
    // 4. 整合分站通道信息
    __MakeJunStnChlInfo();
    
    // 5. 为每个子站创建会话
    for (auto& subStn : m_mSubStnChlInf) {
        InitOneSession(subStn.second);
    }
}

bool InitOneSession(SUBSTATION_CHANNEL_INFO& SubStnChlInfo) {
    // 检查是否属于本服务器管辖
    if (!IfStnIdBelongMe(SubStnChlInfo.szStationId, nCommuType)) {
        return false;
    }
    
    // 创建客户端会话对象
    CXJClientSession* pSession = new CXJClientSession(
        m_nSessionIndex++,
        &m_bExit,
        m_LogFile,
        m_bLogMsg,
        m_nLogLevel,
        m_nLogDay,
        m_szLogPath
    );
    
    // 设置会话参数
    pSession->SetStnChlInfo(SubStnChlInfo);
    pSession->ProtocolName(protocolName);
    pSession->SetCylSstnStatus(m_nCylSstnStatus);
    pSession->SetGetDataTypeObj(m_pGetDataType);
    
    // 加入会话池
    m_pClientSessionPool.insert(make_pair(SubStnChlInfo.szStationId, pSession));
}
```

##### 3. 命令处理
```cpp
int CommandHandleLoop() {
    STTP_PUBLISH_MSG sttpCmdmsg;
    
    while (!m_bExit) {
        // 从发布者拉取命令
        if (PullCommandFromPublisher(sttpCmdmsg)) {
            CommandHandle(sttpCmdmsg);
        }
        OS_Sleep(10);
    }
}

void CommandHandle(const STTP_PUBLISH_MSG& sttpCmdmsg) {
    // 解析命令消息
    STTPMSG* pMsg = &sttpCmdmsg.msg;
    
    switch (pMsg->msgID) {
        case 20162:  // 查询客户端启动状态
            QueryClientStartStatus(sttpCmdmsg);
            break;
            
        case 20165:  // 加载客户端规约库
            LoadClientLibHandle(sttpCmdmsg);
            break;
            
        default:
            // 转发到相应的客户端会话
            ForwardToSession(sttpCmdmsg);
            break;
    }
}
```

##### 4. 服务器切换处理
```cpp
static int OnSrvSwitch(void* pParam, int pStatus, 
                      vector<stXJSubstation>& pStationList) {
    CClientManager* pThis = (CClientManager*)pParam;
    return pThis->SrvSwitchHandle(pStatus, pStationList);
}

int SrvSwitchHandle(int pStatus, vector<stXJSubstation>& pStationList) {
    // 更新管辖厂站列表
    m_PermitCtrlStationSet.clear();
    
    for (auto& station : pStationList) {
        m_PermitCtrlStationSet.insert(station.szStationId);
        
        // 更新动态管理列表
        if (m_pvStn) {
            ModifyVector(m_pvStn, station.szStationId, 1);
        }
    }
    
    // 停止不在列表中的子站
    StopStationsExceptLoadList();
    
    // 启动列表中的子站
    StartStationsInLoadList();
}
```

### 2. CLIENTSESSION - 会话结构体

```cpp
struct CLIENTSESSION {
    UINT iNO;                                    // 会话索引
    int iAddr;                                   // 站地址
    int iProtocolId;                            // 规约ID
    int iRunType;                               // 运行类型
    bool bExit;                                 // 退出标志
    void* pOperation;                           // 操作对象指针
    THREAD_ID threadId;                         // 线程ID
    THREAD_HANDLE hThreadHandle;                // 线程句柄
    HNSEM hWaitSem;                            // 信号量
    XJHANDLE hProLib;                          // 动态库句柄
    PFUNCLIENTRUN pProRun;                     // Run函数指针
    char szStationId[13];                      // 子站ID
    char szClientName[30];                     // 子站名称
    STRUCT_DESTINATION_SERVER Destination;      // 目标服务器地址
    char szGatewayList[FILE_NAME_MAX_LEN];     // 网关列表
};
```

### 3. CXJClientSession - 客户端会话类

#### 类定义
```cpp
class CXJClientSession {
private:
    // 状态标志
    bool m_bStarted;                            // 启动标志
    bool m_bLogMsg;                             // 记录报文标志
    bool m_bExit;                               // 退出标志
    
    // 会话参数
    UINT m_iNO;                                 // 会话号
    int m_iAddr;                                // 站地址
    int m_iRunType;                             // 运行类型
    int m_nLogLevel;                            // 日志级别
    int m_nLogDay;                              // 日志天数
    
    // 线程管理
    THREAD_ID m_threadId;                       // 线程ID
    THREAD_HANDLE m_hThreadHandle;              // 线程句柄
    
    // 动态库管理
    XJHANDLE m_hProLib;                         // 协议库句柄
    PFUNCLIENTRUN m_pProRun;                    // 运行函数
    
    // 日志
    CLogFile& m_rLogFile;                       // 日志引用
    
    // 配置信息
    char m_szStationId[13];                     // 子站ID
    char m_szClientName[31];                    // 子站名称
    char m_szGatewayList[FILE_NAME_MAX_LEN];    // 网关列表
    char m_szLogPath[FILE_NAME_MAX_LEN];        // 日志路径
    string m_strProtocolName;                   // 协议库名称
    
    // 目标服务器
    STRUCT_DESTINATION_SERVER m_iDestination;   // 目标地址信息
    
    // 其他参数
    int m_nCylSstnStatus;                       // 状态上送周期
    CGetDataType* m_pGetDataType;               // 数据类型对象
};
```

#### 主要功能

##### 1. 加载协议库
```cpp
bool LoadLibrary() {
    // 加载动态库
    m_hProLib = OS_LoadLibrary(m_strProtocolName.c_str());
    if (!m_hProLib) {
        return false;
    }
    
    // 获取运行函数
    m_pProRun = (PFUNCLIENTRUN)OS_GetProcAddress(m_hProLib, "Run");
    if (!m_pProRun) {
        return false;
    }
    
    return true;
}
```

##### 2. 运行协议
```cpp
int RunProtocol() {
    CLIENT_PRO_INTERFACE proInterface;
    
    // 填充接口参数
    proInterface.iNO = m_iNO;
    proInterface.p_bExit = &m_bExit;
    proInterface.pszStationId = m_szStationId;
    proInterface.pszClientName = m_szClientName;
    proInterface.pDestination = &m_iDestination;
    strcpy(proInterface.log_path, m_szLogPath);
    proInterface.log_level = m_nLogLevel;
    proInterface.bRecordMsg = m_bLogMsg;
    proInterface.iRunType = m_iRunType;
    proInterface.ilog_day = m_nLogDay;
    proInterface.nAutoUpCylSstnStatus = m_nCylSstnStatus;
    proInterface.pGetDataType = m_pGetDataType;
    strcpy(proInterface.szReserved, m_szGatewayList);
    
    // 调用协议库运行函数
    return m_pProRun(&proInterface);
}
```

### 4. CXJClientManagerPublisher - 发布者类

继承自 CXJPublisher，实现发布-订阅模式，负责：
- 接收来自订阅中心的命令
- 发布数据到数据处理中心
- 管理客户端与其他模块的消息交互

## 运行类型说明

系统支持三种运行类型：
1. **0 - 常规模式**: 处理全部信息
2. **1 - 实时子网模式**: 仅传输保护信息
3. **2 - 非实时子网模式**: 仅传输录波器信息

## 数据流程

### 1. 命令下发流程
```
订阅中心 -> 客户端管理器 -> 客户端会话 -> 协议库 -> 子站
```

### 2. 数据上送流程
```
子站 -> 协议库 -> 客户端会话 -> 客户端管理器 -> 数据处理中心
```

## 高可用设计

### 1. 服务器切换
- 支持主备切换和多机热备
- 动态调整管辖厂站列表
- 自动接管故障服务器的子站

### 2. 会话管理
- 会话池管理，支持动态增删
- 异常会话自动重连
- 线程池模式，每个会话独立线程

### 3. 协议扩展
- 动态加载协议库
- 支持多种协议并存
- 协议配置灵活可变

## 配置管理

### 1. 数据库配置
- 子站配置表 (t_substations)
- 分站配置表 (t_junior_masters)
- 通道配置表 (t_channels)
- 协议配置表 (t_protocols)

### 2. 运行时配置
- 日志级别和路径
- 报文记录开关
- 安全分区设置
- 状态上送周期

## 关键技术点

### 1. 线程安全
- 每个会话独立线程
- 会话池访问同步
- 命令队列线程安全

### 2. 资源管理
- 动态库生命周期管理
- 内存池技术
- 连接池复用

### 3. 性能优化
- 批量处理命令
- 异步消息处理
- 智能重连策略

## 注意事项

1. **协议库兼容性**: 确保协议库接口版本匹配
2. **配置一致性**: 数据库配置必须与实际网络拓扑一致
3. **资源限制**: 注意系统最大线程数和文件句柄限制
4. **日志管理**: 定期清理日志文件，避免磁盘满
5. **安全考虑**: 网关配置需要考虑网络安全策略
