# ZcsNw103v3MsCliPro - 南网IEC103 V3版主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsNw103v3MsCliPro
- **主要功能**: 实现南方电网IEC 60870-5-103协议V3版本主站客户端
- **设计目的**: 符合南网标准的103协议V3版本实现，支持主站侧数据采集

## 核心组件

### 1. 南网V3版本特性
- **扩展ASDU类型**：支持南网扩展的ASDU类型
- **增强时标**：毫秒级时标精度
- **扩展信息体**：支持南网定制信息体
- **双重化支持**：支持双网双机冗余

### 2. 协议扩展实现
```cpp
// 南网V3版ASDU扩展
class CNw103V3AsduExtension {
public:
    // 南网扩展类型标识
    enum NwV3TypeID {
        // 标准103类型
        M_SP_NA_1 = 1,      // 单点信息
        M_DP_NA_1 = 3,      // 双点信息
        M_ME_NA_1 = 9,      // 测量值
        
        // 南网V3扩展类型
        M_SP_EX_1 = 128,    // 扩展单点信息
        M_DP_EX_1 = 129,    // 扩展双点信息
        M_ME_EX_1 = 130,    // 扩展测量值
        M_ST_EX_1 = 131,    // 扩展步位置信息
        M_BO_EX_1 = 132,    // 扩展比特串
        M_FA_EX_1 = 133,    // 故障信息扩展
        M_EV_EX_1 = 134     // 事件信息扩展
    };
    
    // 扩展信息体
    struct ExtendedInfoObject {
        uint16_t address;        // 信息对象地址
        uint32_t value;         // 值
        uint8_t quality;        // 品质描述
        uint64_t timestamp;     // 时标（毫秒精度）
        uint8_t extData[16];    // 扩展数据
    };
};
```

### 3. 主站客户端实现
```cpp
class CNw103V3MasterClient {
public:
    // 初始化连接
    int Initialize(const Nw103V3Config& config);
    
    // 总召唤
    int GeneralInterrogation(int asduAddr);
    
    // 时钟同步
    int ClockSynchronization(const TimeStamp& time);
    
    // 读取扰动数据
    int ReadDisturbanceData(int funcType, int infNo);
    
    // 读取录波数据
    int ReadRecordedData(const RecordRequest& request);
    
    // 控制命令
    int SendControl(int asduAddr, const ControlCommand& cmd);
    
private:
    // 南网V3配置
    Nw103V3Config m_config;
    
    // 数据处理器
    Nw103V3DataProcessor m_processor;
};
```

## 南网V3协议特殊处理

### 1. 链路层优化
```cpp
class CNw103V3LinkLayer {
public:
    // 平衡模式通信
    int BalancedCommunication();
    
    // 非平衡模式通信  
    int UnbalancedCommunication();
    
    // 链路状态管理
    int ManageLinkState();
    
    // 双网切换
    int SwitchNetwork(int netId);
    
private:
    // 主备链路
    LinkConnection m_primaryLink;
    LinkConnection m_backupLink;
    
    // 链路参数
    struct LinkParams {
        int retryCount;         // 重试次数
        int retryTimeout;       // 重试超时
        int idleTimeout;        // 空闲超时
        bool autoSwitch;        // 自动切换
    } m_linkParams;
};
```

### 2. 应用层扩展
```cpp
class CNw103V3AppLayer {
public:
    // 处理南网扩展ASDU
    int ProcessExtendedASDU(const uint8_t* buffer, int length);
    
    // 生成扩展命令
    int GenerateExtendedCommand(uint8_t typeId, uint8_t* buffer);
    
    // 故障录波处理
    int ProcessFaultRecord(const FaultData& data);
    
    // 事件顺序记录
    int ProcessSOE(const SOEData& soe);
    
private:
    // 扩展处理器映射
    std::map<uint8_t, ExtendedHandler*> m_extHandlers;
};
```

### 3. 时标处理增强
```cpp
// 南网V3时标格式
struct Nw103V3TimeStamp {
    uint16_t milliseconds;   // 毫秒(0-999)
    uint8_t seconds;        // 秒(0-59)
    uint8_t minutes;        // 分(0-59)
    uint8_t hours;          // 时(0-23)
    uint8_t day;            // 日(1-31)
    uint8_t month;          // 月(1-12)
    uint16_t year;          // 年(2000-2099)
    uint8_t quality;        // 时标品质
    
    // 转换为标准时间
    uint64_t ToEpochMs() const;
    
    // 从标准时间转换
    void FromEpochMs(uint64_t epochMs);
};
```

## 数据采集优化

### 1. 批量数据处理
```cpp
class CNw103V3BatchProcessor {
public:
    // 批量总召
    int BatchGeneralInterrogation(const std::vector<int>& asduAddrs);
    
    // 批量读取
    int BatchRead(const std::vector<ReadRequest>& requests);
    
    // 批量控制
    int BatchControl(const std::vector<ControlItem>& items);
    
    // 结果汇总
    int CollectResults(BatchResults& results);
    
private:
    // 批处理队列
    std::queue<BatchTask> m_taskQueue;
    
    // 并发控制
    int m_maxConcurrent;
};
```

### 2. 数据缓存机制
- **分级缓存**：热点数据内存缓存
- **预读取**：预测性数据读取
- **压缩存储**：历史数据压缩

## 故障录波处理

### 1. 录波数据获取
```cpp
class CNw103V3RecorderHandler {
public:
    // 读取录波目录
    int ReadRecorderDirectory(RecorderDir& dir);
    
    // 选择录波文件
    int SelectRecorderFile(int fileId);
    
    // 传输录波数据
    int TransferRecorderData(uint8_t* buffer, int& length);
    
    // 录波数据解析
    int ParseRecorderData(const uint8_t* data, int length, 
                         RecorderInfo& info);
    
private:
    // 录波缓冲区
    std::vector<uint8_t> m_recorderBuffer;
    
    // 传输状态
    TransferState m_transferState;
};
```

### 2. COMTRADE格式支持
- **CFG文件生成**：配置文件生成
- **DAT文件生成**：数据文件生成
- **格式转换**：103到COMTRADE转换

## 配置参数

### 南网V3版配置
```ini
[Nw103V3_Config]
# 链路层参数
LinkAddress = 1         # 链路地址
LinkAddressSize = 2     # 链路地址长度
BalancedMode = true     # 平衡模式

# 应用层参数
AsduAddress = 1         # ASDU公共地址
AsduAddressSize = 2     # ASDU地址长度
InfoObjAddressSize = 2  # 信息体地址长度
CauseOfTransSize = 2    # 传送原因长度

# 南网扩展参数
EnableExtendedTypes = true    # 启用扩展类型
TimeStampPrecision = 1        # 时标精度(ms)
SupportDualNetwork = true     # 支持双网
AutoSwitchNetwork = true      # 自动切换网络

# 性能参数
MaxFrameSize = 255      # 最大帧长度
MaxRetries = 3          # 最大重试次数
ResponseTimeout = 5000  # 响应超时(ms)
IdleTimeout = 20000     # 空闲超时(ms)
```

## 双网冗余支持

### 1. 双网配置
```cpp
struct DualNetworkConfig {
    // 主网配置
    NetworkConfig primaryNet;
    
    // 备网配置
    NetworkConfig backupNet;
    
    // 切换策略
    SwitchStrategy strategy;
    
    // 心跳参数
    HeartbeatParams heartbeat;
};
```

### 2. 自动切换机制
- **心跳检测**：定期心跳监测
- **故障检测**：通信故障检测
- **无缝切换**：主备网无缝切换
- **状态同步**：双网状态同步

## 使用示例

### 基本使用流程
```cpp
// 1. 创建南网V3客户端
auto nwClient = std::make_unique<CNw103V3MasterClient>();

// 2. 加载配置
Nw103V3Config config;
config.LoadFromFile("nw103v3_config.ini");

// 3. 初始化连接
nwClient->Initialize(config);

// 4. 连接装置（支持双网）
nwClient->ConnectDevice("192.168.1.100", 2404);  // 主网
nwClient->ConnectBackup("192.168.2.100", 2404);  // 备网

// 5. 执行总召唤
nwClient->GeneralInterrogation(1);

// 6. 订阅实时数据
nwClient->Subscribe([](const Nw103V3Data& data) {
    // 处理南网V3格式数据
    if (data.typeId >= 128) {  // 扩展类型
        ProcessExtendedData(data);
    } else {
        ProcessStandardData(data);
    }
});

// 7. 读取录波数据
RecorderRequest req;
req.startTime = GetStartTime();
req.endTime = GetEndTime();
nwClient->ReadRecorderData(req);
```

## 错误处理

### 南网V3特定错误码
- `0x2001`: 扩展类型不支持
- `0x2002`: 时标格式错误
- `0x2003`: 双网切换失败
- `0x2004`: 录波数据传输错误
- `0x2005`: 南网校验失败

## 注意事项
- 严格遵守南网V3规范
- 正确处理扩展类型
- 注意时标精度要求
- 合理配置双网参数
- 及时处理录波数据

## 相关模块
- ZcsNw103MsCliPro: 南网103主站客户端
- ZcsNw103MmCliPro: 南网103主主站客户端
- ZcsPro103SrvFlow: 103服务端流程
