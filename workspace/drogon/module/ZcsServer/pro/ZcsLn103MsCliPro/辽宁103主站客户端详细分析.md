# ZcsLn103MsCliPro - 辽宁电网103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsLn103MsCliPro
- **主要功能**: 实现辽宁电网IEC103协议主站客户端，符合辽宁电网特定要求
- **设计目的**: 满足辽宁电网对103协议的定制化需求，适配辽宁电网设备特性

## 核心组件

### 1. 辽宁电网特殊要求
- **防冻控制**：针对东北地区的防冻特殊控制
- **双套配置**：支持双套保护配置切换
- **扩展遥测**：增加温度、湿度等环境监测
- **冬季模式**：特殊的冬季运行模式

### 2. 客户端架构
```cpp
class CLn103Client {
public:
    // 初始化辽宁客户端
    int Initialize(const LnConfig& config);
    
    // 连接辽宁设备
    int ConnectLnDevice(const std::string& ip, int port);
    
    // 辽宁总召唤
    int LnGeneralInterrogation();
    
    // 防冻控制
    int AntiFreezingControl(const AFControlCmd& cmd);
    
    // 双套切换
    int SwitchProtectionSet(int setNo);
    
    // 读取环境数据
    int ReadEnvironmentData(EnvData& data);
    
private:
    // 辽宁配置
    LnConfig m_config;
    
    // 防冻控制器
    AntiFreezingController m_afController;
};
```

### 3. 辽宁扩展数据类型
```cpp
namespace LnExtTypes {
    // 辽宁功能类型
    enum LnFunctionType {
        // 标准功能类型
        STANDARD_BASE = 0,
        
        // 辽宁扩展功能类型
        LN_ENV_MONITOR = 170,       // 环境监测
        LN_ANTI_FREEZE = 171,       // 防冻控制
        LN_DUAL_PROTECT = 172,      // 双套保护
        LN_WINTER_MODE = 173,       // 冬季模式
        LN_HEATING_CTRL = 174,      // 加热控制
        LN_ICE_DETECT = 175         // 覆冰检测
    };
    
    // 辽宁信息序号
    enum LnInfoNumber {
        // 环境监测信息
        LN_TEMPERATURE = 250,       // 温度
        LN_HUMIDITY = 251,          // 湿度
        LN_WIND_SPEED = 252,        // 风速
        LN_WIND_DIRECTION = 253,    // 风向
        LN_ICE_THICKNESS = 254,     // 覆冰厚度
        
        // 防冻信息
        LN_HEATER_STATUS = 255,     // 加热器状态
        LN_FREEZE_ALARM = 256,      // 防冻告警
        LN_TEMP_ALARM = 257         // 温度告警
    };
}
```

## 防冻控制实现

### 1. 防冻控制器
```cpp
class CAntiFreezingController {
public:
    // 防冻策略
    struct AFStrategy {
        float triggerTemp;          // 触发温度
        float releaseTemp;          // 释放温度
        int heatingPower;           // 加热功率
        int heatingDuration;        // 加热时长
        bool autoMode;              // 自动模式
    };
    
    // 启动防冻控制
    int StartAntiFreeze() {
        // 1. 检查环境温度
        float temp = GetEnvironmentTemp();
        
        // 2. 判断是否需要防冻
        if (temp < m_strategy.triggerTemp) {
            // 3. 启动加热器
            StartHeater(m_strategy.heatingPower);
            
            // 4. 设置定时器
            SetTimer(m_strategy.heatingDuration);
            
            // 5. 记录防冻日志
            LogAntiFreeze(temp);
        }
        
        return SUCCESS;
    }
    
    // 停止防冻控制
    int StopAntiFreeze() {
        StopHeater();
        CancelTimer();
        return SUCCESS;
    }
    
    // 自动防冻模式
    int AutoAntiFreeze() {
        while (m_strategy.autoMode) {
            MonitorTemperature();
            AdjustHeating();
            std::this_thread::sleep_for(std::chrono::seconds(60));
        }
        return SUCCESS;
    }
    
private:
    AFStrategy m_strategy;
    HeaterController m_heater;
};
```

### 2. 覆冰检测
```cpp
class CIceDetector {
public:
    // 覆冰数据
    struct IceData {
        float thickness;            // 覆冰厚度(mm)
        float weight;              // 覆冰重量(kg/m)
        float tension;             // 导线张力(N)
        int iceType;               // 覆冰类型
        int alarmLevel;            // 告警级别
    };
    
    // 检测覆冰
    int DetectIcing(IceData& data) {
        // 1. 读取传感器数据
        ReadIceSensors(data);
        
        // 2. 计算覆冰参数
        CalculateIceParameters(data);
        
        // 3. 判断告警级别
        DetermineAlarmLevel(data);
        
        // 4. 触发防冰措施
        if (data.alarmLevel > 2) {
            TriggerAntiIcing();
        }
        
        return SUCCESS;
    }
    
    // 融冰控制
    int MeltingControl(int method) {
        switch (method) {
            case THERMAL_MELTING:
                return ThermalMelting();
            case MECHANICAL_MELTING:
                return MechanicalMelting();
            case DC_MELTING:
                return DCMelting();
            default:
                return ERR_INVALID_METHOD;
        }
    }
    
private:
    std::vector<IceSensor> m_sensors;
};
```

## 双套保护管理

### 1. 双套配置
```cpp
class CDualProtectionManager {
public:
    // 保护定值组
    struct ProtectionSet {
        int setNo;                  // 定值组号
        std::string name;           // 定值组名称
        bool isActive;             // 是否激活
        std::map<int, float> settings; // 定值参数
        uint32_t checksum;         // 校验和
    };
    
    // 切换定值组
    int SwitchProtectionSet(int targetSet) {
        // 1. 验证目标定值组
        if (!ValidateSet(targetSet)) {
            return ERR_INVALID_SET;
        }
        
        // 2. 备份当前定值
        BackupCurrentSet();
        
        // 3. 下装新定值
        DownloadSet(targetSet);
        
        // 4. 验证切换结果
        if (!VerifySwitching()) {
            RollbackSet();
            return ERR_SWITCH_FAILED;
        }
        
        // 5. 更新状态
        UpdateSetStatus(targetSet);
        
        return SUCCESS;
    }
    
    // 定值比对
    int CompareSettings(int set1, int set2) {
        auto settings1 = GetSettings(set1);
        auto settings2 = GetSettings(set2);
        
        // 比对差异
        std::vector<SettingDiff> diffs;
        for (const auto& [key, value1] : settings1) {
            if (settings2[key] != value1) {
                diffs.push_back({key, value1, settings2[key]});
            }
        }
        
        return ReportDifferences(diffs);
    }
    
private:
    std::map<int, ProtectionSet> m_protectionSets;
    int m_activeSet;
};
```

### 2. 冬季运行模式
```cpp
class CWinterModeManager {
public:
    // 冬季模式配置
    struct WinterConfig {
        bool enabled;               // 是否启用
        float lowTempThreshold;     // 低温阈值
        float highWindThreshold;    // 大风阈值
        int heatingSchedule[24];    // 24小时加热计划
        bool emergencyMode;         // 应急模式
    };
    
    // 启用冬季模式
    int EnableWinterMode() {
        // 1. 加载冬季配置
        LoadWinterConfig();
        
        // 2. 调整保护定值
        AdjustProtectionForWinter();
        
        // 3. 启动环境监测
        StartEnvironmentMonitor();
        
        // 4. 启用防冻策略
        EnableAntiFreezingStrategy();
        
        // 5. 设置加热计划
        SetHeatingSchedule();
        
        return SUCCESS;
    }
    
    // 应急处理
    int EmergencyHandler(const EmergencyEvent& event) {
        switch (event.type) {
            case BLIZZARD:
                return HandleBlizzard();
            case EXTREME_COLD:
                return HandleExtremeCold();
            case ICE_STORM:
                return HandleIceStorm();
            default:
                return SUCCESS;
        }
    }
    
private:
    WinterConfig m_winterConfig;
    EmergencyPlan m_emergencyPlan;
};
```

## 环境监测

### 1. 环境数据采集
```cpp
class CEnvironmentMonitor {
public:
    // 环境数据结构
    struct EnvironmentData {
        // 温度数据
        float ambientTemp;          // 环境温度
        float deviceTemp;           // 设备温度
        float cableTemp;            // 电缆温度
        
        // 湿度数据
        float humidity;             // 相对湿度
        float dewPoint;             // 露点温度
        
        // 风力数据
        float windSpeed;            // 风速
        int windDirection;          // 风向
        float maxGust;              // 最大阵风
        
        // 其他环境参数
        float pressure;             // 气压
        float visibility;           // 能见度
        int weatherCode;            // 天气代码
    };
    
    // 采集环境数据
    int CollectEnvironmentData(EnvironmentData& data) {
        // 读取各类传感器
        ReadTemperatureSensors(data);
        ReadHumiditySensor(data);
        ReadWindSensors(data);
        ReadWeatherStation(data);
        
        // 数据有效性检查
        ValidateEnvironmentData(data);
        
        return SUCCESS;
    }
    
    // 环境告警
    int CheckEnvironmentAlarms(const EnvironmentData& data) {
        std::vector<EnvAlarm> alarms;
        
        // 温度告警
        if (data.ambientTemp < -40) {
            alarms.push_back({EXTREME_LOW_TEMP, CRITICAL});
        }
        
        // 覆冰告警
        if (data.humidity > 90 && data.ambientTemp < 0) {
            alarms.push_back({ICE_RISK, WARNING});
        }
        
        // 大风告警
        if (data.windSpeed > 30) {
            alarms.push_back({HIGH_WIND, WARNING});
        }
        
        return ProcessAlarms(alarms);
    }
    
private:
    std::vector<Sensor> m_sensors;
    AlarmProcessor m_alarmProcessor;
};
```

## 配置参数

### 辽宁103配置
```ini
[Ln103_Config]
# 基本通信参数
DeviceAddress = 1           # 装置地址
LinkAddress = 1             # 链路地址
BaudRate = 9600            # 波特率
DataBits = 8               # 数据位
StopBits = 1               # 停止位
Parity = E                 # 校验

# 辽宁扩展参数
LnStationCode = 2101       # 辽宁站点代码
LnAreaCode = 024           # 辽宁地区代码
EnableLnExtension = true   # 启用辽宁扩展
LnProtocolVersion = 3.0    # 辽宁协议版本

# 防冻控制参数
AntiFreezeTrigger = -10    # 防冻触发温度(℃)
AntiFreezeRelease = 5      # 防冻释放温度(℃)
HeatingPower = 500         # 加热功率(W)
HeatingDuration = 3600     # 加热时长(秒)
AutoAntiFreeze = true      # 自动防冻

# 覆冰检测参数
IceDetectPeriod = 300      # 覆冰检测周期(秒)
IceAlarmThreshold = 10     # 覆冰告警阈值(mm)
MeltingMethod = THERMAL    # 融冰方式

# 双套保护参数
DualProtectionEnabled = true   # 启用双套保护
ActiveSetNo = 1               # 当前定值组
BackupSetNo = 2               # 备用定值组
AutoSwitchEnabled = false     # 自动切换

# 冬季模式参数
WinterModeEnabled = true      # 启用冬季模式
WinterStartMonth = 11          # 冬季开始月份
WinterEndMonth = 3            # 冬季结束月份
EmergencyModeEnabled = true   # 启用应急模式

# 环境监测参数
EnvMonitorPeriod = 60         # 环境监测周期(秒)
TempSensorCount = 4           # 温度传感器数量
WindSensorEnabled = true      # 启用风速监测
WeatherStationEnabled = true  # 启用气象站
```

## 错误处理

### 辽宁特定错误码
```cpp
enum LnErrorCode {
    LN_ERR_FREEZE_CTRL = 0x4001,     // 防冻控制错误
    LN_ERR_ICE_DETECT = 0x4002,      // 覆冰检测错误
    LN_ERR_DUAL_PROTECT = 0x4003,    // 双套保护错误
    LN_ERR_WINTER_MODE = 0x4004,     // 冬季模式错误
    LN_ERR_ENV_SENSOR = 0x4005,      // 环境传感器错误
    LN_ERR_HEATER = 0x4006,          // 加热器错误
    LN_ERR_EMERGENCY = 0x4007        // 应急处理错误
};
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建辽宁客户端
auto lnClient = std::make_unique<CLn103Client>();

// 2. 加载配置
LnConfig config;
config.LoadFromFile("ln103_config.ini");
lnClient->Initialize(config);

// 3. 连接设备
lnClient->ConnectLnDevice("192.168.1.100", 2404);

// 4. 执行总召
lnClient->LnGeneralInterrogation();

// 5. 读取环境数据
EnvironmentData envData;
lnClient->ReadEnvironmentData(envData);
std::cout << "Temperature: " << envData.ambientTemp << "℃" << std::endl;
std::cout << "Wind Speed: " << envData.windSpeed << "m/s" << std::endl;

// 6. 防冻控制
if (envData.ambientTemp < -10) {
    AFControlCmd afCmd;
    afCmd.action = START_HEATING;
    afCmd.power = 500;
    lnClient->AntiFreezingControl(afCmd);
}

// 7. 覆冰检测
IceData iceData;
lnClient->DetectIcing(iceData);
if (iceData.thickness > 10) {
    std::cout << "Ice warning! Thickness: " << iceData.thickness << "mm" << std::endl;
}

// 8. 切换保护定值组(冬季)
lnClient->SwitchProtectionSet(2);  // 切换到冬季定值组

// 9. 启用冬季模式
lnClient->EnableWinterMode();
```

## 注意事项
- 重点关注防冻和覆冰问题
- 合理设置环境监测参数
- 注意双套保护切换时机
- 冬季模式下调整运行策略
- 及时处理环境告警

## 相关模块
- ZcsHuBei103MsCliPro: 湖北103主站客户端
- ZcsHlj103MsCliPro: 黑龙江103主站客户端
- ZcsFuJian103MsCliPro: 福建103主站客户端
