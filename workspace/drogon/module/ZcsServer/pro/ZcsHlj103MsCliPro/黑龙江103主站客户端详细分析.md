# ZcsHlj103MsCliPro - 黑龙江电网103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsHlj103MsCliPro
- **主要功能**: 实现黑龙江电网IEC103协议主站客户端，适应极寒地区电网特性
- **设计目的**: 满足黑龙江电网在极寒环境下的特殊运行需求

## 核心组件

### 1. 黑龙江电网特殊要求
- **极寒防护**：-40℃以下的极寒防护措施
- **雪载监测**：实时雪载荷监测与告警
- **供暖保障**：变电站供暖系统监控
- **防凝措施**：SF6气体防液化监测

### 2. 客户端架构
```cpp
class CHlj103Client {
public:
    // 初始化黑龙江客户端
    int Initialize(const HljConfig& config);
    
    // 极寒模式控制
    int ExtremeColdModeControl(const ECModeCmd& cmd);
    
    // 雪载监测
    int SnowLoadMonitoring(SnowLoadData& data);
    
    // 供暖系统控制
    int HeatingSystemControl(const HeatingCmd& cmd);
    
    // SF6监测
    int SF6Monitoring(SF6Data& data);
    
    // 紧急除冰
    int EmergencyDeicing(const DeicingCmd& cmd);
    
private:
    // 黑龙江配置
    HljConfig m_config;
    
    // 极寒控制器
    ExtremeColdController m_ecController;
    
    // 雪载监测器
    SnowLoadMonitor m_snowMonitor;
};
```

### 3. 黑龙江扩展数据类型
```cpp
namespace HljExtTypes {
    // 黑龙江功能类型
    enum HljFunctionType {
        // 标准功能类型
        STANDARD_BASE = 0,
        
        // 黑龙江扩展功能类型
        HLJ_EXTREME_COLD = 180,     // 极寒监测
        HLJ_SNOW_LOAD = 181,        // 雪载监测
        HLJ_HEATING_SYS = 182,      // 供暖系统
        HLJ_SF6_MONITOR = 183,      // SF6监测
        HLJ_ICE_MELTING = 184,      // 融冰系统
        HLJ_EMERGENCY = 185         // 应急系统
    };
    
    // 黑龙江信息序号
    enum HljInfoNumber {
        // 极寒监测信息
        HLJ_EXTREME_TEMP = 260,     // 极端温度
        HLJ_COLD_ALARM = 261,       // 严寒告警
        HLJ_FREEZE_STATUS = 262,    // 冻结状态
        
        // 雪载信息
        HLJ_SNOW_WEIGHT = 263,      // 雪载重量
        HLJ_SNOW_THICKNESS = 264,   // 积雪厚度
        HLJ_SNOW_ALARM = 265,       // 雪载告警
        
        // SF6信息
        HLJ_SF6_PRESSURE = 266,     // SF6压力
        HLJ_SF6_DENSITY = 267,      // SF6密度
        HLJ_SF6_LEAKAGE = 268       // SF6泄漏
    };
}
```

## 极寒防护实现

### 1. 极寒模式控制器
```cpp
class CExtremeColdController {
public:
    // 极寒防护策略
    struct ECStrategy {
        float criticalTemp;         // 临界温度(-40℃)
        float emergencyTemp;        // 应急温度(-45℃)
        int heatingLevel;           // 加热级别(1-5)
        bool autoProtection;        // 自动防护
        int checkInterval;          // 检查间隔(分钟)
    };
    
    // 启动极寒防护
    int StartExtremeColdProtection() {
        // 1. 获取当前温度
        float currentTemp = GetCurrentTemperature();
        
        // 2. 判断防护级别
        int protectionLevel = DetermineProtectionLevel(currentTemp);
        
        // 3. 执行防护措施
        switch (protectionLevel) {
            case 1:  // 一级防护(-30℃ ~ -35℃)
                return Level1Protection();
            case 2:  // 二级防护(-35℃ ~ -40℃)
                return Level2Protection();
            case 3:  // 三级防护(-40℃ ~ -45℃)
                return Level3Protection();
            case 4:  // 应急防护(< -45℃)
                return EmergencyProtection();
            default:
                return SUCCESS;
        }
    }
    
    // 设备预热管理
    int DevicePreheating() {
        // 预热断路器机构
        PreheatingCircuitBreaker();
        
        // 预热隔离开关
        PreheatingDisconnector();
        
        // 预热二次设备
        PreheatingSecondaryDevice();
        
        return SUCCESS;
    }
    
private:
    ECStrategy m_strategy;
    std::vector<HeatingDevice> m_heatingDevices;
};
```

### 2. 雪载监测系统
```cpp
class CSnowLoadMonitor {
public:
    // 雪载数据
    struct SnowLoadData {
        float totalWeight;          // 总重量(kg)
        float distribution[10];     // 分布情况
        float maxLoad;             // 最大载荷
        float safetyFactor;        // 安全系数
        int alarmLevel;            // 告警级别
        bool needCleaning;         // 需要清雪
    };
    
    // 实时监测雪载
    int MonitorSnowLoad(SnowLoadData& data) {
        // 1. 读取载荷传感器
        ReadLoadSensors(data);
        
        // 2. 计算雪载分布
        CalculateLoadDistribution(data);
        
        // 3. 评估安全性
        EvaluateSafety(data);
        
        // 4. 触发告警
        if (data.safetyFactor < 1.5) {
            TriggerSnowLoadAlarm(data.alarmLevel);
        }
        
        // 5. 自动清雪决策
        if (data.needCleaning) {
            InitiateSnowCleaning();
        }
        
        return SUCCESS;
    }
    
    // 预测雪载趋势
    int PredictSnowLoadTrend(int hours) {
        // 获取天气预报
        WeatherForecast forecast = GetWeatherForecast(hours);
        
        // 计算预期雪载
        float expectedLoad = CalculateExpectedLoad(forecast);
        
        // 生成预警
        if (expectedLoad > m_maxAllowedLoad * 0.8) {
            GenerateEarlyWarning(expectedLoad);
        }
        
        return SUCCESS;
    }
    
private:
    std::vector<LoadSensor> m_loadSensors;
    float m_maxAllowedLoad;
};
```

### 3. 供暖系统管理
```cpp
class CHeatingSystemManager {
public:
    // 供暖系统状态
    struct HeatingStatus {
        float indoorTemp;           // 室内温度
        float outdoorTemp;          // 室外温度
        float supplyTemp;           // 供水温度
        float returnTemp;           // 回水温度
        float flowRate;             // 流量
        bool pumpRunning;           // 水泵运行
        bool boilerRunning;         // 锅炉运行
        int faultCode;              // 故障代码
    };
    
    // 自动供暖控制
    int AutoHeatingControl() {
        HeatingStatus status;
        GetHeatingStatus(status);
        
        // 1. 温度控制策略
        if (status.outdoorTemp < -30) {
            // 极寒模式：最大功率供暖
            SetHeatingMode(EXTREME_COLD_MODE);
            SetSupplyTemp(85);  // 85℃供水
        } else if (status.outdoorTemp < -20) {
            // 严寒模式：高功率供暖
            SetHeatingMode(SEVERE_COLD_MODE);
            SetSupplyTemp(75);  // 75℃供水
        } else if (status.outdoorTemp < -10) {
            // 普通模式：正常供暖
            SetHeatingMode(NORMAL_MODE);
            SetSupplyTemp(65);  // 65℃供水
        }
        
        // 2. 故障处理
        if (status.faultCode != 0) {
            HandleHeatingFault(status.faultCode);
        }
        
        return SUCCESS;
    }
    
    // 应急供暖
    int EmergencyHeating() {
        // 启动所有备用加热器
        StartAllBackupHeaters();
        
        // 最大功率运行
        SetHeatingPower(MAX_POWER);
        
        // 关闭非关键区域供暖
        OptimizeHeatingDistribution();
        
        return SUCCESS;
    }
    
private:
    HeatingEquipment m_equipment;
    HeatingStrategy m_strategy;
};
```

## SF6气体监测

### 1. SF6防液化监测
```cpp
class CSF6Monitor {
public:
    // SF6监测数据
    struct SF6Data {
        float pressure;             // 压力(MPa)
        float density;              // 密度(kg/m³)
        float temperature;          // 温度(℃)
        float dewPoint;             // 露点(℃)
        float leakageRate;          // 泄漏率
        bool liquefactionRisk;      // 液化风险
    };
    
    // 监测SF6状态
    int MonitorSF6Status(SF6Data& data) {
        // 1. 读取传感器数据
        ReadSF6Sensors(data);
        
        // 2. 计算液化风险
        CalculateLiquefactionRisk(data);
        
        // 3. 泄漏检测
        DetectLeakage(data);
        
        // 4. 防液化措施
        if (data.liquefactionRisk) {
            // 启动加热
            StartSF6Heating();
            
            // 调整压力
            AdjustSF6Pressure(data.pressure);
            
            // 发出告警
            IssueLiquefactionWarning();
        }
        
        return SUCCESS;
    }
    
    // SF6补气控制
    int SF6Refilling(float targetPressure) {
        // 检查当前压力
        float currentPressure = GetCurrentPressure();
        
        // 计算补气量
        float refillAmount = CalculateRefillAmount(
            currentPressure, targetPressure);
        
        // 执行补气
        return ExecuteRefilling(refillAmount);
    }
    
private:
    std::vector<SF6Sensor> m_sf6Sensors;
    SF6HeatingSystem m_heatingSystem;
};
```

## 应急处理系统

### 1. 暴雪应急
```cpp
class CBlizzardEmergency {
public:
    // 暴雪应急响应
    int BlizzardResponse(const BlizzardInfo& info) {
        // 1. 评估影响
        int impactLevel = AssessBlizzardImpact(info);
        
        // 2. 启动应急预案
        switch (impactLevel) {
            case LEVEL_I:   // 特别重大
                return ExecuteLevelIResponse();
            case LEVEL_II:  // 重大
                return ExecuteLevelIIResponse();
            case LEVEL_III: // 较大
                return ExecuteLevelIIIResponse();
            case LEVEL_IV:  // 一般
                return ExecuteLevelIVResponse();
        }
        
        return SUCCESS;
    }
    
    // 线路除冰
    int LineDeicing(int lineId) {
        // 直流融冰
        if (HasDCMeltingDevice(lineId)) {
            return DCMelting(lineId);
        }
        
        // 交流融冰
        if (HasACMeltingDevice(lineId)) {
            return ACMelting(lineId);
        }
        
        // 机械除冰
        return MechanicalDeicing(lineId);
    }
    
private:
    EmergencyPlan m_emergencyPlan;
    DeicingEquipment m_deicingEquip;
};
```

## 配置参数

### 黑龙江103配置
```ini
[Hlj103_Config]
# 基本通信参数
DeviceAddress = 1           # 装置地址
LinkAddress = 1             # 链路地址
BaudRate = 9600            # 波特率

# 黑龙江扩展参数
HljStationCode = 2301       # 黑龙江站点代码
HljAreaCode = 0451          # 黑龙江地区代码
EnableHljExtension = true   # 启用黑龙江扩展
HljProtocolVersion = 2.5    # 黑龙江协议版本

# 极寒防护参数
ExtremeColdThreshold = -40  # 极寒阈值(℃)
EmergencyThreshold = -45    # 应急阈值(℃)
PreheatingTemp = -25        # 预热启动温度(℃)
HeatingPowerMax = 1000      # 最大加热功率(kW)

# 雪载监测参数
SnowLoadMax = 5000          # 最大雪载(kg)
SnowAlarmThreshold = 3500   # 雪载告警阈值(kg)
AutoCleaningEnabled = true  # 自动清雪
SnowMonitorPeriod = 300     # 监测周期(秒)

# SF6监测参数
SF6PressureMin = 0.4        # 最小压力(MPa)
SF6HeatingTemp = -35        # 加热启动温度(℃)
SF6MonitorPeriod = 600      # 监测周期(秒)
LiquefactionTemp = -40      # 液化温度(℃)

# 供暖系统参数
HeatingAutoControl = true   # 自动控制
IndoorTempTarget = 18       # 目标室温(℃)
SupplyTempMax = 90          # 最高供水温度(℃)
EmergencyHeatingPower = 500 # 应急加热功率(kW)

# 应急参数
EmergencyPlanEnabled = true # 启用应急预案
DeicingMethod = DC          # 除冰方式(DC/AC/MECH)
EmergencyResponseTime = 30  # 应急响应时间(分钟)
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建黑龙江客户端
auto hljClient = std::make_unique<CHlj103Client>();

// 2. 加载配置
HljConfig config;
config.LoadFromFile("hlj103_config.ini");
hljClient->Initialize(config);

// 3. 连接设备
hljClient->Connect("192.168.1.100", 2404);

// 4. 检查极寒状态
float temp = hljClient->GetTemperature();
if (temp < -40) {
    // 启动极寒防护
    ECModeCmd ecCmd;
    ecCmd.mode = EXTREME_COLD_PROTECTION;
    ecCmd.level = 3;
    hljClient->ExtremeColdModeControl(ecCmd);
}

// 5. 监测雪载
SnowLoadData snowData;
hljClient->SnowLoadMonitoring(snowData);
if (snowData.totalWeight > 3500) {
    std::cout << "Snow load warning: " << snowData.totalWeight << "kg" << std::endl;
    // 启动清雪
    hljClient->InitiateSnowCleaning();
}

// 6. SF6监测
SF6Data sf6Data;
hljClient->SF6Monitoring(sf6Data);
if (sf6Data.liquefactionRisk) {
    std::cout << "SF6 liquefaction risk detected!" << std::endl;
    // 启动SF6加热
    hljClient->StartSF6Heating();
}

// 7. 供暖控制
HeatingCmd heatCmd;
heatCmd.mode = AUTO_CONTROL;
heatCmd.targetTemp = 18;
hljClient->HeatingSystemControl(heatCmd);

// 8. 应急除冰
if (hljClient->DetectIcing()) {
    DeicingCmd deiceCmd;
    deiceCmd.method = DC_MELTING;
    deiceCmd.lineId = 1;
    hljClient->EmergencyDeicing(deiceCmd);
}
```

## 注意事项
- 极寒环境下设备预热必不可少
- 实时监测雪载防止结构损坏
- SF6防液化是关键安全措施
- 供暖系统必须有冗余备份
- 应急预案要定期演练

## 相关模块
- ZcsLn103MsCliPro: 辽宁103主站客户端
- ZcsHuBei103MsCliPro: 湖北103主站客户端
- ZcsFuJian103MsCliPro: 福建103主站客户端
