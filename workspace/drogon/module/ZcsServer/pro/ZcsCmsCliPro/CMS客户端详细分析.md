# ZcsCmsCliPro - CMS客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsCmsCliPro
- **主要功能**: 实现CMS（集中监控系统）客户端，提供设备监控和管理功能
- **设计目的**: 实现与集中监控系统的通信，支持设备状态监控、告警管理和远程控制

## 核心组件

### 1. CMS客户端架构
```cpp
class CCmsClient {
public:
    // 初始化CMS客户端
    int Initialize(const CmsConfig& config);
    
    // 连接CMS服务器
    int ConnectToCmsServer(const std::string& serverUrl);
    
    // 上报设备状态
    int ReportDeviceStatus(const DeviceStatus& status);
    
    // 上报告警信息
    int ReportAlarm(const AlarmInfo& alarm);
    
    // 接收控制命令
    int ReceiveControlCommand(ControlCommand& cmd);
    
    // 心跳保活
    int SendHeartbeat();
    
private:
    // CMS配置
    CmsConfig m_config;
    
    // 状态管理器
    StatusManager m_statusMgr;
    
    // 告警管理器
    AlarmManager m_alarmMgr;
    
    // 命令处理器
    CommandProcessor m_cmdProcessor;
};
```

### 2. CMS数据模型
```cpp
namespace CmsDataModel {
    // 设备状态
    struct DeviceStatus {
        std::string deviceId;       // 设备ID
        std::string deviceType;     // 设备类型
        std::string deviceName;     // 设备名称
        enum State {
            ONLINE,                 // 在线
            OFFLINE,                // 离线
            FAULT,                  // 故障
            MAINTENANCE             // 维护
        } state;
        std::map<std::string, float> metrics; // 性能指标
        uint64_t timestamp;         // 时间戳
    };
    
    // 告警信息
    struct AlarmInfo {
        std::string alarmId;        // 告警ID
        std::string deviceId;       // 设备ID
        enum Level {
            CRITICAL = 1,           // 严重
            MAJOR = 2,              // 重要
            MINOR = 3,              // 次要
            WARNING = 4             // 警告
        } level;
        std::string alarmType;      // 告警类型
        std::string description;    // 描述
        uint64_t occurTime;         // 发生时间
        uint64_t clearTime;         // 清除时间
    };
}
```

## 监控功能实现

### 1. 设备状态监控
```cpp
class CDeviceMonitor {
public:
    // 采集设备状态
    int CollectDeviceStatus(DeviceStatus& status) {
        // 1. 获取设备基本信息
        GetDeviceInfo(status);
        
        // 2. 采集性能指标
        CollectMetrics(status.metrics);
        
        // 3. 检查设备状态
        CheckDeviceState(status.state);
        
        // 4. 添加时间戳
        status.timestamp = GetCurrentTime();
        
        return SUCCESS;
    }
    
    // 性能指标采集
    int CollectMetrics(std::map<std::string, float>& metrics) {
        // CPU使用率
        metrics["cpu_usage"] = GetCpuUsage();
        
        // 内存使用率
        metrics["memory_usage"] = GetMemoryUsage();
        
        // 磁盘使用率
        metrics["disk_usage"] = GetDiskUsage();
        
        // 网络流量
        metrics["network_in"] = GetNetworkIn();
        metrics["network_out"] = GetNetworkOut();
        
        // 设备温度
        metrics["temperature"] = GetTemperature();
        
        return SUCCESS;
    }
    
    // 状态变化检测
    bool DetectStateChange(const DeviceStatus& current,
                           const DeviceStatus& previous) {
        return current.state != previous.state;
    }
    
private:
    SystemMonitor m_sysMonitor;
    std::map<std::string, DeviceStatus> m_statusCache;
};
```

### 2. 告警管理
```cpp
class CAlarmManager {
public:
    // 生成告警
    int GenerateAlarm(const AlarmCondition& condition,
                     AlarmInfo& alarm) {
        // 1. 检查告警条件
        if (!CheckAlarmCondition(condition)) {
            return SUCCESS;
        }
        
        // 2. 创建告警信息
        alarm.alarmId = GenerateAlarmId();
        alarm.deviceId = condition.deviceId;
        alarm.level = DetermineAlarmLevel(condition);
        alarm.alarmType = condition.type;
        alarm.description = FormatDescription(condition);
        alarm.occurTime = GetCurrentTime();
        
        // 3. 存储告警
        StoreAlarm(alarm);
        
        // 4. 触发告警通知
        NotifyAlarm(alarm);
        
        return SUCCESS;
    }
    
    // 清除告警
    int ClearAlarm(const std::string& alarmId) {
        // 1. 查找告警
        auto alarm = FindAlarm(alarmId);
        if (!alarm) {
            return ERR_ALARM_NOT_FOUND;
        }
        
        // 2. 设置清除时间
        alarm->clearTime = GetCurrentTime();
        
        // 3. 更新告警状态
        UpdateAlarmStatus(alarmId, CLEARED);
        
        // 4. 通知告警清除
        NotifyAlarmCleared(*alarm);
        
        return SUCCESS;
    }
    
    // 告警抑制
    bool SuppressAlarm(const AlarmInfo& alarm) {
        // 检查抑制规则
        for (const auto& rule : m_suppressRules) {
            if (MatchRule(alarm, rule)) {
                return true;
            }
        }
        return false;
    }
    
private:
    std::map<std::string, AlarmInfo> m_activeAlarms;
    std::vector<SuppressionRule> m_suppressRules;
    AlarmNotifier m_notifier;
};
```

### 3. 远程控制
```cpp
class CRemoteControl {
public:
    // 处理控制命令
    int ProcessControlCommand(const ControlCommand& cmd) {
        // 1. 验证命令权限
        if (!VerifyCommandPermission(cmd)) {
            return ERR_NO_PERMISSION;
        }
        
        // 2. 执行命令
        int result = SUCCESS;
        switch (cmd.type) {
            case CMD_RESTART:
                result = RestartDevice(cmd.targetDevice);
                break;
            case CMD_SHUTDOWN:
                result = ShutdownDevice(cmd.targetDevice);
                break;
            case CMD_CONFIG_UPDATE:
                result = UpdateConfiguration(cmd.parameters);
                break;
            case CMD_FIRMWARE_UPDATE:
                result = UpdateFirmware(cmd.firmwareUrl);
                break;
            default:
                result = ERR_UNKNOWN_COMMAND;
        }
        
        // 3. 上报执行结果
        ReportCommandResult(cmd.commandId, result);
        
        return result;
    }
    
    // 批量控制
    int BatchControl(const std::vector<ControlCommand>& commands) {
        std::vector<std::future<int>> futures;
        
        // 异步执行所有命令
        for (const auto& cmd : commands) {
            futures.push_back(std::async(std::launch::async,
                [this, cmd]() { return ProcessControlCommand(cmd); }));
        }
        
        // 等待所有命令完成
        for (auto& future : futures) {
            future.wait();
        }
        
        return SUCCESS;
    }
    
private:
    CommandExecutor m_executor;
    PermissionChecker m_permChecker;
};
```

## 通信协议

### 1. 消息格式
```cpp
class CCmsProtocol {
public:
    // CMS消息头
    struct MessageHeader {
        uint32_t messageId;         // 消息ID
        uint16_t messageType;       // 消息类型
        uint16_t version;           // 协议版本
        uint32_t bodyLength;        // 消息体长度
        uint64_t timestamp;         // 时间戳
    };
    
    // 消息类型
    enum MessageType {
        MSG_REGISTER = 0x0001,      // 注册
        MSG_HEARTBEAT = 0x0002,     // 心跳
        MSG_STATUS = 0x0003,        // 状态上报
        MSG_ALARM = 0x0004,         // 告警上报
        MSG_COMMAND = 0x0005,       // 控制命令
        MSG_RESPONSE = 0x0006,      // 响应
        MSG_FILE = 0x0007,          // 文件传输
        MSG_LOG = 0x0008            // 日志上报
    };
    
    // 编码消息
    int EncodeMessage(const MessageHeader& header,
                     const std::vector<uint8_t>& body,
                     std::vector<uint8_t>& message) {
        // 序列化头部
        SerializeHeader(header, message);
        
        // 添加消息体
        message.insert(message.end(), body.begin(), body.end());
        
        // 计算校验和
        uint32_t checksum = CalculateChecksum(message);
        AppendChecksum(message, checksum);
        
        return SUCCESS;
    }
    
    // 解码消息
    int DecodeMessage(const std::vector<uint8_t>& message,
                     MessageHeader& header,
                     std::vector<uint8_t>& body) {
        // 验证校验和
        if (!VerifyChecksum(message)) {
            return ERR_CHECKSUM_FAILED;
        }
        
        // 解析头部
        ParseHeader(message, header);
        
        // 提取消息体
        ExtractBody(message, header.bodyLength, body);
        
        return SUCCESS;
    }
    
private:
    Serializer m_serializer;
};
```

### 2. 数据上报
```cpp
class CDataReporter {
public:
    // 周期性上报
    int StartPeriodicReporting() {
        m_reportTimer = std::make_unique<Timer>();
        
        // 状态上报：30秒
        m_reportTimer->Schedule([this]() {
            DeviceStatus status;
            m_monitor.CollectDeviceStatus(status);
            ReportDeviceStatus(status);
        }, 30000);
        
        // 性能上报：5分钟
        m_reportTimer->Schedule([this]() {
            PerformanceData perfData;
            CollectPerformanceData(perfData);
            ReportPerformance(perfData);
        }, 300000);
        
        // 日志上报：1小时
        m_reportTimer->Schedule([this]() {
            LogData logs;
            CollectLogs(logs);
            ReportLogs(logs);
        }, 3600000);
        
        return SUCCESS;
    }
    
    // 实时上报
    int RealtimeReport(const EventData& event) {
        // 立即上报重要事件
        if (event.priority == HIGH) {
            return ImmediateReport(event);
        }
        
        // 缓存普通事件
        m_eventCache.Push(event);
        
        // 批量上报
        if (m_eventCache.Size() >= BATCH_SIZE) {
            return BatchReport(m_eventCache);
        }
        
        return SUCCESS;
    }
    
private:
    std::unique_ptr<Timer> m_reportTimer;
    EventCache m_eventCache;
    DeviceMonitor m_monitor;
};
```

## 配置参数

### CMS客户端配置
```ini
[Cms_Config]
# 服务器配置
ServerUrl = https://cms.server.com:8443
BackupServerUrl = https://cms-backup.server.com:8443
DeviceId = DEV_001                 # 设备ID
DeviceType = SWITCH                # 设备类型

# 连接参数
ConnectionTimeout = 30000          # 连接超时(ms)
HeartbeatInterval = 30000          # 心跳间隔(ms)
ReconnectInterval = 5000           # 重连间隔(ms)
MaxReconnectAttempts = 10          # 最大重连次数

# 上报参数
StatusReportInterval = 30000       # 状态上报间隔(ms)
MetricsReportInterval = 300000     # 指标上报间隔(ms)
LogReportInterval = 3600000        # 日志上报间隔(ms)
BatchReportSize = 100              # 批量上报大小

# 告警参数
AlarmQueueSize = 1000              # 告警队列大小
AlarmSuppressionEnabled = true     # 启用告警抑制
AlarmSuppressionWindow = 60000     # 抑制窗口(ms)

# 安全参数
EnableSSL = true                   # 启用SSL
CertificatePath = /etc/cms/cert.pem
PrivateKeyPath = /etc/cms/key.pem
VerifyServerCert = true            # 验证服务器证书

# 性能参数
MaxConcurrentRequests = 10         # 最大并发请求
RequestQueueSize = 1000           # 请求队列大小
ResponseTimeout = 10000            # 响应超时(ms)
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建CMS客户端
auto cmsClient = std::make_unique<CCmsClient>();

// 2. 加载配置
CmsConfig config;
config.LoadFromFile("cms_config.ini");
cmsClient->Initialize(config);

// 3. 连接CMS服务器
cmsClient->ConnectToCmsServer("https://cms.server.com:8443");

// 4. 注册设备
DeviceInfo deviceInfo;
deviceInfo.deviceId = "DEV_001";
deviceInfo.deviceType = "SWITCH";
deviceInfo.version = "1.0.0";
cmsClient->RegisterDevice(deviceInfo);

// 5. 启动状态监控
cmsClient->StartMonitoring();

// 6. 上报设备状态
DeviceStatus status;
status.deviceId = "DEV_001";
status.state = DeviceStatus::ONLINE;
status.metrics["cpu_usage"] = 45.5;
cmsClient->ReportDeviceStatus(status);

// 7. 上报告警
AlarmInfo alarm;
alarm.deviceId = "DEV_001";
alarm.level = AlarmInfo::WARNING;
alarm.alarmType = "HIGH_CPU_USAGE";
alarm.description = "CPU usage exceeds 80%";
cmsClient->ReportAlarm(alarm);

// 8. 处理控制命令
cmsClient->SetCommandHandler([](const ControlCommand& cmd) {
    std::cout << "Received command: " << cmd.type << std::endl;
    // 执行命令
    return ProcessCommand(cmd);
});

// 9. 保持心跳
while (running) {
    cmsClient->SendHeartbeat();
    std::this_thread::sleep_for(std::chrono::seconds(30));
}
```

## 注意事项
- 确保设备ID唯一性
- 合理设置上报间隔
- 注意告警级别划分
- 实现命令权限控制
- 处理网络异常重连

## 相关模块
- ZcsGspCliPro: GSP客户端
- ZcsGspTransHdl: GSP传输处理器
