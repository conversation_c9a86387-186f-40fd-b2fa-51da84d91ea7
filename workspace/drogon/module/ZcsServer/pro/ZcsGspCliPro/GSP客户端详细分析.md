# ZcsGspCliPro - GSP协议客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsGspCliPro
- **主要功能**: 实现GSP（通用服务协议）客户端，提供标准化的数据服务访问
- **设计目的**: 统一不同协议的数据访问接口，实现协议转换和数据集成

## 核心组件

### 1. GSP客户端架构
```cpp
class CGspClient {
public:
    // 初始化GSP客户端
    int Initialize(const GspConfig& config);
    
    // 连接GSP服务器
    int ConnectToGspServer(const std::string& serverUrl);
    
    // 请求数据服务
    int RequestDataService(const ServiceRequest& request);
    
    // 订阅数据变化
    int SubscribeDataChange(const Subscription& sub);
    
    // 执行远程调用
    int RemoteCall(const RpcRequest& rpc);
    
private:
    // GSP协议处理器
    GspProtocolHandler m_handler;
    
    // 服务发现
    ServiceDiscovery m_discovery;
    
    // 数据序列化
    DataSerializer m_serializer;
};
```

### 2. GSP数据模型
```cpp
namespace GspDataModel {
    // 数据类型定义
    enum DataType {
        GSP_BOOL = 1,
        GSP_INT32 = 2,
        GSP_INT64 = 3,
        GSP_FLOAT = 4,
        GSP_DOUBLE = 5,
        GSP_STRING = 6,
        GSP_BINARY = 7,
        GSP_OBJECT = 8,
        GSP_ARRAY = 9
    };
    
    // GSP数据对象
    struct GspDataObject {
        std::string objectId;       // 对象ID
        std::string objectName;     // 对象名称
        DataType type;             // 数据类型
        std::vector<uint8_t> value;// 数据值
        std::map<std::string, std::string> attributes; // 属性
        uint64_t timestamp;        // 时标
    };
}
```

## GSP协议实现

### 1. 服务发现机制
```cpp
class CGspServiceDiscovery {
public:
    // 发现可用服务
    int DiscoverServices(std::vector<ServiceInfo>& services) {
        // 1. 广播服务发现请求
        BroadcastDiscoveryRequest();
        
        // 2. 收集服务响应
        CollectServiceResponses(services);
        
        // 3. 验证服务可用性
        ValidateServices(services);
        
        return SUCCESS;
    }
    
    // 注册服务
    int RegisterService(const ServiceInfo& service) {
        // 向注册中心注册
        return m_registry.Register(service);
    }
    
    // 服务健康检查
    int HealthCheck(const std::string& serviceId) {
        return m_healthChecker.Check(serviceId);
    }
    
private:
    ServiceRegistry m_registry;
    HealthChecker m_healthChecker;
};
```

### 2. 数据序列化
```cpp
class CGspSerializer {
public:
    // 序列化数据
    int Serialize(const GspDataObject& obj, std::vector<uint8_t>& buffer) {
        // 1. 写入头部
        WriteHeader(buffer, obj);
        
        // 2. 写入数据类型
        WriteDataType(buffer, obj.type);
        
        // 3. 写入数据值
        WriteValue(buffer, obj.value);
        
        // 4. 写入属性
        WriteAttributes(buffer, obj.attributes);
        
        return SUCCESS;
    }
    
    // 反序列化数据
    int Deserialize(const std::vector<uint8_t>& buffer, GspDataObject& obj) {
        // 1. 读取头部
        ReadHeader(buffer, obj);
        
        // 2. 读取数据类型
        ReadDataType(buffer, obj.type);
        
        // 3. 读取数据值
        ReadValue(buffer, obj.value);
        
        // 4. 读取属性
        ReadAttributes(buffer, obj.attributes);
        
        return SUCCESS;
    }
    
private:
    // 使用Protocol Buffers或MessagePack
    ProtobufSerializer m_protobuf;
};
```

### 3. 远程过程调用
```cpp
class CGspRpcClient {
public:
    // 同步调用
    int SyncCall(const std::string& method, 
                 const std::vector<GspDataObject>& params,
                 GspDataObject& result) {
        // 1. 构造RPC请求
        RpcRequest request;
        request.method = method;
        request.params = params;
        
        // 2. 发送请求
        SendRequest(request);
        
        // 3. 等待响应
        RpcResponse response;
        WaitForResponse(response);
        
        // 4. 处理结果
        result = response.result;
        
        return SUCCESS;
    }
    
    // 异步调用
    int AsyncCall(const std::string& method,
                  const std::vector<GspDataObject>& params,
                  RpcCallback callback) {
        // 1. 构造异步请求
        AsyncRpcRequest request;
        request.method = method;
        request.params = params;
        request.callback = callback;
        
        // 2. 提交到异步队列
        m_asyncQueue.Push(request);
        
        // 3. 返回请求ID
        return request.requestId;
    }
    
private:
    AsyncQueue<AsyncRpcRequest> m_asyncQueue;
    std::map<int, RpcCallback> m_callbacks;
};
```

## 协议转换

### 1. 多协议适配
```cpp
class CGspProtocolAdapter {
public:
    // 协议转换
    int ConvertProtocol(const ProtocolData& source,
                       ProtocolType targetType,
                       ProtocolData& target) {
        switch (targetType) {
            case PROTOCOL_IEC61850:
                return ConvertToIEC61850(source, target);
            case PROTOCOL_IEC104:
                return ConvertToIEC104(source, target);
            case PROTOCOL_MODBUS:
                return ConvertToModbus(source, target);
            case PROTOCOL_OPC:
                return ConvertToOPC(source, target);
            default:
                return ERR_UNSUPPORTED_PROTOCOL;
        }
    }
    
private:
    // 各协议转换器
    IEC61850Converter m_iec61850Conv;
    IEC104Converter m_iec104Conv;
    ModbusConverter m_modbusConv;
    OPCConverter m_opcConv;
};
```

### 2. 数据映射
```cpp
class CGspDataMapper {
public:
    // 数据映射配置
    struct MappingRule {
        std::string sourceField;    // 源字段
        std::string targetField;    // 目标字段
        std::string transform;      // 转换规则
        float scale;                // 缩放因子
        float offset;              // 偏移量
    };
    
    // 应用映射规则
    int ApplyMapping(const GspDataObject& source,
                     const std::vector<MappingRule>& rules,
                     GspDataObject& target) {
        for (const auto& rule : rules) {
            // 获取源值
            auto sourceValue = GetFieldValue(source, rule.sourceField);
            
            // 应用转换
            auto targetValue = Transform(sourceValue, rule);
            
            // 设置目标值
            SetFieldValue(target, rule.targetField, targetValue);
        }
        
        return SUCCESS;
    }
    
private:
    std::map<std::string, MappingRule> m_mappingRules;
};
```

## 配置参数

### GSP客户端配置
```ini
[Gsp_Config]
# 服务器配置
ServerUrl = http://gsp.server.com:8080
ServiceDiscoveryUrl = http://discovery.server.com:8500
ConnectionTimeout = 30000      # 连接超时(ms)
RequestTimeout = 10000         # 请求超时(ms)

# 协议配置
ProtocolVersion = 2.0          # GSP协议版本
SerializationFormat = PROTOBUF # 序列化格式
CompressionEnabled = true      # 启用压缩
EncryptionEnabled = true       # 启用加密

# 性能配置
ConnectionPoolSize = 10        # 连接池大小
MaxConcurrentRequests = 100    # 最大并发请求
CacheEnabled = true           # 启用缓存
CacheSize = 10000            # 缓存大小

# 重试配置
RetryEnabled = true           # 启用重试
MaxRetries = 3               # 最大重试次数
RetryInterval = 1000         # 重试间隔(ms)
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建GSP客户端
auto gspClient = std::make_unique<CGspClient>();

// 2. 初始化配置
GspConfig config;
config.LoadFromFile("gsp_config.ini");
gspClient->Initialize(config);

// 3. 发现服务
std::vector<ServiceInfo> services;
gspClient->DiscoverServices(services);

// 4. 连接服务器
gspClient->ConnectToGspServer("http://gsp.server.com:8080");

// 5. 请求数据服务
ServiceRequest request;
request.serviceName = "DataQuery";
request.parameters["pointId"] = "1001";
ServiceResponse response;
gspClient->RequestDataService(request, response);

// 6. 订阅数据变化
Subscription sub;
sub.dataPattern = "realtime.*";
sub.callback = [](const GspDataObject& data) {
    std::cout << "Data changed: " << data.objectId << std::endl;
};
gspClient->SubscribeDataChange(sub);

// 7. 远程调用
RpcRequest rpc;
rpc.method = "GetDeviceStatus";
rpc.params.push_back(CreateGspObject("deviceId", "DEV001"));
GspDataObject result;
gspClient->RemoteCall(rpc, result);
```

## 注意事项
- 确保服务发现机制正常工作
- 注意数据序列化格式兼容性
- 合理设置超时和重试参数
- 监控连接池状态
- 处理协议转换异常

## 相关模块
- ZcsGspTransHdl: GSP传输处理器
- ZcsCmsCliPro: CMS客户端
