# pro/common - 协议公共组件详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/common
- **主要功能**: 提供各种协议实现的公共组件和基础设施
- **设计目的**: 复用协议处理的通用功能，统一协议开发接口

## 文件分类

### 1. IEC 60870-5-103协议相关
- **Zx103ProtocolDef.h**: 103协议定义
- **Zx103MsgAttach.h**: 103消息附件
- **Zx103MsgAttachFactory.h**: 103消息工厂
- **Zx103M2MASDUHandler.h**: 103主从ASDU处理
- **Zx103Len2CpuMsgAttach.h**: 103长度2带CPU消息
- **Zx103Len2NoCpuMsgAttach.h**: 103长度2无CPU消息

### 2. IEC 60870-5-104协议相关
- **Zx104MsgAttach.h**: 104消息附件
- **Zx104MsgAttachFactory.h**: 104消息工厂
- **Zx104Len1CpuMsgAttach.h**: 104长度1带CPU消息
- **Zx104Len1NoCpuMsgAttach.h**: 104长度1无CPU消息
- **Zx104Len2CpuMsgAttach.h**: 104长度2带CPU消息

### 3. 佛山104特殊实现
- **Zx104FoShanASDU1.h**: 佛山ASDU类型1
- **Zx104FoShanASDU100.h**: 佛山ASDU类型100
- **Zx104FoShanASDUAutoUp.h**: 佛山自动上送
- **Zx104FoshanMsgAttach.h**: 佛山消息附件
- **Zx104FoshanMsgAttachFactory.h**: 佛山消息工厂

### 4. IEC 61850协议相关
- **iec61850define.h**: 61850协议定义
- **Zx61850DBFunction.h**: 61850数据库功能
- **Zx61850FuncHandle.h**: 61850功能处理
- **Zx61850MD5.h**: 61850 MD5校验

### 5. APCI/ASDU处理
- **ZxAPCIInterFace.h**: APCI接口
- **ZxAPCIWrapper.h**: APCI封装
- **ZxASDUFactory.h**: ASDU工厂
- **ZxASDUHandler.h**: ASDU处理器
- **ZxASDUInterFace.h**: ASDU接口

### 6. 其他公共组件
- **ThreadManger.h**: 线程管理器
- **SftpHdl.h**: SFTP处理
- **GWModelFileCreate.h**: 国网模型文件创建
- **InitDataToXml.h**: 初始化数据到XML
- **IZxGspTransObj.h**: GSP传输对象接口

## 核心组件详细分析

### 1. Zx103ProtocolDef.h - 103协议定义

#### 帧格式定义
```cpp
// 103协议帧结构
struct IEC103_FRAME {
    BYTE start;           // 启动字符 0x68
    BYTE length;          // 长度
    BYTE length_repeat;   // 长度重复
    BYTE start_repeat;    // 启动字符重复 0x68
    BYTE control;         // 控制域
    BYTE address;         // 地址域
    BYTE asdu[250];       // ASDU数据
    BYTE checksum;        // 校验和
    BYTE end;            // 结束字符 0x16
};

// ASDU结构
struct IEC103_ASDU {
    BYTE type;           // 类型标识
    BYTE vsq;            // 可变结构限定词
    BYTE cot;            // 传送原因
    BYTE addr;           // 公共地址
    BYTE fun;            // 功能类型
    BYTE inf;            // 信息序号
    BYTE data[240];      // 数据
};
```

#### 类型标识定义
```cpp
enum IEC103_TYPE_ID {
    M_SP_NA_1 = 1,       // 单点信息
    M_SP_TA_1 = 2,       // 带时标的单点信息
    M_DP_NA_1 = 3,       // 双点信息
    M_DP_TA_1 = 4,       // 带时标的双点信息
    M_ST_NA_1 = 5,       // 步位置信息
    M_ST_TA_1 = 6,       // 带时标的步位置信息
    M_ME_NA_1 = 9,       // 测量值，归一化值
    M_ME_TA_1 = 10,      // 带时标的测量值，归一化值
    // ... 更多类型
};
```

### 2. Zx104MsgAttach.h - 104消息附件

#### 消息附件基类
```cpp
class CZx104MsgAttach {
protected:
    int m_nPointID;              // 点号
    int m_nInfoAddr;             // 信息体地址
    BYTE m_byQuality;            // 品质
    CP56Time2a m_time;           // 时标
    
public:
    // 序列化和反序列化
    virtual int Serialize(BYTE* buffer, int& len) = 0;
    virtual int Deserialize(const BYTE* buffer, int len) = 0;
    
    // 获取和设置属性
    int GetPointID() const { return m_nPointID; }
    void SetPointID(int id) { m_nPointID = id; }
    
    BYTE GetQuality() const { return m_byQuality; }
    void SetQuality(BYTE q) { m_byQuality = q; }
    
    // 时标处理
    void SetTime(const CP56Time2a& t) { m_time = t; }
    CP56Time2a GetTime() const { return m_time; }
};
```

#### 具体消息类型
```cpp
// 单点信息
class CZx104SinglePoint : public CZx104MsgAttach {
private:
    bool m_bValue;               // 单点值
    
public:
    int Serialize(BYTE* buffer, int& len) override {
        // 序列化信息体地址
        memcpy(buffer, &m_nInfoAddr, 3);
        // 序列化单点值和品质
        buffer[3] = (m_bValue ? 0x01 : 0x00) | m_byQuality;
        len = 4;
        return 0;
    }
};

// 测量值
class CZx104MeasuredValue : public CZx104MsgAttach {
private:
    float m_fValue;              // 测量值
    
public:
    int Serialize(BYTE* buffer, int& len) override {
        // 序列化信息体地址
        memcpy(buffer, &m_nInfoAddr, 3);
        // 序列化测量值
        short nValue = (short)(m_fValue * 32768);
        memcpy(buffer + 3, &nValue, 2);
        // 序列化品质
        buffer[5] = m_byQuality;
        len = 6;
        return 0;
    }
};
```

### 3. ZxAPCIWrapper.h - APCI层封装

#### APCI帧类型
```cpp
enum APCI_FRAME_TYPE {
    I_FRAME = 0,         // I帧：信息传输
    S_FRAME = 1,         // S帧：监视功能
    U_FRAME = 3          // U帧：控制功能
};

// U帧功能码
enum U_FRAME_FUNC {
    STARTDT_ACT = 0x07,  // 启动数据传输激活
    STARTDT_CON = 0x0B,  // 启动数据传输确认
    STOPDT_ACT = 0x13,   // 停止数据传输激活
    STOPDT_CON = 0x23,   // 停止数据传输确认
    TESTFR_ACT = 0x43,   // 测试帧激活
    TESTFR_CON = 0x83    // 测试帧确认
};
```

#### APCI封装类
```cpp
class CXJAPCIWrapper {
private:
    WORD m_wSendSeq;             // 发送序号
    WORD m_wRecvSeq;             // 接收序号
    
    // 超时参数
    int m_nT0;                   // 连接建立超时
    int m_nT1;                   // 发送或测试APDU超时
    int m_nT2;                   // 无数据报文时确认超时
    int m_nT3;                   // 测试帧发送间隔
    
public:
    // 创建APCI帧
    int CreateIFrame(BYTE* asdu, int asduLen, BYTE* apci, int& apciLen);
    int CreateSFrame(BYTE* apci, int& apciLen);
    int CreateUFrame(BYTE func, BYTE* apci, int& apciLen);
    
    // 解析APCI帧
    int ParseAPCI(const BYTE* apci, int len, APCI_INFO& info);
    
    // 序号管理
    void IncreaseSendSeq() { m_wSendSeq++; }
    void IncreaseRecvSeq() { m_wRecvSeq++; }
    bool CheckSeqValid(WORD seq);
};
```

### 4. ZxASDUHandler.h - ASDU处理器

#### ASDU处理器基类
```cpp
class CZxASDUHandler {
protected:
    // 处理函数映射
    typedef int (CZxASDUHandler::*ASDU_HANDLER)(const ASDU_MSG& msg);
    map<int, ASDU_HANDLER> m_mapHandlers;
    
public:
    CZxASDUHandler() {
        // 注册处理函数
        RegisterHandlers();
    }
    
    // 处理ASDU
    virtual int HandleASDU(const ASDU_MSG& msg) {
        auto it = m_mapHandlers.find(msg.type);
        if (it != m_mapHandlers.end()) {
            return (this->*(it->second))(msg);
        }
        return -1;
    }
    
protected:
    // 注册处理函数
    virtual void RegisterHandlers() = 0;
    
    // 具体处理函数
    virtual int HandleSinglePoint(const ASDU_MSG& msg) = 0;
    virtual int HandleDoublePoint(const ASDU_MSG& msg) = 0;
    virtual int HandleMeasuredValue(const ASDU_MSG& msg) = 0;
    virtual int HandleIntegratedTotals(const ASDU_MSG& msg) = 0;
    virtual int HandleCommand(const ASDU_MSG& msg) = 0;
};
```

### 5. Zx61850FuncHandle.h - 61850功能处理

#### MMS服务处理
```cpp
class CZx61850FuncHandle {
private:
    IedConnection m_connection;  // IED连接
    IedModel* m_pModel;          // 数据模型
    
public:
    // 连接管理
    int ConnectToIED(const char* hostname, int port);
    int DisconnectFromIED();
    
    // 读写服务
    int ReadDataValues(const char* reference, MmsValue** value);
    int WriteDataValues(const char* reference, MmsValue* value);
    
    // 数据集操作
    int ReadDataSet(const char* dataSetRef, ClientDataSet* dataSet);
    int CreateDataSet(const char* dataSetRef, LinkedList members);
    int DeleteDataSet(const char* dataSetRef);
    
    // 报告处理
    int GetRCBValues(const char* rcbRef, ClientReportControlBlock* rcb);
    int SetRCBValues(const char* rcbRef, ClientReportControlBlock* rcb);
    int InstallReportHandler(const char* rcbRef, ReportHandler handler);
    
    // 控制操作
    int SelectWithValue(const char* ctlRef, MmsValue* ctlVal);
    int Operate(const char* ctlRef, MmsValue* ctlVal);
    int Cancel(const char* ctlRef);
    
    // GOOSE处理
    int SubscribeGOOSE(const char* goRef, GooseHandler handler);
    int UnsubscribeGOOSE(const char* goRef);
    
    // 文件服务
    int GetFileDirectory(const char* directoryName);
    int GetFile(const char* fileName, const char* localFile);
    int DeleteFile(const char* fileName);
};
```

### 6. ThreadManger.h - 线程管理器

#### 线程池实现
```cpp
class CThreadManager {
private:
    struct THREAD_INFO {
        THREAD_HANDLE handle;    // 线程句柄
        THREAD_ID id;           // 线程ID
        bool bRunning;          // 运行状态
        void* pParam;           // 参数
    };
    
    vector<THREAD_INFO> m_vecThreads;  // 线程列表
    queue<TASK_INFO> m_queueTasks;     // 任务队列
    CXJLock m_lockQueue;               // 队列锁
    HNSEM m_semTask;                   // 任务信号量
    
public:
    // 初始化线程池
    int InitThreadPool(int nThreadCount);
    
    // 添加任务
    int AddTask(TASK_FUNC func, void* param);
    
    // 启动/停止
    int Start();
    int Stop();
    
    // 工作线程函数
    static THREAD_FUNC WorkerThread(LPVOID pParam);
    
private:
    // 获取任务
    bool GetTask(TASK_INFO& task);
    
    // 执行任务
    void ExecuteTask(const TASK_INFO& task);
};
```

## 设计模式应用

### 1. 工厂模式
```cpp
// 消息附件工厂
class CZx104MsgAttachFactory {
public:
    static CZx104MsgAttach* CreateAttach(int nTypeID) {
        switch (nTypeID) {
            case M_SP_NA_1:
                return new CZx104SinglePoint();
            case M_ME_NA_1:
                return new CZx104MeasuredValue();
            case M_IT_NA_1:
                return new CZx104IntegratedTotals();
            default:
                return nullptr;
        }
    }
};
```

### 2. 模板方法模式
```cpp
// ASDU处理模板
class CASDUHandlerTemplate {
public:
    // 模板方法
    int ProcessASDU(const BYTE* data, int len) {
        // 1. 解析ASDU头
        if (!ParseHeader(data, len)) return -1;
        
        // 2. 验证ASDU
        if (!ValidateASDU()) return -1;
        
        // 3. 处理ASDU体
        ProcessBody();
        
        // 4. 生成响应
        GenerateResponse();
        
        return 0;
    }
    
protected:
    // 子类实现
    virtual bool ParseHeader(const BYTE* data, int len) = 0;
    virtual bool ValidateASDU() = 0;
    virtual void ProcessBody() = 0;
    virtual void GenerateResponse() = 0;
};
```

### 3. 策略模式
```cpp
// 协议处理策略
class IProtocolStrategy {
public:
    virtual int Encode(const DATA& data, BYTE* buffer, int& len) = 0;
    virtual int Decode(const BYTE* buffer, int len, DATA& data) = 0;
};

class C103Strategy : public IProtocolStrategy {
    int Encode(const DATA& data, BYTE* buffer, int& len) override {
        // 103协议编码
    }
};

class C104Strategy : public IProtocolStrategy {
    int Encode(const DATA& data, BYTE* buffer, int& len) override {
        // 104协议编码
    }
};
```

## 性能优化

### 1. 内存池
- 预分配消息缓冲区
- 对象池复用
- 减少内存碎片

### 2. 零拷贝
- 直接操作缓冲区
- 避免不必要的内存拷贝
- 使用引用传递

### 3. 批处理
- 批量消息处理
- 批量数据库操作
- 减少系统调用

## 使用示例

### 创建104消息
```cpp
// 创建单点信息
CZx104SinglePoint* sp = new CZx104SinglePoint();
sp->SetPointID(1001);
sp->SetInfoAddr(0x000001);
sp->SetValue(true);
sp->SetQuality(0x00);

// 序列化
BYTE buffer[256];
int len;
sp->Serialize(buffer, len);
```

### 处理ASDU
```cpp
// 创建处理器
CZxASDUHandler* handler = new CMy104ASDUHandler();

// 处理ASDU
ASDU_MSG msg;
msg.type = M_SP_NA_1;
msg.data = buffer;
msg.len = len;

handler->HandleASDU(msg);
```

## 注意事项

1. **协议版本**: 注意不同版本协议的差异
2. **字节序**: 处理网络字节序转换
3. **内存管理**: 正确释放动态分配的内存
4. **线程安全**: 多线程环境下的同步
5. **错误处理**: 完善的异常处理机制
