# ZcsProMainFlow - 协议主流程详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsProMainFlow
- **主要功能**: 提供103协议客户端的主流程控制，包括初始化、控制命令、文件下载等操作
- **设计目的**: 统一管理103协议客户端的各种操作流程，提供标准化的执行框架

## 核心文件分析

### 1. ZxProMainFlow.h/cpp - 主流程入口

#### 类定义
```cpp
class CZxProMainFlow {
private:
    CLIENT_PRO_INTERFACE* m_pProInterface;  // 客户端协议接口
    CZxPro103ClientWay* m_pClientWay;       // 103客户端处理
    
public:
    CZxProMainFlow();
    ~CZxProMainFlow();
    
    // 主运行函数
    int Run(CLIENT_PRO_INTERFACE* pProInterface);
    
    // 流程控制
    int InitFlow();
    int StartFlow();
    int StopFlow();
};
```

#### 动态库导出
```cpp
extern "C" {
#ifdef OS_WINDOWS
    __declspec(dllexport) int Run(CLIENT_PRO_INTERFACE* pProInterface);
#else
    int Run(CLIENT_PRO_INTERFACE* pProInterface);
#endif
}

int Run(CLIENT_PRO_INTERFACE* pProInterface) {
    CZxProMainFlow mainFlow;
    return mainFlow.Run(pProInterface);
}
```

### 2. ZxPro103ClientWay.h/cpp - 103客户端处理方式

#### 类定义
```cpp
class CZxPro103ClientWay {
private:
    // 核心组件
    CLIENT_PRO_INTERFACE& m_ProInterface;   // 协议接口
    CXJAPCIWrapper* m_pAPCIWrapper;         // APCI封装器
    CXJASDUHandler* m_pASDUHandler;         // ASDU处理器
    
    // 操作管理器
    CZxProInitOperation* m_pInitOper;       // 初始化操作
    CZxProCtrlCommandOperation* m_pCtrlOper; // 控制命令操作
    CZxProCFDownloadOperation* m_pCFOper;   // 文件下载操作
    
    // 连接管理
    SOCKET m_socket;                        // 套接字
    bool m_bConnected;                      // 连接状态
    int m_nLinkAddress;                     // 链路地址
    int m_nCommonAddress;                   // 公共地址
    
    // 数据缓存
    CZx103CacheIndex* m_pCacheIndex;        // 缓存索引
    queue<ASDU_MSG> m_sendQueue;            // 发送队列
    queue<ASDU_MSG> m_recvQueue;            // 接收队列
    CXJLock m_lockSend;                     // 发送锁
    CXJLock m_lockRecv;                     // 接收锁
    
    // 状态管理
    enum ClientState {
        STATE_IDLE,                         // 空闲
        STATE_CONNECTING,                   // 连接中
        STATE_CONNECTED,                    // 已连接
        STATE_INITIALIZING,                 // 初始化中
        STATE_RUNNING,                      // 运行中
        STATE_ERROR                         // 错误
    };
    ClientState m_state;                   // 当前状态
    
    // 线程管理
    THREAD_HANDLE m_hRecvThread;           // 接收线程
    THREAD_HANDLE m_hSendThread;           // 发送线程
    THREAD_HANDLE m_hProcessThread;        // 处理线程
    bool m_bExit;                          // 退出标志
    
public:
    CZxPro103ClientWay(CLIENT_PRO_INTERFACE& interface);
    ~CZxPro103ClientWay();
    
    // 生命周期管理
    int Init();
    int Start();
    int Stop();
    int Release();
    
    // 连接管理
    int Connect();
    int Disconnect();
    int Reconnect();
    bool IsConnected() const { return m_bConnected; }
    
    // 数据收发
    int SendASDU(const ASDU_MSG& msg);
    int RecvASDU(ASDU_MSG& msg);
    int ProcessASDU(const ASDU_MSG& msg);
    
    // 操作执行
    int ExecuteInit();           // 执行初始化
    int ExecuteGeneralQuery();   // 执行总查询
    int ExecuteClockSync();      // 执行时钟同步
    int ExecuteControl(const CONTROL_CMD& cmd); // 执行控制
    int ExecuteFileDownload(const string& fileName); // 执行文件下载
    
    // 状态管理
    void SetState(ClientState state);
    ClientState GetState() const { return m_state; }
    
    // 线程函数
    static THREAD_FUNC RecvThread(LPVOID pParam);
    static THREAD_FUNC SendThread(LPVOID pParam);
    static THREAD_FUNC ProcessThread(LPVOID pParam);
    
private:
    // 内部处理
    void RecvLoop();
    void SendLoop();
    void ProcessLoop();
    
    // ASDU处理
    int HandleType1(const ASDU_MSG& msg);  // 带时标的保护事件
    int HandleType2(const ASDU_MSG& msg);  // 带相对时间的保护事件
    int HandleType3(const ASDU_MSG& msg);  // 被测值I
    int HandleType4(const ASDU_MSG& msg);  // 带相对时间的被测值
    int HandleType5(const ASDU_MSG& msg);  // 标识
    int HandleType6(const ASDU_MSG& msg);  // 时间同步
    int HandleType10(const ASDU_MSG& msg); // 通用分类数据
    int HandleType23(const ASDU_MSG& msg); // 扰动数据传输准备就绪
};
```

### 3. ZxProInitOperation.h/cpp - 初始化操作

#### 类定义
```cpp
class CZxProInitOperation : public CZxProOperation {
private:
    // 初始化配置
    struct INIT_CONFIG {
        bool bEnableGeneralQuery;      // 启用总查询
        bool bEnableClockSync;         // 启用时钟同步
        bool bEnableDeviceQuery;       // 启用设备查询
        int nRetryCount;               // 重试次数
        int nTimeout;                  // 超时时间
    };
    INIT_CONFIG m_config;
    
    // 初始化状态
    enum InitStep {
        INIT_STEP_START,               // 开始
        INIT_STEP_LINK_INIT,          // 链路初始化
        INIT_STEP_CLOCK_SYNC,         // 时钟同步
        INIT_STEP_GENERAL_QUERY,      // 总查询
        INIT_STEP_DEVICE_QUERY,       // 设备查询
        INIT_STEP_COMPLETE            // 完成
    };
    InitStep m_currentStep;
    
    // 初始化结果
    struct INIT_RESULT {
        bool bSuccess;                 // 是否成功
        int nPointCount;               // 点数
        int nDeviceCount;              // 设备数
        time_t tCompleteTime;          // 完成时间
    };
    INIT_RESULT m_result;
    
public:
    CZxProInitOperation();
    virtual ~CZxProInitOperation();
    
    // 执行初始化
    virtual int Execute() override;
    
    // 初始化步骤
    int DoLinkInit();
    int DoClockSync();
    int DoGeneralQuery();
    int DoDeviceQuery();
    
    // 配置管理
    void SetConfig(const INIT_CONFIG& config);
    INIT_CONFIG GetConfig() const { return m_config; }
    
    // 结果查询
    INIT_RESULT GetResult() const { return m_result; }
    bool IsComplete() const { return m_currentStep == INIT_STEP_COMPLETE; }
    
    // 重试机制
    int RetryCurrentStep();
    bool CanRetry() const;
};
```

#### 初始化流程实现
```cpp
int CZxProInitOperation::Execute() {
    m_currentStep = INIT_STEP_START;
    
    while (m_currentStep != INIT_STEP_COMPLETE) {
        int ret = 0;
        
        switch (m_currentStep) {
            case INIT_STEP_START:
                m_currentStep = INIT_STEP_LINK_INIT;
                break;
                
            case INIT_STEP_LINK_INIT:
                ret = DoLinkInit();
                if (ret == 0) {
                    m_currentStep = INIT_STEP_CLOCK_SYNC;
                } else if (!RetryCurrentStep()) {
                    return ret;
                }
                break;
                
            case INIT_STEP_CLOCK_SYNC:
                if (m_config.bEnableClockSync) {
                    ret = DoClockSync();
                }
                m_currentStep = INIT_STEP_GENERAL_QUERY;
                break;
                
            case INIT_STEP_GENERAL_QUERY:
                if (m_config.bEnableGeneralQuery) {
                    ret = DoGeneralQuery();
                    if (ret == 0) {
                        m_currentStep = INIT_STEP_DEVICE_QUERY;
                    } else if (!RetryCurrentStep()) {
                        return ret;
                    }
                } else {
                    m_currentStep = INIT_STEP_DEVICE_QUERY;
                }
                break;
                
            case INIT_STEP_DEVICE_QUERY:
                if (m_config.bEnableDeviceQuery) {
                    ret = DoDeviceQuery();
                }
                m_currentStep = INIT_STEP_COMPLETE;
                break;
                
            case INIT_STEP_COMPLETE:
                m_result.bSuccess = true;
                m_result.tCompleteTime = time(NULL);
                break;
        }
    }
    
    return 0;
}
```

### 4. ZxProCtrlCommandOperation.h/cpp - 控制命令操作

#### 类定义
```cpp
class CZxProCtrlCommandOperation : public CZxProOperation {
private:
    // 控制命令
    struct CONTROL_COMMAND {
        int nPointID;                  // 点号
        int nControlType;              // 控制类型
        int nControlValue;             // 控制值
        bool bSelect;                  // 是否选择
        bool bExecute;                 // 是否执行
        time_t tTimeout;               // 超时时间
    };
    CONTROL_COMMAND m_command;
    
    // 控制状态
    enum ControlState {
        CTRL_STATE_IDLE,               // 空闲
        CTRL_STATE_SELECT,             // 选择
        CTRL_STATE_EXECUTE,            // 执行
        CTRL_STATE_CONFIRM,            // 确认
        CTRL_STATE_COMPLETE,           // 完成
        CTRL_STATE_ERROR              // 错误
    };
    ControlState m_state;
    
    // 控制结果
    struct CONTROL_RESULT {
        bool bSuccess;                 // 是否成功
        int nErrorCode;                // 错误码
        string strErrorMsg;            // 错误信息
    };
    CONTROL_RESULT m_result;
    
public:
    CZxProCtrlCommandOperation();
    virtual ~CZxProCtrlCommandOperation();
    
    // 执行控制
    virtual int Execute() override;
    
    // 设置命令
    void SetCommand(const CONTROL_COMMAND& cmd);
    
    // 控制步骤
    int DoSelect();
    int DoExecute();
    int DoConfirm();
    
    // 结果查询
    CONTROL_RESULT GetResult() const { return m_result; }
    
    // 取消控制
    int Cancel();
    
    // 超时处理
    bool IsTimeout() const;
    void HandleTimeout();
};
```

### 5. ZxProCFDownloadOperation.h/cpp - 文件下载操作

#### 类定义
```cpp
class CZxProCFDownloadOperation : public CZxProOperation {
private:
    // 文件信息
    struct FILE_INFO {
        int nFileID;                   // 文件ID
        string strFileName;            // 文件名
        string strFilePath;            // 文件路径
        long nFileSize;                // 文件大小
        time_t tCreateTime;            // 创建时间
    };
    FILE_INFO m_fileInfo;
    
    // 下载状态
    enum DownloadState {
        DL_STATE_IDLE,                 // 空闲
        DL_STATE_REQUEST,              // 请求
        DL_STATE_PREPARE,              // 准备
        DL_STATE_TRANSFER,             // 传输
        DL_STATE_VERIFY,               // 校验
        DL_STATE_COMPLETE,             // 完成
        DL_STATE_ERROR                // 错误
    };
    DownloadState m_state;
    
    // 传输信息
    struct TRANSFER_INFO {
        long nTransferredSize;         // 已传输大小
        int nBlockSize;                // 块大小
        int nCurrentBlock;             // 当前块
        int nTotalBlocks;              // 总块数
        float fProgress;               // 进度
        int nSpeed;                    // 速度(字节/秒)
    };
    TRANSFER_INFO m_transferInfo;
    
    // 文件缓存
    BYTE* m_pFileBuffer;               // 文件缓冲区
    FILE* m_pFile;                     // 文件指针
    
public:
    CZxProCFDownloadOperation();
    virtual ~CZxProCFDownloadOperation();
    
    // 执行下载
    virtual int Execute() override;
    
    // 设置文件信息
    void SetFileInfo(const FILE_INFO& info);
    
    // 下载步骤
    int DoRequest();
    int DoPrepare();
    int DoTransfer();
    int DoVerify();
    
    // 数据接收
    int ReceiveBlock(const BYTE* data, int len);
    
    // 进度查询
    float GetProgress() const { return m_transferInfo.fProgress; }
    int GetSpeed() const { return m_transferInfo.nSpeed; }
    
    // 取消下载
    int Cancel();
    
    // 文件保存
    int SaveToFile(const string& filePath);
};
```

### 6. Zx103CacheIndex.h/cpp - 缓存索引

#### 类定义
```cpp
class CZx103CacheIndex {
private:
    // 缓存项
    struct CACHE_ITEM {
        int nPointID;                  // 点号
        int nInfoAddr;                 // 信息地址
        int nFuncType;                 // 功能类型
        int nInfoNum;                  // 信息序号
        void* pData;                   // 数据指针
        int nDataSize;                 // 数据大小
        time_t tUpdateTime;            // 更新时间
    };
    
    // 索引类型
    map<int, CACHE_ITEM*> m_mapByPointID;     // 按点号索引
    map<int, CACHE_ITEM*> m_mapByInfoAddr;    // 按信息地址索引
    multimap<int, CACHE_ITEM*> m_mapByFuncType; // 按功能类型索引
    
    // 缓存管理
    list<CACHE_ITEM*> m_listCache;            // 缓存列表
    int m_nMaxSize;                           // 最大大小
    CXJLock m_lock;                          // 锁
    
public:
    CZx103CacheIndex(int maxSize = 10000);
    ~CZx103CacheIndex();
    
    // 添加缓存
    int Add(const CACHE_ITEM& item);
    
    // 查询缓存
    CACHE_ITEM* GetByPointID(int pointID);
    CACHE_ITEM* GetByInfoAddr(int infoAddr);
    vector<CACHE_ITEM*> GetByFuncType(int funcType);
    
    // 更新缓存
    int Update(int pointID, void* pData, int dataSize);
    
    // 删除缓存
    int Remove(int pointID);
    int RemoveByFuncType(int funcType);
    
    // 清空缓存
    void Clear();
    
    // 缓存管理
    int GetSize() const { return m_listCache.size(); }
    bool IsFull() const { return m_listCache.size() >= m_nMaxSize; }
    void RemoveOldest();
};
```

## 执行流程

### 1. 主流程执行
```
Run()
    ├── 创建CZxPro103ClientWay
    ├── 初始化
    │   ├── 加载配置
    │   ├── 创建操作对象
    │   └── 初始化缓存
    ├── 连接服务器
    ├── 执行初始化操作
    │   ├── 链路初始化
    │   ├── 时钟同步
    │   ├── 总查询
    │   └── 设备查询
    ├── 进入主循环
    │   ├── 接收数据
    │   ├── 处理命令
    │   └── 发送响应
    └── 清理退出
```

### 2. 控制命令流程
```
ExecuteControl()
    ├── 创建控制命令
    ├── 选择控制点
    │   └── 发送选择命令
    ├── 等待选择确认
    ├── 执行控制
    │   └── 发送执行命令
    ├── 等待执行确认
    └── 返回结果
```

### 3. 文件下载流程
```
ExecuteFileDownload()
    ├── 请求文件信息
    ├── 准备接收
    │   ├── 分配缓冲区
    │   └── 创建文件
    ├── 接收数据块
    │   ├── 接收块数据
    │   ├── 写入缓冲区
    │   └── 更新进度
    ├── 校验文件
    │   └── CRC校验
    └── 保存文件
```

## 配置管理

### 主流程配置
```ini
[MainFlow]
# 连接配置
ServerIP=*************
ServerPort=2404
LinkAddress=1
CommonAddress=1

# 初始化配置
EnableGeneralQuery=true
EnableClockSync=true
EnableDeviceQuery=true
InitTimeout=60
InitRetryCount=3

# 控制配置
ControlTimeout=30
SelectBeforeExecute=true

# 文件下载配置
DownloadPath=/var/download
MaxFileSize=104857600
BlockSize=8192
```

## 错误处理

### 错误码定义
```cpp
enum ErrorCode {
    ERR_SUCCESS = 0,
    ERR_CONNECT_FAILED = -1,
    ERR_INIT_FAILED = -2,
    ERR_CONTROL_FAILED = -3,
    ERR_DOWNLOAD_FAILED = -4,
    ERR_TIMEOUT = -5,
    ERR_INVALID_PARAM = -6,
    ERR_NO_MEMORY = -7
};
```

## 使用注意事项

1. **连接管理**: 确保服务器地址和端口正确
2. **初始化顺序**: 必须按照规定的顺序执行初始化
3. **控制安全**: 控制操作需要验证权限
4. **文件大小**: 注意文件下载的大小限制
5. **超时处理**: 合理设置各种操作的超时时间
