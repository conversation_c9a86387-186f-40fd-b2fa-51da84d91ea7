# ZcsFoshan104SrvPro - 佛山104服务端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsFoshan104SrvPro
- **主要功能**: 实现佛山地区定制的IEC 60870-5-104协议服务端
- **设计目的**: 满足佛山电网的特殊需求，提供定制化的104协议实现

## 核心文件分析

### 1. XJProRun.h/cpp - 协议运行入口

#### 类定义
```cpp
class CXJProFoshan104ServerWay {
private:
    SERVER_PRO_INTERFACE& m_ProInterface;       // 服务端协议接口
    CXJFoShan104ASDUFactory* m_pASDUFactory;   // 佛山104 ASDU工厂
    CXJSrvAPCIInterFace* m_pAPCIHandler;       // APCI处理器
    CLogFile& m_LogFile;                       // 日志文件
    CXJMsgCaster* m_pMsgCaster;                // 消息广播器
    
    // 佛山特殊配置
    int m_nUpDataFromDb;                       // 是否从数据库更新数据
    bool m_bEnableFoshanExtension;             // 启用佛山扩展
    
public:
    CXJProFoshan104ServerWay(SERVER_PRO_INTERFACE& interface);
    ~CXJProFoshan104ServerWay();
    
    // 主运行函数
    int Run();
    
    // 初始化
    int Init();
    int InitFoshanConfig();
    
    // 佛山特殊处理
    int ProcessFoshanSpecialCommand(const ASDUMESSAGE& msg);
    int HandleFoshanExtendedData(const FOSHAN_EXT_DATA& data);
};
```

#### 动态库导出
```cpp
extern "C" {
#ifdef OS_WINDOWS
    __declspec(dllexport) int Run(SERVER_PRO_INTERFACE* pProInterface,
                                  CXJSrvAPCIInterFace* pAPCIHandler,
                                  CXJSrvASDUFactory* pASDUFactory,
                                  CLogFile* pLogFile,
                                  CXJMsgCaster* pMsgCaster);
#else
    int Run(SERVER_PRO_INTERFACE* pProInterface,
           CXJSrvAPCIInterFace* pAPCIHandler,
           CXJSrvASDUFactory* pASDUFactory,
           CLogFile* pLogFile,
           CXJMsgCaster* pMsgCaster);
#endif
}
```

### 2. XJFoShan104ASDUFactory.h/cpp - 佛山104 ASDU工厂

#### 类定义
```cpp
class CXJFoShan104ASDUFactory : public CXJSrvASDUFactory {
private:
    CXJCommonDBFunction& m_ComDBFun;           // 数据库功能
    CLogFile& m_LogFile;                       // 日志文件
    CXJMsgCaster* m_pMsgCaster;                // 消息广播器
    
    // 佛山特殊配置
    int m_nUpDataFromDb;                       // 是否从数据库更新数据
    
    // ASDU处理器映射
    map<int, ASDU_CREATOR> m_mapCreators;      // ASDU创建器映射
    
public:
    CXJFoShan104ASDUFactory(CXJCommonDBFunction& pComDBFun, 
                           CLogFile& pLogFile, 
                           CXJMsgCaster* pMsgCaster);
    virtual ~CXJFoShan104ASDUFactory();
    
    // 创建ASDU处理器
    virtual CXJSrvASDUInterface* CreateControlASDUHandler(ASDUMESSAGE* pASDU);
    virtual CXJSrvASDUInterface* CreateMonitorASDUHandler(STTPDATA_103CMDS_STRUCT* pSTTPData);
    
    // 佛山特殊配置
    int SetUpDataFromDb(int nValue);
    
protected:
    // 日志记录
    virtual void WriteLog(const char* pLog, int nLevel);
    
    // 厂站管理
    virtual string GetStationID(int pLinkAddr);
    virtual int GetLinkAddrByStationId(string pStationID);
    
    // 信息集长度
    virtual UINT GetMaxInfSetLength(void);
    
private:
    // 注册ASDU处理器
    void RegisterASDUHandlers();
    
    // 佛山特殊ASDU创建
    CXJSrvASDUInterface* CreateFoshanASDU1(ASDUMESSAGE* pASDU);
    CXJSrvASDUInterface* CreateFoshanASDU100(ASDUMESSAGE* pASDU);
    CXJSrvASDUInterface* CreateFoshanAutoUp(ASDUMESSAGE* pASDU);
};
```

#### ASDU处理器创建
```cpp
CXJSrvASDUInterface* CXJFoShan104ASDUFactory::CreateControlASDUHandler(
    ASDUMESSAGE* pASDU) {
    
    if (!pASDU) {
        WriteLog("Invalid ASDU message", LOG_ERROR);
        return nullptr;
    }
    
    // 根据ASDU类型创建处理器
    switch (pASDU->byType) {
        case 1:  // 单点信息
            return CreateFoshanASDU1(pASDU);
            
        case 100:  // 总召唤命令
            return new CXJFoShan104SrvCASDU100(
                *this, *pASDU, m_ComDBFun, m_LogFile, m_nUpDataFromDb);
            
        case 103:  // 时钟同步命令
            return new CXJSrvCASDU103(*this, *pASDU, m_ComDBFun, m_LogFile);
            
        // 佛山特殊类型
        case 200:  // 佛山扩展类型200
            return new CXJFoshanExtASDU200(*this, *pASDU, m_ComDBFun, m_LogFile);
            
        case 201:  // 佛山扩展类型201
            return new CXJFoshanExtASDU201(*this, *pASDU, m_ComDBFun, m_LogFile);
            
        default:
            WriteLog("Unknown ASDU type: " + to_string(pASDU->byType), LOG_WARN);
            return nullptr;
    }
}
```

### 3. XJFoShan104SrvCASDU100.h/cpp - 佛山总召唤处理

#### 类定义
```cpp
class CXJFoShan104SrvCASDU100 : public CXJ104FoShanASDU100 {
private:
    CXJSrvASDUFactory& m_ASDUFactory;          // ASDU工厂
    ASDUMESSAGE& m_AsduCMD;                    // ASDU命令
    CXJCommonDBFunction& m_ComDBFun;          // 数据库功能
    CLogFile& m_LogFile;                      // 日志文件
    
    // 佛山特殊配置
    int m_nUpDataFromDb;                      // 是否从数据库更新数据
    
    // 数据缓存
    vector<FOSHAN_DATA_POINT> m_vecDataPoints; // 数据点缓存
    
public:
    CXJFoShan104SrvCASDU100(CXJSrvASDUFactory& pASDUFactory,
                           ASDUMESSAGE& pAsduCMD,
                           CXJCommonDBFunction& pComDBFun,
                           CLogFile& pLogFile,
                           int nUpDataFromDb);
    virtual ~CXJFoShan104SrvCASDU100();
    
protected:
    // 处理总召唤
    int Handle(ASDUQUEUE& pASDUResults, STTP103CMDSET& pSttp103CMDs);
    
private:
    // 佛山特殊处理
    int HandleFoshanGeneralInterrogation();
    int QueryDataFromDatabase();
    int BuildResponseASDUs(ASDUQUEUE& results);
    
    // 数据分组
    int GroupDataByType();
    int CreateGroupedASDUs(int groupType, ASDUQUEUE& results);
    
    // 数据更新策略
    int UpdateDataStrategy();
    bool ShouldUpdateFromDB() const { return m_nUpDataFromDb == 1; }
};
```

#### 总召唤处理实现
```cpp
int CXJFoShan104SrvCASDU100::Handle(
    ASDUQUEUE& pASDUResults, 
    STTP103CMDSET& pSttp103CMDs) {
    
    WriteLog("Handle Foshan GI command", LOG_INFO);
    
    // 1. 发送总召唤确认
    ASDUMESSAGE* pConfirm = CreateConfirmASDU(7); // COT=7 激活确认
    if (pConfirm) {
        pASDUResults.push(pConfirm);
    }
    
    // 2. 判断数据更新策略
    if (ShouldUpdateFromDB()) {
        // 从数据库获取最新数据
        if (QueryDataFromDatabase() != 0) {
            WriteLog("Query database failed", LOG_ERROR);
            return -1;
        }
    } else {
        // 使用缓存数据
        if (m_vecDataPoints.empty()) {
            WriteLog("No cached data available", LOG_WARN);
            return -1;
        }
    }
    
    // 3. 数据分组（佛山特殊要求）
    GroupDataByType();
    
    // 4. 构建响应ASDU
    BuildResponseASDUs(pASDUResults);
    
    // 5. 发送总召唤结束
    ASDUMESSAGE* pEnd = CreateConfirmASDU(10); // COT=10 激活结束
    if (pEnd) {
        pASDUResults.push(pEnd);
    }
    
    WriteLog("Foshan GI handled successfully", LOG_INFO);
    return 0;
}

int CXJFoShan104SrvCASDU100::QueryDataFromDatabase() {
    // 佛山特殊SQL查询
    string sql = "SELECT point_id, point_value, quality, update_time "
                "FROM foshan_realtime_data "
                "WHERE station_id = ? AND device_id = ? "
                "ORDER BY point_type, point_id";
    
    SQL_DATA queryData;
    queryData.strSQL = sql;
    // ... 设置参数
    
    CMemSet resultSet;
    char errorMsg[256];
    
    int ret = m_ComDBFun.Select(TABLE_REALTIME, queryData, errorMsg, &resultSet);
    if (ret != 0) {
        WriteLog(string("Database query failed: ") + errorMsg, LOG_ERROR);
        return ret;
    }
    
    // 解析查询结果
    m_vecDataPoints.clear();
    for (int i = 0; i < resultSet.GetRowCount(); i++) {
        FOSHAN_DATA_POINT point;
        point.nPointID = resultSet.GetIntValue(i, 0);
        point.fValue = resultSet.GetFloatValue(i, 1);
        point.nQuality = resultSet.GetIntValue(i, 2);
        point.tUpdateTime = resultSet.GetTimeValue(i, 3);
        
        m_vecDataPoints.push_back(point);
    }
    
    WriteLog("Loaded " + to_string(m_vecDataPoints.size()) + 
             " points from database", LOG_INFO);
    return 0;
}
```

### 4. 佛山特殊ASDU类型

#### Zx104FoShanASDU1 - 佛山单点信息
```cpp
class CXJ104FoShanASDU1 : public CXJSrvASDUInterface {
private:
    // 佛山特殊字段
    struct FOSHAN_SP_EXT {
        BYTE byDeviceType;     // 设备类型
        BYTE byAreaCode;       // 区域代码
        WORD wExtInfo;         // 扩展信息
    };
    
public:
    // 佛山特殊处理
    virtual int Process(ASDUQUEUE& results) override {
        // 添加佛山扩展信息
        AddFoshanExtension();
        
        // 标准处理
        return ProcessStandard(results);
    }
    
private:
    void AddFoshanExtension() {
        FOSHAN_SP_EXT ext;
        ext.byDeviceType = GetDeviceType();
        ext.byAreaCode = GetAreaCode();
        ext.wExtInfo = GetExtendedInfo();
        
        // 添加到ASDU
        AppendToASDU(&ext, sizeof(ext));
    }
};
```

#### Zx104FoShanASDUAutoUp - 佛山自动上送
```cpp
class CXJ104FoShanASDUAutoUp : public CXJSrvASDUInterface {
private:
    // 自动上送配置
    struct AUTO_UP_CONFIG {
        bool bEnableAutoUp;         // 启用自动上送
        int nInterval;              // 上送间隔（秒）
        int nPriority;              // 优先级
        vector<int> vecPointIDs;   // 上送点列表
    };
    AUTO_UP_CONFIG m_config;
    
public:
    // 自动上送处理
    int ProcessAutoUp() {
        if (!m_config.bEnableAutoUp) {
            return 0;
        }
        
        // 收集变化数据
        vector<CHANGED_DATA> changedData;
        CollectChangedData(changedData);
        
        // 构建自动上送ASDU
        if (!changedData.empty()) {
            BuildAutoUpASDU(changedData);
        }
        
        return 0;
    }
    
private:
    void CollectChangedData(vector<CHANGED_DATA>& data) {
        for (int pointID : m_config.vecPointIDs) {
            if (HasChanged(pointID)) {
                CHANGED_DATA item;
                item.nPointID = pointID;
                item.fNewValue = GetCurrentValue(pointID);
                item.tChangeTime = time(NULL);
                data.push_back(item);
            }
        }
    }
};
```

## 佛山特殊功能

### 1. 数据库更新策略
```cpp
enum UpdateStrategy {
    UPDATE_FROM_CACHE = 0,     // 从缓存更新
    UPDATE_FROM_DB = 1,        // 从数据库更新
    UPDATE_HYBRID = 2          // 混合模式
};

class CFoshanDataUpdater {
public:
    int UpdateData(UpdateStrategy strategy) {
        switch (strategy) {
            case UPDATE_FROM_CACHE:
                return UpdateFromCache();
            case UPDATE_FROM_DB:
                return UpdateFromDatabase();
            case UPDATE_HYBRID:
                return UpdateHybrid();
        }
        return -1;
    }
    
private:
    int UpdateHybrid() {
        // 重要数据从数据库更新
        UpdateImportantFromDB();
        
        // 其他数据从缓存更新
        UpdateOthersFromCache();
        
        return 0;
    }
};
```

### 2. 区域代码处理
```cpp
class CFoshanAreaManager {
private:
    // 佛山地区代码
    enum AreaCode {
        AREA_CHANCHENG = 0x01,   // 禅城区
        AREA_NANHAI = 0x02,      // 南海区
        AREA_SHUNDE = 0x03,      // 顺德区
        AREA_GAOMING = 0x04,     // 高明区
        AREA_SANSHUI = 0x05      // 三水区
    };
    
    map<string, AreaCode> m_mapAreaCodes;
    
public:
    AreaCode GetAreaCode(const string& stationName) {
        // 根据厂站名称判断区域
        if (stationName.find("禅城") != string::npos) {
            return AREA_CHANCHENG;
        } else if (stationName.find("南海") != string::npos) {
            return AREA_NANHAI;
        } else if (stationName.find("顺德") != string::npos) {
            return AREA_SHUNDE;
        } else if (stationName.find("高明") != string::npos) {
            return AREA_GAOMING;
        } else if (stationName.find("三水") != string::npos) {
            return AREA_SANSHUI;
        }
        return AREA_CHANCHENG; // 默认禅城
    }
};
```

### 3. 扩展信息处理
```cpp
class CFoshanExtInfo {
public:
    // 构建扩展信息
    WORD BuildExtInfo(const POINT_INFO& point) {
        WORD extInfo = 0;
        
        // Bit 0-3: 电压等级
        extInfo |= (GetVoltageLevel(point) & 0x0F);
        
        // Bit 4-7: 设备类型
        extInfo |= ((GetDeviceType(point) & 0x0F) << 4);
        
        // Bit 8-11: 重要程度
        extInfo |= ((GetImportance(point) & 0x0F) << 8);
        
        // Bit 12-15: 保留
        
        return extInfo;
    }
    
private:
    int GetVoltageLevel(const POINT_INFO& point) {
        // 0: 10kV, 1: 35kV, 2: 110kV, 3: 220kV, 4: 500kV
        if (point.strVoltage == "10kV") return 0;
        if (point.strVoltage == "35kV") return 1;
        if (point.strVoltage == "110kV") return 2;
        if (point.strVoltage == "220kV") return 3;
        if (point.strVoltage == "500kV") return 4;
        return 0;
    }
};
```

## 配置管理

### 佛山104服务端配置
```ini
[Foshan104Server]
# 基本配置
ListenPort=2404
MaxConnections=50
LinkAddress=1
CommonAddress=1

# 佛山特殊配置
UpdateFromDB=1              # 0-缓存, 1-数据库, 2-混合
EnableFoshanExtension=true  # 启用佛山扩展
EnableAutoUp=true          # 启用自动上送
AutoUpInterval=10          # 自动上送间隔(秒)

# 区域配置
DefaultAreaCode=1          # 默认区域代码
EnableAreaMapping=true     # 启用区域映射

# 数据库配置
DBQueryTimeout=30          # 数据库查询超时
MaxQueryRows=10000        # 最大查询行数
EnableDBCache=true        # 启用数据库缓存
CacheExpireTime=300       # 缓存过期时间(秒)

# 扩展信息
EnableExtInfo=true        # 启用扩展信息
ExtInfoVersion=2          # 扩展信息版本
```

## 性能优化

### 1. 数据缓存
- 分层缓存策略
- LRU缓存淘汰
- 定时刷新机制

### 2. 批量处理
- 批量查询数据库
- 批量构建ASDU
- 批量发送响应

### 3. 异步处理
- 异步数据库查询
- 异步ASDU生成
- 异步消息发送

## 故障处理

### 1. 数据库故障
- 自动切换到缓存模式
- 记录故障日志
- 触发告警通知

### 2. 通信故障
- 自动重连机制
- 数据缓存重发
- 连接状态监控

## 使用注意事项

1. **区域配置**: 正确配置佛山各区域代码
2. **数据库策略**: 根据实际需求选择更新策略
3. **扩展信息**: 确保扩展信息格式正确
4. **性能监控**: 监控数据库查询性能
5. **版本兼容**: 注意与标准104协议的兼容性
