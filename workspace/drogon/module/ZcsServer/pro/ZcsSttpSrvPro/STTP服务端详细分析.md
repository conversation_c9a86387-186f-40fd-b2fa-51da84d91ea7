# ZcsSttpSrvPro - STTP服务端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsSttpSrvPro
- **主要功能**: 实现STTP（Smart Transmission & Transformation Protocol）协议服务端
- **设计目的**: 提供高效的电力系统数据传输服务，支持主站、前置、自动召唤等多种应用场景

## 核心文件分析

### 1. ZxProRun.cpp - 协议运行入口

#### 功能说明
- 协议库的动态加载入口
- 初始化各种STTP消息处理器
- 管理协议运行生命周期

```cpp
extern "C" int Run(SERVER_PRO_INTERFACE* pProInterface) {
    // 1. 创建STTP服务器
    SttpServer* server = new SttpServer(pProInterface);
    
    // 2. 初始化消息处理器
    server->RegisterHandler(new ZxMainStationSttpMsgHandler());
    server->RegisterHandler(new ZxFrontSttpMsgHandler());
    server->RegisterHandler(new ZxAutoCallSttpMsgHandler());
    server->RegisterHandler(new ZxCmdTransSttpMsgHandler());
    server->RegisterHandler(new ZxPACKAGESttpMsgHandler());
    
    // 3. 启动服务
    server->Start();
    
    // 4. 运行主循环
    server->Run();
    
    return 0;
}
```

### 2. ZxMainStationSttpMsgHandler.h/cpp - 主站STTP消息处理

#### 类定义
```cpp
class ZxMainStationSttpMsgHandler : public ISttpMsgHandler {
private:
    // 消息缓存
    map<int, STTP_MSG_CACHE> m_mapMsgCache;   // 消息缓存映射
    CXJLock m_lockCache;                      // 缓存锁
    
    // 站点管理
    map<string, STATION_INFO> m_mapStations;  // 站点信息映射
    
    // 数据处理
    CDataProcessor* m_pDataProcessor;          // 数据处理器
    CXJPublisher* m_pPublisher;               // 发布者
    
public:
    // 消息处理接口
    virtual int HandleMessage(STTPMSG* pMsg) override;
    
    // 具体消息处理
    int HandleLoginMsg(STTPMSG* pMsg);        // 登录消息
    int HandleLogoutMsg(STTPMSG* pMsg);       // 登出消息
    int HandleDataMsg(STTPMSG* pMsg);         // 数据消息
    int HandleCommandMsg(STTPMSG* pMsg);      // 命令消息
    int HandleResponseMsg(STTPMSG* pMsg);     // 响应消息
    int HandleHeartbeatMsg(STTPMSG* pMsg);    // 心跳消息
    
    // 数据处理
    int ProcessRealtimeData(STTP_DATA* pData);
    int ProcessHistoryData(STTP_DATA* pData);
    int ProcessAlarmData(STTP_DATA* pData);
    int ProcessSOEData(STTP_DATA* pData);
    
    // 命令处理
    int ProcessControl(STTP_CONTROL* pCtrl);
    int ProcessSetpoint(STTP_SETPOINT* pSet);
    int ProcessFileTransfer(STTP_FILE* pFile);
};
```

#### 主站功能实现
```cpp
int ZxMainStationSttpMsgHandler::HandleDataMsg(STTPMSG* pMsg) {
    // 1. 解析消息类型
    STTP_DATA_TYPE dataType = GetDataType(pMsg);
    
    // 2. 根据类型分发处理
    switch (dataType) {
        case STTP_REALTIME:
            return ProcessRealtimeData(pMsg->pData);
            
        case STTP_HISTORY:
            return ProcessHistoryData(pMsg->pData);
            
        case STTP_ALARM:
            return ProcessAlarmData(pMsg->pData);
            
        case STTP_SOE:
            return ProcessSOEData(pMsg->pData);
            
        default:
            return -1;
    }
}

int ZxMainStationSttpMsgHandler::ProcessRealtimeData(STTP_DATA* pData) {
    // 1. 数据验证
    if (!ValidateData(pData)) {
        return -1;
    }
    
    // 2. 数据转换
    UNIFIED_DATA unifiedData;
    ConvertToUnified(pData, unifiedData);
    
    // 3. 数据存储
    m_pDataProcessor->SaveRealtimeData(unifiedData);
    
    // 4. 数据发布
    m_pPublisher->PublishData(unifiedData);
    
    // 5. 生成响应
    SendResponse(pData->nRII, STTP_SUCCESS);
    
    return 0;
}
```

### 3. ZxFrontSttpMsgHandler.h/cpp - 前置STTP消息处理

#### 类定义
```cpp
class ZxFrontSttpMsgHandler : public ISttpMsgHandler {
private:
    // 前置管理
    map<int, FRONT_INFO> m_mapFronts;        // 前置信息映射
    
    // 协议转换
    ProtocolConverter* m_pConverter;          // 协议转换器
    
    // 发布者
    ZxFrontPublisher* m_pFrontPublisher;     // 前置发布者
    
public:
    // 前置特有功能
    int HandleFrontRegister(STTPMSG* pMsg);   // 前置注册
    int HandleFrontStatus(STTPMSG* pMsg);     // 前置状态
    int HandleProtocolData(STTPMSG* pMsg);    // 协议数据
    
    // 协议转换
    int Convert103ToSttp(const BYTE* data103, STTP_DATA* sttpData);
    int Convert104ToSttp(const BYTE* data104, STTP_DATA* sttpData);
    int Convert61850ToSttp(const MMS_DATA* mmsData, STTP_DATA* sttpData);
    
    // 前置管理
    int RegisterFront(int frontID, const FRONT_INFO& info);
    int UnregisterFront(int frontID);
    int UpdateFrontStatus(int frontID, int status);
};
```

### 4. ZxFrontPublisher.h/cpp - 前置发布者

#### 类定义
```cpp
class ZxFrontPublisher : public CXJPublisher {
private:
    // 订阅管理
    map<string, SUBSCRIBER_LIST> m_mapSubscribers; // 订阅者映射
    
    // 过滤规则
    FilterRules* m_pFilterRules;              // 过滤规则
    
public:
    // 发布接口
    int PublishFrontData(const FRONT_DATA& data);
    int PublishFrontStatus(int frontID, int status);
    int PublishFrontAlarm(const FRONT_ALARM& alarm);
    
    // 订阅管理
    int AddSubscriber(const string& topic, ISubscriber* subscriber);
    int RemoveSubscriber(const string& topic, ISubscriber* subscriber);
    
    // 数据过滤
    bool FilterData(const FRONT_DATA& data);
    bool MatchTopic(const string& topic, const string& pattern);
};
```

### 5. ZxAutoCallSttpMsgHandler.h/cpp - 自动召唤消息处理

#### 类定义
```cpp
class ZxAutoCallSttpMsgHandler : public ISttpMsgHandler {
private:
    // 召唤配置
    AUTO_CALL_CONFIG m_config;                // 自动召唤配置
    
    // 召唤任务
    queue<CALL_TASK> m_queueTasks;           // 任务队列
    CXJLock m_lockQueue;                     // 队列锁
    
    // 发布者
    ZxAutoCallPublisher* m_pPublisher;       // 自动召唤发布者
    
    // 定时器
    TIMER_HANDLE m_hTimer;                   // 定时器句柄
    
public:
    // 召唤处理
    int HandleGeneralInterrogation(STTPMSG* pMsg);  // 总召
    int HandleClockSync(STTPMSG* pMsg);            // 时钟同步
    int HandleCounterInterrogation(STTPMSG* pMsg);  // 电度召唤
    int HandleTestCommand(STTPMSG* pMsg);          // 测试命令
    
    // 自动召唤
    int StartAutoCall();
    int StopAutoCall();
    int ExecuteCallTask(const CALL_TASK& task);
    
    // 定时处理
    static void OnTimer(TIMER_ID id, void* param);
    void ProcessTimer();
};
```

#### 自动召唤实现
```cpp
int ZxAutoCallSttpMsgHandler::StartAutoCall() {
    // 1. 读取配置
    LoadAutoCallConfig(m_config);
    
    // 2. 创建召唤任务
    if (m_config.bEnableGI) {
        CALL_TASK task;
        task.type = CALL_GI;
        task.interval = m_config.nGIInterval;
        task.nextTime = time(NULL) + task.interval;
        m_queueTasks.push(task);
    }
    
    if (m_config.bEnableClockSync) {
        CALL_TASK task;
        task.type = CALL_CLOCK_SYNC;
        task.interval = m_config.nClockSyncInterval;
        task.nextTime = time(NULL) + task.interval;
        m_queueTasks.push(task);
    }
    
    // 3. 启动定时器
    m_hTimer = CreateTimer(1000, OnTimer, this);
    
    return 0;
}

void ZxAutoCallSttpMsgHandler::ProcessTimer() {
    time_t now = time(NULL);
    
    CCsLocker lock(&m_lockQueue);
    
    // 检查任务队列
    queue<CALL_TASK> tempQueue;
    while (!m_queueTasks.empty()) {
        CALL_TASK task = m_queueTasks.front();
        m_queueTasks.pop();
        
        if (task.nextTime <= now) {
            // 执行任务
            ExecuteCallTask(task);
            
            // 重新调度
            task.nextTime = now + task.interval;
        }
        
        tempQueue.push(task);
    }
    
    m_queueTasks = tempQueue;
}
```

### 6. ZxAutoCallPublisher.h/cpp - 自动召唤发布者

#### 功能实现
```cpp
class ZxAutoCallPublisher : public CXJPublisher {
public:
    // 发布总召结果
    int PublishGIResult(const GI_RESULT& result) {
        STTP_FULL_DATA sttpData;
        
        // 填充数据
        sttpData.nDataType = STTP_GI_RESULT;
        sttpData.strStation = result.station;
        sttpData.nPointCount = result.pointCount;
        
        // 转换数据点
        for (int i = 0; i < result.pointCount; i++) {
            STTP_POINT point;
            point.nID = result.points[i].id;
            point.fValue = result.points[i].value;
            point.nQuality = result.points[i].quality;
            point.tTime = result.points[i].time;
            
            sttpData.points.push_back(point);
        }
        
        // 发布数据
        return Publish(sttpData);
    }
    
    // 发布时钟同步结果
    int PublishClockSyncResult(const CLOCK_SYNC_RESULT& result) {
        STTP_FULL_DATA sttpData;
        
        sttpData.nDataType = STTP_CLOCK_SYNC_RESULT;
        sttpData.strStation = result.station;
        sttpData.tLocalTime = result.localTime;
        sttpData.tRemoteTime = result.remoteTime;
        sttpData.nTimeDiff = result.timeDiff;
        
        return Publish(sttpData);
    }
};
```

### 7. ZxCmdTransSttpMsgHandler.h/cpp - 命令转发消息处理

#### 类定义
```cpp
class ZxCmdTransSttpMsgHandler : public ISttpMsgHandler {
private:
    // 命令路由
    CommandRouter* m_pRouter;                 // 命令路由器
    
    // 命令缓存
    map<int, COMMAND_CACHE> m_mapCmdCache;   // 命令缓存
    
public:
    // 命令处理
    int HandleControlCommand(STTPMSG* pMsg);  // 控制命令
    int HandleSetpointCommand(STTPMSG* pMsg); // 设点命令
    int HandleFileCommand(STTPMSG* pMsg);     // 文件命令
    
    // 命令路由
    int RouteCommand(const STTP_COMMAND& cmd);
    int FindDestination(const string& target);
    
    // 命令执行
    int ExecuteCommand(const STTP_COMMAND& cmd);
    int WaitCommandResult(int cmdID, int timeout);
};
```

### 8. ZxPACKAGESttpMsgHandler.h/cpp - 包消息处理

#### 类定义
```cpp
class ZxPACKAGESttpMsgHandler : public ISttpMsgHandler {
private:
    // 包管理
    map<int, PACKAGE_INFO> m_mapPackages;    // 包信息映射
    
    // 分片处理
    FragmentManager* m_pFragmentMgr;         // 分片管理器
    
    // 压缩处理
    Compressor* m_pCompressor;               // 压缩器
    
public:
    // 包处理
    int HandlePackageStart(STTPMSG* pMsg);    // 包开始
    int HandlePackageData(STTPMSG* pMsg);     // 包数据
    int HandlePackageEnd(STTPMSG* pMsg);      // 包结束
    
    // 分片处理
    int SplitPackage(const BYTE* data, int len, FRAGMENT_LIST& fragments);
    int MergeFragments(const FRAGMENT_LIST& fragments, BYTE* data, int& len);
    
    // 压缩处理
    int CompressData(const BYTE* src, int srcLen, BYTE* dst, int& dstLen);
    int DecompressData(const BYTE* src, int srcLen, BYTE* dst, int& dstLen);
    
    // 大文件传输
    int TransferLargeFile(const string& fileName, const BYTE* data, int len);
    int ReceiveLargeFile(const string& fileName, BYTE* data, int& len);
};
```

#### 分片处理实现
```cpp
int ZxPACKAGESttpMsgHandler::SplitPackage(
    const BYTE* data, int len, FRAGMENT_LIST& fragments) {
    
    const int MAX_FRAGMENT_SIZE = 8192;  // 最大分片大小
    
    int fragmentCount = (len + MAX_FRAGMENT_SIZE - 1) / MAX_FRAGMENT_SIZE;
    
    for (int i = 0; i < fragmentCount; i++) {
        FRAGMENT fragment;
        fragment.nPackageID = GeneratePackageID();
        fragment.nIndex = i;
        fragment.nTotal = fragmentCount;
        
        int offset = i * MAX_FRAGMENT_SIZE;
        fragment.nSize = min(MAX_FRAGMENT_SIZE, len - offset);
        
        fragment.pData = new BYTE[fragment.nSize];
        memcpy(fragment.pData, data + offset, fragment.nSize);
        
        fragments.push_back(fragment);
    }
    
    return 0;
}
```

## STTP协议特点

### 1. 消息格式
```cpp
struct STTPMSG {
    STTP_HEADER header;      // 消息头
    STTP_BODY body;         // 消息体
    STTP_CHECKSUM checksum; // 校验和
};

struct STTP_HEADER {
    WORD wSync;             // 同步字 0x5AA5
    WORD wLength;           // 消息长度
    BYTE byVersion;         // 协议版本
    BYTE byType;            // 消息类型
    WORD wRII;              // 请求标识
    DWORD dwSequence;       // 序列号
};
```

### 2. 消息类型
```cpp
enum STTP_MSG_TYPE {
    STTP_LOGIN = 0x01,      // 登录
    STTP_LOGOUT = 0x02,     // 登出
    STTP_DATA = 0x03,       // 数据
    STTP_COMMAND = 0x04,    // 命令
    STTP_RESPONSE = 0x05,   // 响应
    STTP_HEARTBEAT = 0x06,  // 心跳
    STTP_FILE = 0x07,       // 文件
    STTP_PACKAGE = 0x08     // 包
};
```

## 性能优化

### 1. 消息批处理
- 合并小消息
- 批量发送
- 减少网络开销

### 2. 压缩传输
- 数据压缩
- 协议压缩
- 动态压缩策略

### 3. 缓存机制
- 消息缓存
- 命令缓存
- 结果缓存

### 4. 异步处理
- 异步消息处理
- 异步命令执行
- 异步文件传输

## 配置示例

```ini
[SttpServer]
Port=8080
MaxConnections=100
BufferSize=65536
Timeout=30

[AutoCall]
EnableGI=true
GIInterval=3600
EnableClockSync=true
ClockSyncInterval=86400

[Package]
MaxPackageSize=10485760
EnableCompression=true
CompressionLevel=6

[Security]
EnableEncryption=true
EncryptionAlgorithm=AES256
```

## 使用注意事项

1. **消息序列号**: 确保序列号连续性
2. **心跳维护**: 定期发送心跳保持连接
3. **大数据传输**: 使用分片机制传输大数据
4. **错误重传**: 实现错误重传机制
5. **流量控制**: 注意流量控制避免拥塞
