# ZcsWebSrvPro - Web服务端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsWebSrvPro
- **主要功能**: 提供基于HTTP/HTTPS的Web服务接口，支持RESTful API
- **设计目的**: 为Web客户端提供数据访问和控制接口

## 核心文件分析

### 1. ZxProWebSrvWay.h/cpp - Web服务主类

#### 类定义
```cpp
class CZxProWebSrvWay {
private:
    // HTTP服务器
    struct HTTP_SERVER {
        SOCKET listenSocket;        // 监听套接字
        int port;                   // 监听端口
        bool bSSL;                  // 是否启用SSL
        SSL_CTX* pSSLContext;       // SSL上下文
    };
    
    HTTP_SERVER m_httpServer;       // HTTP服务器
    HTTP_SERVER m_httpsServer;      // HTTPS服务器
    
    // 会话管理
    struct WEB_SESSION {
        int sessionID;              // 会话ID
        SOCKET clientSocket;        // 客户端套接字
        string clientIP;            // 客户端IP
        time_t loginTime;           // 登录时间
        time_t lastActiveTime;      // 最后活动时间
        USER_INFO userInfo;         // 用户信息
    };
    
    map<int, WEB_SESSION*> m_mapSessions;  // 会话映射
    CXJLock m_lockSessions;                // 会话锁
    
    // 路由表
    typedef int (CZxProWebSrvWay::*API_HANDLER)(const HTTP_REQUEST&, HTTP_RESPONSE&);
    map<string, API_HANDLER> m_mapRoutes;  // 路由映射
    
    // 数据处理
    CDataProcessor* m_pDataProcessor;       // 数据处理器
    CXJDBFacade* m_pDBFacade;              // 数据库接口
    
    // 文件服务
    CSendFile* m_pSendFile;                // 文件发送器
    
    // 配置
    WEB_CONFIG m_config;                   // Web配置
    
public:
    // 生命周期
    int Init(const SERVER_PRO_INTERFACE& interface);
    int Start();
    int Stop();
    int Release();
    
    // HTTP服务
    int StartHTTPServer(int port);
    int StartHTTPSServer(int port, const SSL_CONFIG& sslConfig);
    
    // 请求处理
    int ProcessRequest(SOCKET clientSocket);
    int ParseHTTPRequest(const string& rawRequest, HTTP_REQUEST& request);
    int RouteRequest(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int SendResponse(SOCKET clientSocket, const HTTP_RESPONSE& response);
    
    // API路由注册
    void RegisterRoutes();
    
    // API处理函数
    // 认证相关
    int HandleLogin(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleLogout(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleRefreshToken(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    
    // 数据查询
    int HandleGetRealtimeData(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleGetHistoryData(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleGetAlarmData(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleGetSOEData(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    
    // 控制操作
    int HandleControl(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleSetpoint(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    
    // 文件操作
    int HandleFileUpload(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleFileDownload(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int HandleFileList(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    
    // WebSocket支持
    int HandleWebSocketUpgrade(const HTTP_REQUEST& request, HTTP_RESPONSE& response);
    int ProcessWebSocketFrame(SOCKET clientSocket, const WS_FRAME& frame);
    
    // 会话管理
    WEB_SESSION* CreateSession(SOCKET clientSocket, const string& clientIP);
    void DestroySession(int sessionID);
    WEB_SESSION* GetSession(int sessionID);
    WEB_SESSION* GetSessionByToken(const string& token);
    
    // 安全验证
    bool ValidateToken(const string& token);
    string GenerateToken(const USER_INFO& userInfo);
    bool CheckPermission(const WEB_SESSION* session, const string& resource);
    
    // JSON处理
    string ToJSON(const DATA_OBJECT& obj);
    bool FromJSON(const string& json, DATA_OBJECT& obj);
    
    // 线程函数
    static THREAD_FUNC AcceptThread(LPVOID pParam);
    static THREAD_FUNC WorkerThread(LPVOID pParam);
};
```

#### HTTP请求和响应结构
```cpp
// HTTP请求
struct HTTP_REQUEST {
    string method;              // GET/POST/PUT/DELETE
    string uri;                 // 请求URI
    string version;             // HTTP版本
    map<string, string> headers; // 请求头
    string body;                // 请求体
    map<string, string> params; // 查询参数
    string token;               // 认证令牌
};

// HTTP响应
struct HTTP_RESPONSE {
    int statusCode;             // 状态码
    string statusText;          // 状态文本
    map<string, string> headers; // 响应头
    string body;                // 响应体
    
    void SetJSON(const string& json) {
        headers["Content-Type"] = "application/json";
        body = json;
    }
    
    void SetHTML(const string& html) {
        headers["Content-Type"] = "text/html";
        body = html;
    }
};
```

### 2. RESTful API实现

#### API路由注册
```cpp
void CZxProWebSrvWay::RegisterRoutes() {
    // 认证API
    m_mapRoutes["/api/auth/login"] = &CZxProWebSrvWay::HandleLogin;
    m_mapRoutes["/api/auth/logout"] = &CZxProWebSrvWay::HandleLogout;
    m_mapRoutes["/api/auth/refresh"] = &CZxProWebSrvWay::HandleRefreshToken;
    
    // 数据API
    m_mapRoutes["/api/data/realtime"] = &CZxProWebSrvWay::HandleGetRealtimeData;
    m_mapRoutes["/api/data/history"] = &CZxProWebSrvWay::HandleGetHistoryData;
    m_mapRoutes["/api/data/alarm"] = &CZxProWebSrvWay::HandleGetAlarmData;
    m_mapRoutes["/api/data/soe"] = &CZxProWebSrvWay::HandleGetSOEData;
    
    // 控制API
    m_mapRoutes["/api/control/execute"] = &CZxProWebSrvWay::HandleControl;
    m_mapRoutes["/api/control/setpoint"] = &CZxProWebSrvWay::HandleSetpoint;
    
    // 文件API
    m_mapRoutes["/api/file/upload"] = &CZxProWebSrvWay::HandleFileUpload;
    m_mapRoutes["/api/file/download"] = &CZxProWebSrvWay::HandleFileDownload;
    m_mapRoutes["/api/file/list"] = &CZxProWebSrvWay::HandleFileList;
    
    // WebSocket
    m_mapRoutes["/ws"] = &CZxProWebSrvWay::HandleWebSocketUpgrade;
}
```

#### 实时数据查询API
```cpp
int CZxProWebSrvWay::HandleGetRealtimeData(
    const HTTP_REQUEST& request, 
    HTTP_RESPONSE& response) {
    
    // 1. 验证令牌
    if (!ValidateToken(request.token)) {
        response.statusCode = 401;
        response.statusText = "Unauthorized";
        response.SetJSON("{\"error\":\"Invalid token\"}");
        return -1;
    }
    
    // 2. 解析参数
    string stationID = request.params["station"];
    string deviceID = request.params["device"];
    string pointType = request.params["type"];
    
    // 3. 查询数据
    vector<REALTIME_DATA> dataList;
    int ret = m_pDataProcessor->QueryRealtimeData(
        stationID, deviceID, pointType, dataList);
    
    if (ret != 0) {
        response.statusCode = 500;
        response.statusText = "Internal Server Error";
        response.SetJSON("{\"error\":\"Query failed\"}");
        return -1;
    }
    
    // 4. 构建JSON响应
    Json::Value root;
    root["status"] = "success";
    root["count"] = (int)dataList.size();
    
    Json::Value data(Json::arrayValue);
    for (const auto& item : dataList) {
        Json::Value obj;
        obj["id"] = item.pointID;
        obj["name"] = item.pointName;
        obj["value"] = item.value;
        obj["quality"] = item.quality;
        obj["timestamp"] = item.timestamp;
        data.append(obj);
    }
    root["data"] = data;
    
    Json::FastWriter writer;
    response.statusCode = 200;
    response.statusText = "OK";
    response.SetJSON(writer.write(root));
    
    return 0;
}
```

### 3. WebSocket支持

#### WebSocket处理
```cpp
class CWebSocketHandler {
private:
    SOCKET m_socket;                // WebSocket连接
    WS_STATE m_state;               // 连接状态
    queue<WS_MESSAGE> m_sendQueue;  // 发送队列
    queue<WS_MESSAGE> m_recvQueue;  // 接收队列
    
public:
    // WebSocket握手
    int PerformHandshake(const HTTP_REQUEST& request, HTTP_RESPONSE& response) {
        // 1. 验证WebSocket请求
        if (request.headers["Upgrade"] != "websocket" ||
            request.headers["Connection"] != "Upgrade") {
            return -1;
        }
        
        // 2. 获取WebSocket密钥
        string wsKey = request.headers["Sec-WebSocket-Key"];
        
        // 3. 计算接受密钥
        string acceptKey = CalculateAcceptKey(wsKey);
        
        // 4. 构建握手响应
        response.statusCode = 101;
        response.statusText = "Switching Protocols";
        response.headers["Upgrade"] = "websocket";
        response.headers["Connection"] = "Upgrade";
        response.headers["Sec-WebSocket-Accept"] = acceptKey;
        
        return 0;
    }
    
    // 发送WebSocket消息
    int SendMessage(const string& message) {
        WS_FRAME frame;
        frame.fin = true;
        frame.opcode = WS_OP_TEXT;
        frame.payload = message;
        
        return SendFrame(frame);
    }
    
    // 接收WebSocket消息
    int ReceiveMessage(string& message) {
        WS_FRAME frame;
        if (ReceiveFrame(frame) == 0) {
            if (frame.opcode == WS_OP_TEXT) {
                message = frame.payload;
                return 0;
            }
        }
        return -1;
    }
    
    // 处理WebSocket帧
    int ProcessFrame(const WS_FRAME& frame) {
        switch (frame.opcode) {
            case WS_OP_TEXT:
                return HandleTextFrame(frame);
            case WS_OP_BINARY:
                return HandleBinaryFrame(frame);
            case WS_OP_CLOSE:
                return HandleCloseFrame(frame);
            case WS_OP_PING:
                return HandlePingFrame(frame);
            case WS_OP_PONG:
                return HandlePongFrame(frame);
            default:
                return -1;
        }
    }
};
```

### 4. 文件服务

#### 文件上传处理
```cpp
int CZxProWebSrvWay::HandleFileUpload(
    const HTTP_REQUEST& request, 
    HTTP_RESPONSE& response) {
    
    // 1. 解析multipart/form-data
    string boundary = GetBoundary(request.headers["Content-Type"]);
    vector<MULTIPART_PART> parts;
    ParseMultipartData(request.body, boundary, parts);
    
    // 2. 查找文件部分
    MULTIPART_PART* filePart = nullptr;
    for (auto& part : parts) {
        if (part.name == "file") {
            filePart = &part;
            break;
        }
    }
    
    if (!filePart) {
        response.statusCode = 400;
        response.statusText = "Bad Request";
        response.SetJSON("{\"error\":\"No file found\"}");
        return -1;
    }
    
    // 3. 保存文件
    string savePath = m_config.uploadPath + "/" + filePart->filename;
    FILE* fp = fopen(savePath.c_str(), "wb");
    if (!fp) {
        response.statusCode = 500;
        response.statusText = "Internal Server Error";
        response.SetJSON("{\"error\":\"Failed to save file\"}");
        return -1;
    }
    
    fwrite(filePart->data.c_str(), 1, filePart->data.size(), fp);
    fclose(fp);
    
    // 4. 返回成功响应
    Json::Value root;
    root["status"] = "success";
    root["filename"] = filePart->filename;
    root["size"] = (int)filePart->data.size();
    root["path"] = savePath;
    
    Json::FastWriter writer;
    response.statusCode = 200;
    response.statusText = "OK";
    response.SetJSON(writer.write(root));
    
    return 0;
}
```

### 5. 安全机制

#### JWT令牌实现
```cpp
class CJWTManager {
private:
    string m_secret;                // 密钥
    int m_expireTime;               // 过期时间（秒）
    
public:
    // 生成令牌
    string GenerateToken(const USER_INFO& userInfo) {
        // 1. 构建头部
        Json::Value header;
        header["alg"] = "HS256";
        header["typ"] = "JWT";
        
        // 2. 构建载荷
        Json::Value payload;
        payload["user_id"] = userInfo.userID;
        payload["username"] = userInfo.username;
        payload["role"] = userInfo.role;
        payload["exp"] = time(NULL) + m_expireTime;
        
        // 3. 编码
        string encodedHeader = Base64Encode(header.toStyledString());
        string encodedPayload = Base64Encode(payload.toStyledString());
        
        // 4. 签名
        string signature = HMAC_SHA256(
            encodedHeader + "." + encodedPayload, m_secret);
        
        // 5. 组合令牌
        return encodedHeader + "." + encodedPayload + "." + signature;
    }
    
    // 验证令牌
    bool ValidateToken(const string& token) {
        // 1. 分割令牌
        vector<string> parts = Split(token, '.');
        if (parts.size() != 3) return false;
        
        // 2. 验证签名
        string signature = HMAC_SHA256(parts[0] + "." + parts[1], m_secret);
        if (signature != parts[2]) return false;
        
        // 3. 解析载荷
        Json::Value payload;
        Json::Reader reader;
        string decodedPayload = Base64Decode(parts[1]);
        if (!reader.parse(decodedPayload, payload)) return false;
        
        // 4. 验证过期时间
        int exp = payload["exp"].asInt();
        if (exp < time(NULL)) return false;
        
        return true;
    }
};
```

### 6. CORS支持

```cpp
void AddCORSHeaders(HTTP_RESPONSE& response) {
    response.headers["Access-Control-Allow-Origin"] = "*";
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization";
    response.headers["Access-Control-Max-Age"] = "3600";
}
```

## 配置管理

### Web服务配置
```ini
[WebServer]
# HTTP配置
HTTPPort=8080
HTTPEnable=true

# HTTPS配置
HTTPSPort=8443
HTTPSEnable=true
SSLCertFile=/etc/ssl/server.crt
SSLKeyFile=/etc/ssl/server.key

# 会话配置
SessionTimeout=3600
MaxSessions=1000

# 文件服务
UploadPath=/var/upload
DownloadPath=/var/download
MaxFileSize=104857600

# 安全配置
JWTSecret=your-secret-key-here
JWTExpireTime=86400
EnableCORS=true

# WebSocket
WebSocketEnable=true
WebSocketMaxConnections=100
```

## 性能优化

### 1. 连接池
- HTTP连接复用
- 数据库连接池
- 减少连接开销

### 2. 缓存策略
- 静态资源缓存
- API响应缓存
- 会话缓存

### 3. 异步处理
- 异步请求处理
- 非阻塞IO
- 工作线程池

## 安全考虑

### 1. 认证授权
- JWT令牌认证
- 基于角色的访问控制
- API密钥管理

### 2. 数据安全
- HTTPS加密传输
- 输入验证
- SQL注入防护

### 3. 限流防护
- API访问限流
- DDoS防护
- 暴力破解防护

## 使用示例

### API调用示例
```javascript
// 登录
fetch('http://server:8080/api/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        username: 'admin',
        password: 'password'
    })
})
.then(response => response.json())
.then(data => {
    const token = data.token;
    // 使用token进行后续请求
});

// 获取实时数据
fetch('http://server:8080/api/data/realtime?station=ST001', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log(data);
});
```

## 注意事项

1. **SSL证书**: 生产环境必须使用有效的SSL证书
2. **密钥管理**: JWT密钥需要安全存储
3. **文件上传**: 需要验证文件类型和大小
4. **并发控制**: 注意控制并发连接数
5. **日志记录**: 记录所有API访问日志
