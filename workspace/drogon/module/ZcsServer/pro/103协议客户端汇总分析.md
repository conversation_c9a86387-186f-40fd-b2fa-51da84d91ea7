# 103协议客户端汇总分析

## 概述
Dragon项目包含多个IEC 60870-5-103协议客户端实现，针对不同电网公司和地区的特定需求进行了定制化开发。

## 103客户端分类

### 1. 国网标准103客户端
- **ZcsGw103MsCliPro** - 国网103主站客户端
- **ZcsGw103MmCliPro** - 国网103主从客户端
- **ZcsGw103MmSrvPro** - 国网103主从服务端

### 2. 南网标准103客户端
- **ZcsNw103MsCliPro** - 南网103主站客户端
- **ZcsNw103MmCliPro** - 南网103主从客户端
- **ZcsNw103MmSrvPro** - 南网103主从服务端
- **ZcsNw103v3MsCliPro** - 南网103v3主站客户端

### 3. 省网定制103客户端
- **ZcsFuJian103MsCliPro** - 福建103主站客户端
- **ZcsHlj103MsCliPro** - 黑龙江103主站客户端
- **ZcsHuBei103MsCliPro** - 湖北103主站客户端
- **ZcsLn103MsCliPro** - 辽宁103主站客户端

## 核心组件分析

### 1. ZcsGw103MsCliPro - 国网103主站客户端

#### 文件结构
```
ZcsGw103MsCliPro/
├── ZxProRun.cpp/h              # 协议运行入口
├── ZxGB103ASDUHandler.cpp/h    # 国标103 ASDU处理器
├── ZxGB103MsgAttachFactory.cpp/h # 消息附件工厂
└── Hunan_cloud.ini             # 湖南云配置
```

#### ZxProRun.cpp - 协议入口
```cpp
class CXJProGB103ClientWay {
private:
    CLIENT_PRO_INTERFACE& m_ProInterface;  // 客户端接口
    CZxGB103ASDUHandler* m_pASDUHandler;   // ASDU处理器
    CXJAPCIWrapper* m_pAPCIWrapper;        // APCI封装器
    
    // 连接管理
    SOCKET m_socket;                       // 套接字
    bool m_bConnected;                     // 连接状态
    
    // 数据缓存
    queue<ASDU_MSG> m_sendQueue;          // 发送队列
    queue<ASDU_MSG> m_recvQueue;          // 接收队列
    
public:
    // 运行入口
    int Run();
    
    // 连接管理
    int Connect();
    int Disconnect();
    int Reconnect();
    
    // 数据处理
    int SendData(const BYTE* data, int len);
    int RecvData(BYTE* buffer, int maxLen);
    int ProcessRecvData();
    
    // ASDU处理
    int HandleASDU(const ASDU_MSG& msg);
    int CreateASDU(int type, ASDU_MSG& msg);
};
```

#### ZxGB103ASDUHandler.h - 国标103 ASDU处理器
```cpp
class CZxGB103ASDUHandler : public CZxASDUHandler {
private:
    // 国标特殊处理
    map<int, GB_ASDU_HANDLER> m_mapGBHandlers;
    
    // 数据映射
    map<int, POINT_INFO> m_mapPointInfo;
    
public:
    // 国标103特殊ASDU类型处理
    int HandleType1_GB(const ASDU_MSG& msg);   // 带时标的保护事件
    int HandleType2_GB(const ASDU_MSG& msg);   // 带相对时间的保护事件
    int HandleType3_GB(const ASDU_MSG& msg);   // 被测值I
    int HandleType4_GB(const ASDU_MSG& msg);   // 带相对时间的被测值
    int HandleType5_GB(const ASDU_MSG& msg);   // 标识
    int HandleType6_GB(const ASDU_MSG& msg);   // 时间同步
    int HandleType7_GB(const ASDU_MSG& msg);   // 总查询启动
    int HandleType8_GB(const ASDU_MSG& msg);   // 总查询终止
    int HandleType10_GB(const ASDU_MSG& msg);  // 通用分类数据
    int HandleType11_GB(const ASDU_MSG& msg);  // 通用分类标识
    int HandleType23_GB(const ASDU_MSG& msg);  // 扰动数据传输准备就绪
    int HandleType26_GB(const ASDU_MSG& msg);  // 扰动数据传输准备
    int HandleType27_GB(const ASDU_MSG& msg);  // 扰动数据传输
    int HandleType28_GB(const ASDU_MSG& msg);  // 扰动数据传输结束
    
    // 故障录波处理
    int ProcessDisturbanceData(const DISTURBANCE_DATA& data);
    int RequestDisturbanceFile(int fileID);
    int ReceiveDisturbanceFile(const BYTE* data, int len);
};
```

### 2. 南网103客户端特点

#### ZcsNw103v3MsCliPro - 南网103v3特殊实现
```cpp
class CZxNW103v3Handler {
private:
    // 南网v3特殊字段
    struct NW_V3_EXTENSION {
        BYTE byDeviceType;      // 设备类型
        BYTE byManufacturer;    // 厂家代码
        WORD wProtocolVer;      // 协议版本
        BYTE byExtData[32];     // 扩展数据
    };
    
public:
    // 南网v3特殊处理
    int HandleNWv3Login(const LOGIN_MSG& msg);
    int HandleNWv3Extension(const NW_V3_EXTENSION& ext);
    int ProcessNWv3SpecialData(const BYTE* data, int len);
    
    // 南网加密
    int EncryptNWv3Data(const BYTE* plain, int plainLen, 
                       BYTE* cipher, int& cipherLen);
    int DecryptNWv3Data(const BYTE* cipher, int cipherLen,
                       BYTE* plain, int& plainLen);
};
```

### 3. 省网定制化实现

#### 福建103特殊处理
```cpp
class CZxFJ103Handler {
public:
    // 福建特殊ASDU类型
    int HandleFJSpecialType50(const ASDU_MSG& msg);  // 福建定制类型50
    int HandleFJSpecialType51(const ASDU_MSG& msg);  // 福建定制类型51
    
    // 福建特殊功能
    int ProcessFJWaveFile(const FJ_WAVE_FILE& file);
    int HandleFJExtendedData(const FJ_EXTENDED& data);
};
```

#### 黑龙江103特殊处理
```cpp
class CZxHLJ103Handler {
public:
    // 黑龙江冬季特殊处理
    int HandleColdWeatherMode(bool enable);
    int AdjustTimeoutForColdWeather(int baseTimeout);
    
    // 黑龙江特殊数据格式
    int ProcessHLJDataFormat(const HLJ_DATA& data);
};
```

#### 湖北103特殊处理
```cpp
class CZxHB103Handler {
public:
    // 湖北三峡相关特殊处理
    int HandleThreeGorgesData(const TG_DATA& data);
    int ProcessHydropowerData(const HYDRO_DATA& data);
    
    // 湖北特殊告警
    int HandleHBSpecialAlarm(const HB_ALARM& alarm);
};
```

#### 辽宁103特殊处理
```cpp
class CZxLN103Handler {
public:
    // 辽宁风电场特殊处理
    int HandleWindFarmData(const WIND_DATA& data);
    int ProcessWindTurbineStatus(const TURBINE_STATUS& status);
    
    // 辽宁特殊配置
    int LoadLNSpecialConfig(const LN_CONFIG& config);
};
```

## 103主从模式实现

### ZcsGw103MmCliPro - 主从客户端
```cpp
class CMasterSlaveClient {
private:
    enum Role {
        ROLE_MASTER,    // 主机
        ROLE_SLAVE      // 从机
    };
    
    Role m_role;                          // 当前角色
    CMasterConnection* m_pMaster;         // 主连接
    CSlaveConnection* m_pSlave;           // 从连接
    
public:
    // 角色切换
    int SwitchRole(Role newRole);
    int HandleRoleChange();
    
    // 主从同步
    int SyncWithPeer();
    int HandleSyncRequest(const SYNC_REQ& req);
    int HandleSyncResponse(const SYNC_RESP& resp);
    
    // 数据分发
    int DistributeData(const DATA& data);
    int MergeSlaveData(const SLAVE_DATA& slaveData);
};
```

### ZcsGw103MmSrvPro - 主从服务端
```cpp
class CMasterSlaveServer {
private:
    // 客户端管理
    map<int, CLIENT_INFO> m_mapMasterClients;  // 主机客户端
    map<int, CLIENT_INFO> m_mapSlaveClients;   // 从机客户端
    
public:
    // 客户端注册
    int RegisterMasterClient(const CLIENT_INFO& info);
    int RegisterSlaveClient(const CLIENT_INFO& info);
    
    // 数据路由
    int RouteToMaster(const DATA& data);
    int RouteToSlave(const DATA& data);
    
    // 负载均衡
    int BalanceLoad();
    CLIENT_INFO* SelectBestClient();
};
```

## 通用功能实现

### 1. 连接管理
```cpp
class CConnectionManager {
private:
    // 连接池
    vector<CONNECTION*> m_vecConnections;
    
    // 重连策略
    int m_nMaxRetry;           // 最大重试次数
    int m_nRetryInterval;      // 重试间隔
    
public:
    // 连接操作
    CONNECTION* CreateConnection(const CONN_PARAM& param);
    void CloseConnection(CONNECTION* conn);
    
    // 自动重连
    void EnableAutoReconnect(bool enable);
    void CheckConnections();
    void ReconnectFailed();
};
```

### 2. 数据缓存
```cpp
class CDataCache {
private:
    // 缓存策略
    enum CacheStrategy {
        CACHE_FIFO,     // 先进先出
        CACHE_LRU,      // 最近最少使用
        CACHE_PRIORITY  // 优先级
    };
    
    CacheStrategy m_strategy;
    map<int, CACHE_ENTRY> m_mapCache;
    
public:
    // 缓存操作
    void AddToCache(int key, const DATA& data);
    bool GetFromCache(int key, DATA& data);
    void ClearCache();
    
    // 缓存管理
    void SetMaxSize(int size);
    void SetExpireTime(int seconds);
    void CleanExpired();
};
```

### 3. 故障录波文件处理
```cpp
class CDisturbanceFileHandler {
private:
    // 文件传输状态
    enum TransferState {
        TRANS_IDLE,
        TRANS_PREPARING,
        TRANS_TRANSFERRING,
        TRANS_COMPLETE,
        TRANS_ERROR
    };
    
    TransferState m_state;
    DISTURBANCE_FILE m_currentFile;
    
public:
    // 文件请求
    int RequestFile(int fileID);
    int CancelRequest();
    
    // 文件接收
    int ReceiveFileHeader(const FILE_HEADER& header);
    int ReceiveFileData(const BYTE* data, int len);
    int ReceiveFileEnd();
    
    // 文件处理
    int ParseComtradeFile(const string& fileName);
    int ConvertToInternalFormat(const COMTRADE& comtrade);
};
```

## 配置管理

### 通用配置结构
```ini
[Client103]
# 基本配置
ServerIP=*************
ServerPort=2404
LocalPort=0
ClientID=1

# 协议参数
ProtocolType=GB103        # GB103/NW103/NW103v3
LinkAddress=1
CommonAddress=1

# 超时配置
ConnectTimeout=30
SendTimeout=10
RecvTimeout=10
HeartbeatInterval=30

# 重连配置
EnableAutoReconnect=true
MaxRetryCount=3
RetryInterval=10

# 缓存配置
CacheStrategy=LRU
MaxCacheSize=1000
CacheExpireTime=3600

# 特殊配置(根据不同省网)
[FujianSpecial]
EnableSpecialType50=true
EnableSpecialType51=true

[HeilongjiangSpecial]
ColdWeatherMode=true
WinterTimeoutMultiplier=2

[HubeiSpecial]
ThreeGorgesMode=true
HydropowerDataEnable=true

[LiaoningSpecial]
WindFarmMode=true
TurbineCount=50
```

## 性能优化策略

### 1. 批量数据处理
- 合并小数据包
- 批量ASDU处理
- 减少网络往返

### 2. 异步处理
- 异步连接
- 异步数据收发
- 异步文件传输

### 3. 内存优化
- 对象池复用
- 缓冲区管理
- 内存预分配

## 故障处理机制

### 1. 连接故障
- 自动重连
- 连接池切换
- 故障通知

### 2. 数据故障
- CRC校验
- 序号检查
- 数据重传

### 3. 协议故障
- 协议状态机重置
- 异常恢复
- 降级处理

## 使用注意事项

1. **协议版本**: 不同电网公司的103协议存在差异，需正确配置
2. **地区特性**: 各省网可能有特殊要求，需要相应的定制化处理
3. **主从模式**: 主从切换需要考虑数据一致性
4. **故障录波**: 大文件传输需要合理的超时和重传机制
5. **性能监控**: 定期监控连接状态和数据处理性能
