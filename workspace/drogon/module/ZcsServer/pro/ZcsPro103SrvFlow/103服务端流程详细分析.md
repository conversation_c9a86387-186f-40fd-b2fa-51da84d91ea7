# ZcsPro103SrvFlow - IEC 60870-5-103服务端流程详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsPro103SrvFlow
- **主要功能**: 实现IEC 60870-5-103协议服务端流程控制
- **设计目的**: 提供103协议的服务端实现，处理客户端连接和数据交换

## 核心文件分析

### 1. ZxPro103ServerFlow.h/cpp - 103服务端流程入口

#### 接口定义
```cpp
// 动态库导出函数
extern "C" int Run(
    SERVER_PRO_INTERFACE* pProInterface,  // 服务端协议接口
    CXJSrvAPCIInterFace* pAPCIHandler,   // APCI层处理器
    CXJSrvASDUFactory* pASDUFactory,      // ASDU工厂
    CLogFile* pLogFile,                   // 日志对象
    CXJMsgCaster* pMsgCaster              // 消息广播器
);
```

#### 功能说明
- **Run函数**: 协议库的入口函数，由框架调用
- **参数传递**: 接收框架提供的各种处理器和工具对象
- **流程控制**: 创建并启动103服务端处理流程

### 2. ZxPro103ServerWay.h/cpp - 103服务端处理方式

#### 类定义
```cpp
class CXJPro103ServerWay {
private:
    // 核心组件
    SERVER_PRO_INTERFACE& m_ProInterface;     // 协议接口
    CXJSrvAPCIInterFace& m_APCIHandler;      // APCI处理器
    CXJSrvASDUFactory& m_ASDUFactory;        // ASDU工厂
    CLogFile& m_LogFile;                     // 日志文件
    CXJMsgCaster* m_pMsgCaster;              // 消息广播器
    CZx103ServerSttpHandler* m_pSttpHandler; // STTP处理器
    
    // 会话管理
    OPERATION103SESSION m_SessionPool[SIZEOFOPERATIONSESSION]; // 会话池
    CLockableObject m_SessionLock;           // 会话锁
    
    // 缓冲区管理
    queue<ASDUMESSAGE*> m_RecvBuffer;        // 接收缓冲区
    queue<ASDUMESSAGE*> m_SendBuffer;        // 发送缓冲区
    queue<ASDUMESSAGE*> m_ResultQueue;       // 结果队列
    
public:
    // 主要方法
    int Run();                                // 运行入口
    int Init();                               // 初始化
    int StartProtocol();                      // 启动协议
    int ReleaseResource();                    // 释放资源
    
    // 会话管理
    void InitSessionPool();                   // 初始化会话池
    OPERATION103SESSION* GetFreeSession();    // 获取空闲会话
    int StartOneSession(OPERATION103SESSION& session); // 启动会话
    bool EndSessionThreadPool();              // 结束会话池
    
    // ASDU处理
    bool PushASDUToRecvBuffer(const ASDUMESSAGE* pASDU);
    ASDUMESSAGE* PullASDUFromSendBuffer();
    bool PushASDUToResultQueue(const ASDUMESSAGE* pASDU);
    
    // 回调函数
    static bool OnPushASDU(const ASDUMESSAGE* pASDU, LPVOID pParam);
    static ASDUMESSAGE* OnPullASDU(LPVOID pParam);
    static void OnDisconnect(LPVOID pParam);
};
```

#### 会话结构体
```cpp
typedef struct _OPERATIONSESSION {
    int iNO;                              // 会话索引
    bool bUsed;                           // 使用状态
    bool bExit;                           // 退出标志
    CXJSrvASDUInterface* pASDUOperation;  // ASDU操作对象
    void* pOperation;                     // 操作对象
    THREAD_ID threadId;                   // 线程ID
    THREAD_HANDLE hThreadHandle;          // 线程句柄
    HNSEM hWaitSem;                       // 信号量
} OPERATION103SESSION;
```

### 3. Zx103ServerSttpHandler.h/cpp - 103服务端STTP处理器

#### 类定义
```cpp
class CZx103ServerSttpHandler {
private:
    // STTP相关
    CSttpMsgProductor* m_pSttpMsgProductor;  // STTP消息生产者
    CXJPublisher* m_pPublisher;              // 发布者
    CXJObserver* m_pObserver;                // 观察者
    
    // 配置信息
    int m_nStationID;                        // 厂站ID
    string m_strDeviceID;                    // 设备ID
    
    // 数据缓存
    map<int, STTP_DATA> m_mapSttpData;       // STTP数据映射
    
public:
    // 初始化和释放
    int Init(const CONFIG& config);
    void Release();
    
    // STTP消息处理
    int HandleSttpMessage(STTPMSG* pMsg);
    int SendSttpMessage(const STTP_DATA& data);
    
    // 103到STTP转换
    int Convert103ToSttp(const ASDU103* pAsdu, STTP_DATA& sttpData);
    int ConvertSttpTo103(const STTP_DATA& sttpData, ASDU103* pAsdu);
    
    // 订阅发布
    int RegisterToSubscribeCenter();
    int UnregisterFromSubscribeCenter();
    
    // 数据处理
    int ProcessYX(const YX_DATA& data);      // 遥信处理
    int ProcessYC(const YC_DATA& data);      // 遥测处理
    int ProcessSOE(const SOE_DATA& data);    // SOE处理
    int ProcessFault(const FAULT_DATA& data); // 故障处理
};
```

## 执行流程分析

### 1. 初始化流程
```
CXJPro103ServerWay::Run()
    ├── Init()
    │   ├── InitTimeOutConfig()      // 初始化超时配置
    │   ├── InitMsgCaster()          // 初始化消息广播器
    │   ├── InitAPCIHandler()        // 初始化APCI处理器
    │   ├── InitSTTPHandler()        // 初始化STTP处理器
    │   └── InitSessionPool()        // 初始化会话池
    └── StartProtocol()              // 启动协议处理
```

### 2. 协议处理流程
```
StartProtocol()
    ├── 注册APCI回调
    │   ├── SetPushASDUCallback(OnPushASDU)
    │   ├── SetPullASDUCallback(OnPullASDU)
    │   └── SetDisconnectCallback(OnDisconnect)
    ├── 启动APCI监听
    └── 进入主循环
        ├── 处理接收缓冲区
        ├── 分发ASDU到会话
        └── 处理结果队列
```

### 3. ASDU处理流程
```
OnPushASDU() [接收ASDU]
    └── PushASDUToRecvBuffer()
        └── m_RecvBuffer.push(pASDU)

主循环处理
    ├── 从接收缓冲区取ASDU
    ├── GetFreeSession()          // 获取空闲会话
    ├── StartOneSession()         // 启动会话处理
    │   └── OperationThreadProc()
    │       ├── 解析ASDU类型
    │       ├── 调用相应处理函数
    │       └── 生成响应ASDU
    └── PushASDUToResultQueue()   // 放入结果队列

OnPullASDU() [发送ASDU]
    └── PullASDUFromSendBuffer()
        └── return m_SendBuffer.front()
```

### 4. 会话管理流程
```
会话池初始化
    └── InitSessionPool()
        ├── 创建SIZEOFOPERATIONSESSION个会话
        ├── 初始化信号量
        └── 设置初始状态

会话分配
    └── GetFreeSession()
        ├── 遍历会话池
        ├── 查找bUsed==false的会话
        └── 标记为使用中

会话处理
    └── OperationThreadProc()
        ├── 等待信号量
        ├── 处理ASDU
        ├── 释放会话
        └── 循环等待下一个任务
```

### 5. STTP集成流程
```
InitSTTPHandler()
    ├── 创建CZx103ServerSttpHandler实例
    ├── 注册到订阅中心
    └── 设置数据回调

103到STTP转换
    └── Convert103ToSttp()
        ├── 解析103数据
        ├── 映射到STTP格式
        └── 填充STTP_DATA结构

STTP到103转换
    └── ConvertSttpTo103()
        ├── 解析STTP数据
        ├── 映射到103格式
        └── 生成ASDU
```

## 数据结构

### 1. ASDU消息结构
```cpp
struct ASDUMESSAGE {
    BYTE TypeID;           // 类型标识
    BYTE VSQ;             // 可变结构限定词
    BYTE COT;             // 传送原因
    BYTE ADDR;            // 公共地址
    BYTE FUN;             // 功能类型
    BYTE INF;             // 信息序号
    BYTE* pData;          // 数据指针
    int nDataLen;         // 数据长度
};
```

### 2. 配置结构
```cpp
struct TIMEOUT_CONFIG {
    int T0;               // 连接建立超时
    int T1;               // 发送或测试APDU超时
    int T2;               // 无数据报文时确认超时
    int T3;               // 测试帧发送间隔
};
```

## 关键技术点

### 1. 线程池技术
- **目的**: 避免频繁创建销毁线程
- **实现**: 预创建固定数量的工作线程
- **调度**: 使用信号量控制任务分配

### 2. 双缓冲机制
- **接收缓冲**: 存储待处理的ASDU
- **发送缓冲**: 存储待发送的ASDU
- **优点**: 解耦接收和处理，提高并发性

### 3. 回调机制
- **OnPushASDU**: APCI层推送ASDU时调用
- **OnPullASDU**: APCI层拉取ASDU时调用
- **OnDisconnect**: 连接断开时调用

### 4. 协议转换
- **103到STTP**: 将103协议数据转换为内部STTP格式
- **STTP到103**: 将内部STTP格式转换为103协议
- **映射表**: 维护点号和地址的映射关系

## 性能优化

### 1. 会话池复用
```cpp
// 避免频繁创建销毁会话对象
OPERATION103SESSION* GetFreeSession() {
    CCsLocker lock(&m_SessionLock);
    for (int i = 0; i < SIZEOFOPERATIONSESSION; i++) {
        if (!m_SessionPool[i].bUsed) {
            m_SessionPool[i].bUsed = true;
            return &m_SessionPool[i];
        }
    }
    return NULL;
}
```

### 2. 批量处理
```cpp
// 批量处理接收缓冲区中的ASDU
while (!m_RecvBuffer.empty()) {
    ASDUMESSAGE* pASDU = m_RecvBuffer.front();
    m_RecvBuffer.pop();
    ProcessASDU(pASDU);
}
```

### 3. 异步处理
- 接收和处理分离
- 使用队列缓冲
- 多线程并发处理

## 错误处理

### 1. 超时处理
- T0超时：连接建立失败
- T1超时：重发或断开连接
- T2超时：发送S帧确认
- T3超时：发送测试帧

### 2. 异常处理
```cpp
try {
    // 处理ASDU
    ProcessASDU(pASDU);
} catch (exception& e) {
    WriteLog(e.what(), LOG_ERROR);
    // 生成否定确认
    SendNACK(pASDU);
}
```

## 配置示例

### 103服务端配置
```ini
[103Server]
Port=2404
MaxConnections=10
SessionPoolSize=20

[Timeout]
T0=30
T1=15
T2=10
T3=20

[Station]
StationID=1
DeviceID=DEV001

[STTP]
Enable=true
PublisherID=103_SERVER
```

## 使用注意事项

1. **会话池大小**: 根据并发连接数配置
2. **超时参数**: 根据网络环境调整
3. **缓冲区大小**: 根据数据量调整
4. **日志级别**: 生产环境避免DEBUG级别
5. **资源释放**: 确保正确释放所有资源
