# ZcsProApciHdl - APCI处理器详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsProApciHdl
- **主要功能**: 处理IEC 60870-5-104协议的APCI（应用规约控制信息）层
- **设计目的**: 管理104协议的传输层控制，包括启动、停止、测试等控制帧

## 核心组件

### 1. APCI帧处理器
- **功能职责**：
  - 解析APCI控制帧
  - 生成APCI响应帧
  - 管理序列号
  - 控制流量

### 2. 控制帧类型

#### U格式帧（无编号控制功能）
- **STARTDT**：启动数据传输
- **STOPDT**：停止数据传输
- **TESTFR**：测试帧

#### S格式帧（编号监视功能）
- **确认接收**：确认I格式帧接收

#### I格式帧（信息传输）
- **数据传输**：携带ASDU数据

## 主要功能实现

### APCI帧结构
```cpp
// APCI帧头定义
struct APCIHeader {
    uint8_t  startByte;     // 启动字节 0x68
    uint8_t  length;        // APDU长度
    uint8_t  control[4];    // 控制域
};

// 控制域类型
enum FrameType {
    I_FORMAT = 0,   // I格式帧
    S_FORMAT = 1,   // S格式帧
    U_FORMAT = 3    // U格式帧
};

// U格式控制功能
enum UControlFunction {
    STARTDT_ACT = 0x07,     // 启动激活
    STARTDT_CON = 0x0B,     // 启动确认
    STOPDT_ACT  = 0x13,     // 停止激活
    STOPDT_CON  = 0x23,     // 停止确认
    TESTFR_ACT  = 0x43,     // 测试激活
    TESTFR_CON  = 0x83      // 测试确认
};
```

### APCI处理类
```cpp
class CApciHandler {
public:
    // 处理接收的APCI帧
    int ProcessAPCI(const uint8_t* buffer, int length);
    
    // 生成U格式帧
    int GenerateUFrame(UControlFunction func, uint8_t* buffer);
    
    // 生成S格式帧
    int GenerateSFrame(uint16_t recvSeq, uint8_t* buffer);
    
    // 生成I格式帧
    int GenerateIFrame(uint16_t sendSeq, uint16_t recvSeq, 
                      const uint8_t* asdu, int asduLen, uint8_t* buffer);
    
private:
    // 序列号管理
    uint16_t m_sendSeq;     // 发送序列号
    uint16_t m_recvSeq;     // 接收序列号
    
    // 连接状态
    bool m_dataTransferEnabled;  // 数据传输使能
    
    // 定时器管理
    Timer m_t1Timer;    // 发送或测试APDU的超时
    Timer m_t2Timer;    // 无数据报文时确认的超时
    Timer m_t3Timer;    // 长期空闲状态下发送测试帧的超时
};
```

## 序列号管理

### 发送序列号（N(S)）
- **范围**：0-32767
- **递增**：每发送一个I格式帧递增
- **重置**：达到最大值后回绕

### 接收序列号（N(R)）
- **范围**：0-32767
- **确认**：表示期望接收的下一个I格式帧序号
- **更新**：接收到正确的I格式帧后更新

## 流量控制

### K值参数
- **定义**：最大未确认I格式帧数量
- **默认值**：12
- **作用**：防止接收缓冲区溢出

### W值参数
- **定义**：最迟确认接收的I格式帧数量
- **默认值**：8
- **作用**：触发S格式帧发送

## 定时器管理

### T1定时器（15秒）
- **触发**：发送I格式帧或U格式帧
- **超时**：关闭TCP连接

### T2定时器（10秒）
- **触发**：接收到I格式帧
- **超时**：发送S格式帧确认

### T3定时器（20秒）
- **触发**：空闲状态
- **超时**：发送TESTFR测试帧

## 状态机管理

### 连接状态
```cpp
enum ConnectionState {
    DISCONNECTED,       // 断开连接
    CONNECTED,         // 已连接
    START_PENDING,     // 等待启动确认
    STARTED,          // 已启动
    STOP_PENDING      // 等待停止确认
};
```

### 状态转换
- `DISCONNECTED` → `CONNECTED`：TCP连接建立
- `CONNECTED` → `START_PENDING`：发送STARTDT
- `START_PENDING` → `STARTED`：接收STARTDT确认
- `STARTED` → `STOP_PENDING`：发送STOPDT
- `STOP_PENDING` → `CONNECTED`：接收STOPDT确认

## 错误处理

### 错误类型
- **序列号错误**：接收序列号不连续
- **超时错误**：定时器超时
- **格式错误**：APCI帧格式错误
- **状态错误**：在错误状态接收控制帧

### 错误恢复
- **重传机制**：基于序列号的重传
- **连接重置**：严重错误时重置连接
- **日志记录**：详细记录错误信息

## 性能优化

### 优化策略
- **批量确认**：减少S格式帧发送
- **动态调整**：根据网络状况调整参数
- **缓冲管理**：优化收发缓冲区

## 配置参数

### APCI参数配置
```ini
[APCI]
K = 12          # 最大未确认帧数
W = 8           # 最迟确认帧数
T1 = 15         # T1定时器（秒）
T2 = 10         # T2定时器（秒）
T3 = 20         # T3定时器（秒）
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建APCI处理器
CApciHandler apciHandler;

// 2. 初始化参数
apciHandler.SetKValue(12);
apciHandler.SetWValue(8);

// 3. 启动数据传输
uint8_t startFrame[6];
apciHandler.GenerateUFrame(STARTDT_ACT, startFrame);
socket.Send(startFrame, 6);

// 4. 处理接收数据
uint8_t recvBuffer[256];
int len = socket.Receive(recvBuffer, 256);
apciHandler.ProcessAPCI(recvBuffer, len);

// 5. 发送I格式帧
uint8_t asdu[249];
// 填充ASDU数据...
uint8_t iframe[255];
int frameLen = apciHandler.GenerateIFrame(asdu, sizeof(asdu), iframe);
socket.Send(iframe, frameLen);
```

## 注意事项
- 严格遵守104协议规范
- 正确维护序列号
- 及时处理定时器
- 合理设置K、W值
- 处理网络异常

## 相关模块
- ZcsPro103SrvFlow: 103服务端流程
- Zcs104VlanSrv: 104 VLAN服务
- ZcsProMainFlow: 协议主流程
