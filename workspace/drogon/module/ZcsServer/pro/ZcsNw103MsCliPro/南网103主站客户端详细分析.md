# ZcsNw103MsCliPro - 南网IEC103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsNw103MsCliPro
- **主要功能**: 实现南方电网IEC 60870-5-103协议标准版主站客户端
- **设计目的**: 符合南网标准的103协议实现，作为主站采集子站数据

## 核心组件

### 1. 南网103标准实现
- **标准遵循**：严格遵循南网103规约
- **兼容性**：兼容各厂家装置
- **可靠性**：工业级可靠性设计
- **扩展性**：支持南网定制扩展

### 2. 主站客户端架构
```cpp
class CNw103MasterClient {
public:
    // 连接管理
    int Connect(const std::string& ip, int port);
    int Disconnect();
    int Reconnect();
    
    // 初始化流程
    int InitializeStation();
    int ResetCommunication();
    int TestCommand();
    
    // 数据采集
    int GeneralInterrogation();    // 总召唤
    int ClockSync();               // 时钟同步
    int ReadGroup(int groupNo);    // 读取组数据
    
    // 控制操作
    int SelectControl(int addr, int value);
    int ExecuteControl(int addr, int value);
    int CancelControl(int addr);
    
private:
    // 协议处理器
    Nw103ProtocolHandler m_protocolHandler;
    
    // 数据管理器
    Nw103DataManager m_dataManager;
};
```

### 3. 数据模型
```cpp
// 南网103数据结构
struct Nw103DataPoint {
    uint16_t funcType;      // 功能类型
    uint8_t infNumber;      // 信息序号
    uint16_t address;       // 信息体地址
    
    union Value {
        bool boolVal;       // 开关量
        float floatVal;     // 模拟量
        int intVal;         // 整数值
        uint8_t rawData[8]; // 原始数据
    } value;
    
    uint8_t quality;        // 品质
    uint32_t timestamp;     // 时标
    bool isValid;          // 有效性
};
```

## 协议处理实现

### 1. 链路层处理
```cpp
class CNw103LinkLayer {
public:
    // 固定帧
    int SendFixedFrame(uint8_t control);
    
    // 可变帧
    int SendVariableFrame(const uint8_t* asdu, int length);
    
    // 帧接收处理
    int ReceiveFrame(uint8_t* buffer, int& length);
    
    // 链路维护
    int SendTestLink();
    int SendResetLink();
    
private:
    // 帧校验
    bool CheckFrame(const uint8_t* frame, int length);
    
    // 序号管理
    uint8_t m_fcb;  // 帧计数位
    uint8_t m_fcv;  // 帧计数有效位
};
```

### 2. 应用层处理
```cpp
class CNw103AppLayer {
public:
    // ASDU处理
    int ProcessASDU(const uint8_t* asdu, int length);
    
    // 生成ASDU
    int GenerateASDU(uint8_t typeId, uint8_t* buffer);
    
    // 数据解析
    int ParseMeasuredValue(const uint8_t* data, float& value);
    int ParseStatusInfo(const uint8_t* data, bool& status);
    int ParseTimeTag(const uint8_t* data, TimeStamp& time);
    
private:
    // ASDU类型处理器
    std::map<uint8_t, AsduHandler> m_asduHandlers;
};
```

## 南网特殊要求

### 1. 时钟同步机制
```cpp
class CNw103ClockSync {
public:
    // 时钟同步命令
    int SendClockSyncCommand(const SystemTime& time);
    
    // 时钟读取
    int ReadDeviceClock(SystemTime& time);
    
    // 时差校正
    int CalculateTimeDiff(int& diffMs);
    
    // 自动同步
    int EnableAutoSync(int intervalSec);
    
private:
    // 同步参数
    struct SyncParams {
        bool autoSync;          // 自动同步
        int syncInterval;       // 同步间隔
        int maxTimeDiff;        // 最大时差
        bool adjustDevice;      // 调整装置时间
    } m_syncParams;
};
```

### 2. 扰动数据处理
```cpp
class CNw103DisturbanceHandler {
public:
    // 读取扰动值
    int ReadDisturbanceValues(int funcType, int infNo);
    
    // 读取扰动目录
    int ReadDisturbanceDirectory(DisturbanceDir& dir);
    
    // 选择扰动
    int SelectDisturbance(int disturbanceId);
    
    // 传输扰动数据
    int TransferDisturbanceData();
    
private:
    // 扰动缓存
    std::vector<DisturbanceRecord> m_disturbanceCache;
};
```

### 3. 通用分类服务
```cpp
class CNw103GenericService {
public:
    // 读取制造厂商信息
    int ReadManufacturerInfo(ManufacturerInfo& info);
    
    // 读取设备标识
    int ReadDeviceIdentification(DeviceId& id);
    
    // 读取功能类型
    int ReadFunctionType(std::vector<FuncType>& types);
    
    // 读取信息序号范围
    int ReadInfoNumberRange(int funcType, InfoRange& range);
    
private:
    // 通用服务数据
    GenericServiceData m_gsData;
};
```

## 数据采集流程

### 1. 初始化流程
```sequence
1. 建立TCP连接
2. 发送复位链路命令
3. 等待链路确认
4. 发送总召唤命令
5. 接收全数据
6. 时钟同步
7. 进入正常运行
```

### 2. 周期性采集
```cpp
class CNw103PeriodicTask {
public:
    // 定时总召
    void PeriodicGeneralCall(int periodSec);
    
    // 定时时钟同步
    void PeriodicClockSync(int periodSec);
    
    // 定时读取组数据
    void PeriodicGroupRead(int groupNo, int periodSec);
    
    // 定时链路测试
    void PeriodicLinkTest(int periodSec);
    
private:
    // 定时器管理
    std::vector<Timer> m_timers;
};
```

## 配置参数

### 南网103配置
```ini
[Nw103_Config]
# 通信参数
DeviceAddress = 1       # 装置地址
LinkAddress = 1         # 链路地址
BaudRate = 9600        # 波特率
DataBits = 8          # 数据位
StopBits = 1          # 停止位
Parity = E             # 校验位(E/O/N)

# 协议参数
AsduAddress = 1        # ASDU地址
CauseSize = 1         # 传送原因长度
AsduAddressSize = 1   # ASDU地址长度
InfoAddressSize = 1   # 信息体地址长度

# 采集参数
GeneralCallPeriod = 60    # 总召周期(秒)
ClockSyncPeriod = 3600    # 时钟同步周期(秒)
LinkTestPeriod = 30       # 链路测试周期(秒)

# 超时参数
ConnectTimeout = 10000    # 连接超时(ms)
ResponseTimeout = 5000    # 响应超时(ms)
RetryCount = 3           # 重试次数
```

## 数据处理

### 1. 数据转换
```cpp
class CNw103DataConverter {
public:
    // 标度转换
    float ConvertScaledValue(int16_t raw, float factor);
    
    // 归一化值转换
    float ConvertNormalizedValue(int16_t normalized);
    
    // 短浮点数转换
    float ConvertShortFloat(uint32_t ieeeFloat);
    
    // 品质转换
    Quality ConvertQuality(uint8_t iec103Quality);
    
private:
    // 转换表
    std::map<int, ConversionRule> m_conversionRules;
};
```

### 2. 数据校验
- **CRC校验**：帧完整性校验
- **序号校验**：帧序号连续性
- **时标校验**：时标合理性
- **范围校验**：数值范围检查

## 异常处理

### 1. 通信异常
```cpp
enum CommError {
    ERR_CONNECT_FAILED = 0x1001,     // 连接失败
    ERR_SEND_FAILED = 0x1002,        // 发送失败
    ERR_RECV_TIMEOUT = 0x1003,       // 接收超时
    ERR_FRAME_ERROR = 0x1004,        // 帧错误
    ERR_CRC_ERROR = 0x1005,          // CRC错误
    ERR_SEQ_ERROR = 0x1006           // 序号错误
};
```

### 2. 协议异常
```cpp
enum ProtocolError {
    ERR_UNKNOWN_TYPE = 0x2001,       // 未知类型
    ERR_INVALID_ADDR = 0x2002,       // 无效地址
    ERR_INVALID_VALUE = 0x2003,      // 无效值
    ERR_NOT_SUPPORTED = 0x2004       // 不支持
};
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建客户端
auto client = std::make_unique<CNw103MasterClient>();

// 2. 配置参数
Nw103Config config;
config.deviceAddr = 1;
config.linkAddr = 1;
client->SetConfig(config);

// 3. 连接装置
client->Connect("*************", 2404);

// 4. 初始化
client->InitializeStation();

// 5. 总召唤
client->GeneralInterrogation();

// 6. 设置数据回调
client->SetDataCallback([](const Nw103DataPoint& data) {
    std::cout << "Received: " << data.address 
              << " = " << data.value.floatVal << std::endl;
});

// 7. 启动周期任务
client->StartPeriodicTasks();

// 8. 执行控制
client->ExecuteControl(0x4001, 1);  // 合闸
```

## 性能优化
- **连接池**：复用TCP连接
- **数据缓存**：缓存常用数据
- **批量处理**：批量读取数据
- **异步IO**：非阻塞通信

## 注意事项
- 遵守南网103规约要求
- 注意链路层时序
- 正确处理异常响应
- 合理设置超时参数
- 定期维护链路

## 相关模块
- ZcsNw103v3MsCliPro: 南网103 V3版客户端
- ZcsNw103MmCliPro: 南网103主主站客户端
- ZcsPro103SrvFlow: 103服务端流程
