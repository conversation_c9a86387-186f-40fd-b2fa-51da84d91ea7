# ZcsHuBei103MsCliPro - 湖北电网103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsHuBei103MsCliPro
- **主要功能**: 实现湖北电网IEC103协议主站客户端，符合湖北电网特定要求
- **设计目的**: 满足湖北电网对103协议的定制化需求，实现与湖北电网设备的通信

## 核心组件

### 1. 湖北电网特殊要求
- **扩展信息体**：支持湖北电网扩展的信息体地址
- **特殊功能码**：湖北定制的功能类型码
- **数据格式**：特定的数据编码格式
- **通信参数**：湖北电网标准通信参数

### 2. 客户端实现
```cpp
class CHuBei103Client {
public:
    // 初始化客户端
    int Initialize(const HuBeiConfig& config);
    
    // 连接装置
    int ConnectDevice(const std::string& ip, int port);
    
    // 湖北特殊召唤
    int HuBeiGeneralCall(int stationNo);
    
    // 读取湖北扩展数据
    int ReadHuBeiExtData(int funcType, int infNo);
    
    // 湖北定制控制
    int HuBeiControl(const HBControlCmd& cmd);
    
private:
    // 湖北配置
    HuBeiConfig m_config;
    
    // 协议处理器
    HuBeiProtocolHandler m_handler;
};
```

### 3. 湖北扩展数据类型
```cpp
// 湖北电网扩展类型
namespace HuBeiExtTypes {
    // 扩展功能类型
    enum FunctionType {
        // 标准功能类型
        STANDARD_BASE = 0,
        
        // 湖北扩展功能类型
        HB_PROTECTION_EXT = 160,    // 保护扩展
        HB_MEASUREMENT_EXT = 161,   // 测量扩展
        HB_FAULT_RECORD = 162,      // 故障录波
        HB_POWER_QUALITY = 163,     // 电能质量
        HB_DEVICE_STATUS = 164,     // 设备状态
        HB_MAINTENANCE = 165        // 检修信息
    };
    
    // 湖北扩展信息序号
    enum InfoNumber {
        // 保护扩展信息
        HB_DIFF_PROTECT = 240,      // 差动保护
        HB_DISTANCE_PROTECT = 241,  // 距离保护
        HB_OVERCURRENT = 242,       // 过流保护
        HB_EARTH_FAULT = 243,       // 接地保护
        
        // 测量扩展信息
        HB_HARMONICS = 244,         // 谐波数据
        HB_POWER_FACTOR = 245,      // 功率因数
        HB_ENERGY_DATA = 246        // 电能数据
    };
}
```

## 湖北协议处理

### 1. 数据帧格式
```cpp
class CHuBeiFrameProcessor {
public:
    // 湖北帧结构
    struct HuBeiFrame {
        uint8_t start;              // 起始字节
        uint8_t length;             // 长度
        uint8_t control;            // 控制域
        uint16_t address;           // 地址域(湖北2字节)
        uint8_t asdu[250];          // ASDU
        uint8_t checksum;           // 校验和
        uint8_t end;                // 结束字节
    };
    
    // 处理湖北帧
    int ProcessHuBeiFrame(const uint8_t* buffer, int length) {
        HuBeiFrame frame;
        
        // 解析帧头
        if (!ParseFrameHeader(buffer, frame)) {
            return ERR_FRAME_FORMAT;
        }
        
        // 验证湖北校验
        if (!VerifyHuBeiChecksum(frame)) {
            return ERR_CHECKSUM;
        }
        
        // 处理ASDU
        return ProcessHuBeiASDU(frame.asdu, frame.length);
    }
    
private:
    // 湖北校验算法
    bool VerifyHuBeiChecksum(const HuBeiFrame& frame);
};
```

### 2. 湖北ASDU处理
```cpp
class CHuBeiAsduHandler {
public:
    // 处理湖北ASDU
    int HandleHuBeiASDU(const uint8_t* asdu, int length) {
        // 解析类型标识
        uint8_t typeId = asdu[0];
        
        // 湖北扩展类型处理
        if (typeId >= 160) {
            return HandleHuBeiExtended(asdu, length);
        }
        
        // 标准类型处理
        return HandleStandard(asdu, length);
    }
    
    // 处理湖北扩展类型
    int HandleHuBeiExtended(const uint8_t* asdu, int length) {
        uint8_t typeId = asdu[0];
        
        switch (typeId) {
            case HB_PROTECTION_EXT:
                return ProcessProtectionExt(asdu, length);
            case HB_MEASUREMENT_EXT:
                return ProcessMeasurementExt(asdu, length);
            case HB_FAULT_RECORD:
                return ProcessFaultRecord(asdu, length);
            case HB_POWER_QUALITY:
                return ProcessPowerQuality(asdu, length);
            default:
                return ERR_UNKNOWN_TYPE;
        }
    }
    
private:
    // 保护扩展数据处理
    int ProcessProtectionExt(const uint8_t* asdu, int length);
    
    // 测量扩展数据处理
    int ProcessMeasurementExt(const uint8_t* asdu, int length);
};
```

### 3. 湖北数据转换
```cpp
class CHuBeiDataConverter {
public:
    // 湖北浮点数转换
    float ConvertHuBeiFloat(const uint8_t* data) {
        // 湖北特殊浮点格式
        uint32_t raw = (data[0] << 24) | (data[1] << 16) | 
                      (data[2] << 8) | data[3];
        
        // 应用湖北转换规则
        return ApplyHuBeiFloatRule(raw);
    }
    
    // 湖北时标转换
    uint64_t ConvertHuBeiTime(const uint8_t* data) {
        // 湖北时标格式：年月日时分秒毫秒
        HuBeiTimeStamp ts;
        ts.year = data[0] + 2000;
        ts.month = data[1];
        ts.day = data[2];
        ts.hour = data[3];
        ts.minute = data[4];
        ts.second = data[5];
        ts.millisecond = (data[6] << 8) | data[7];
        
        return ToEpochTime(ts);
    }
    
    // 湖北品质转换
    uint8_t ConvertHuBeiQuality(uint8_t hbQuality) {
        // 映射湖北品质到标准品质
        uint8_t stdQuality = 0;
        
        if (hbQuality & 0x01) stdQuality |= QDS_INVALID;
        if (hbQuality & 0x02) stdQuality |= QDS_BLOCKED;
        if (hbQuality & 0x04) stdQuality |= QDS_SUBSTITUTED;
        if (hbQuality & 0x08) stdQuality |= QDS_NOT_TOPICAL;
        
        return stdQuality;
    }
    
private:
    // 湖北浮点转换规则
    float ApplyHuBeiFloatRule(uint32_t raw);
};
```

## 湖北特殊功能

### 1. 故障录波处理
```cpp
class CHuBeiFaultRecorder {
public:
    // 读取录波目录
    int ReadRecorderDirectory() {
        // 发送湖北录波目录命令
        uint8_t cmd[] = {0x68, 0x09, 0x09, 0x68, 
                        HB_FAULT_RECORD, 0x01, 0x06, 0x00, 0x01};
        SendCommand(cmd, sizeof(cmd));
        
        // 接收目录响应
        return ReceiveDirectory();
    }
    
    // 选择录波文件
    int SelectRecorderFile(int fileNo) {
        // 构造湖北选择命令
        HBSelectCmd cmd;
        cmd.funcType = HB_FAULT_RECORD;
        cmd.infNo = 0x02;
        cmd.fileNo = fileNo;
        
        return SendHBCommand(cmd);
    }
    
    // 传输录波数据
    int TransferRecorderData(std::vector<uint8_t>& data) {
        // 湖北录波数据传输
        return ReceiveHBRecorderData(data);
    }
    
private:
    // 录波缓冲区
    std::vector<uint8_t> m_recorderBuffer;
};
```

### 2. 电能质量监测
```cpp
class CHuBeiPowerQuality {
public:
    // 电能质量数据
    struct PowerQualityData {
        float voltage[3];           // 三相电压
        float current[3];           // 三相电流
        float frequency;            // 频率
        float powerFactor[3];       // 功率因数
        float thd_v[3];            // 电压谐波畸变率
        float thd_i[3];            // 电流谐波畸变率
        float harmonics[50];        // 谐波分量
    };
    
    // 读取电能质量数据
    int ReadPowerQuality(PowerQualityData& data) {
        // 发送湖北电能质量查询
        SendHBQuery(HB_POWER_QUALITY, 0x01);
        
        // 接收数据
        uint8_t buffer[1024];
        int len = ReceiveData(buffer, sizeof(buffer));
        
        // 解析湖北格式数据
        return ParseHBPowerQuality(buffer, len, data);
    }
    
private:
    // 解析湖北电能质量数据
    int ParseHBPowerQuality(const uint8_t* data, int len, 
                           PowerQualityData& pq);
};
```

### 3. 设备维护信息
```cpp
class CHuBeiMaintenance {
public:
    // 维护信息
    struct MaintenanceInfo {
        std::string deviceId;       // 设备编号
        std::string lastMaintDate;  // 上次维护日期
        std::string nextMaintDate;  // 下次维护日期
        uint32_t operationCount;    // 操作次数
        float operationTime;        // 运行时间
        std::string maintStatus;    // 维护状态
    };
    
    // 读取维护信息
    int ReadMaintenanceInfo(MaintenanceInfo& info) {
        // 查询湖北维护信息
        return QueryHBMaintenance(info);
    }
    
    // 更新维护记录
    int UpdateMaintenanceRecord(const MaintenanceInfo& info) {
        // 发送湖北维护更新命令
        return SendHBMaintenanceUpdate(info);
    }
    
private:
    std::map<std::string, MaintenanceInfo> m_maintRecords;
};
```

## 配置参数

### 湖北103配置
```ini
[HuBei103_Config]
# 通信参数
DeviceAddress = 1           # 装置地址
LinkAddress = 1             # 链路地址
BaudRate = 9600            # 波特率
DataBits = 8               # 数据位
StopBits = 1               # 停止位
Parity = E                 # 校验(湖北要求偶校验)

# 湖北协议参数
AsduAddressSize = 2         # ASDU地址长度(湖北2字节)
InfoAddressSize = 2         # 信息体地址长度(湖北2字节)
CauseSize = 2              # 传送原因长度(湖北2字节)
EnableHBExtension = true    # 启用湖北扩展

# 湖北特殊参数
HBStationCode = 4201        # 湖北站点代码
HBAreaCode = 027           # 湖北地区代码
HBProtocolVersion = 2.1     # 湖北协议版本
HBChecksumType = CRC16     # 湖北校验类型

# 数据采集参数
GeneralCallPeriod = 30      # 总召周期(秒)
PowerQualityPeriod = 60     # 电能质量采集周期(秒)
MaintenancePeriod = 3600    # 维护信息更新周期(秒)

# 录波参数
RecorderBufferSize = 10240  # 录波缓冲区大小(KB)
RecorderTimeout = 30000     # 录波传输超时(ms)
```

## 错误处理

### 湖北特定错误码
```cpp
enum HuBeiErrorCode {
    HB_ERR_FRAME_FORMAT = 0x3001,    // 湖北帧格式错误
    HB_ERR_CHECKSUM = 0x3002,        // 湖北校验错误
    HB_ERR_UNKNOWN_TYPE = 0x3003,    // 未知湖北类型
    HB_ERR_DATA_CONVERT = 0x3004,    // 数据转换错误
    HB_ERR_STATION_CODE = 0x3005,    // 站点代码错误
    HB_ERR_AREA_CODE = 0x3006,       // 地区代码错误
    HB_ERR_VERSION = 0x3007          // 版本不匹配
};
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建湖北客户端
auto hbClient = std::make_unique<CHuBei103Client>();

// 2. 加载湖北配置
HuBeiConfig config;
config.LoadFromFile("hubei103_config.ini");
hbClient->Initialize(config);

// 3. 连接湖北设备
hbClient->ConnectDevice("192.168.1.100", 2404);

// 4. 执行湖北总召
hbClient->HuBeiGeneralCall(1);

// 5. 读取湖北扩展数据
HBExtData extData;
hbClient->ReadHuBeiExtData(HB_PROTECTION_EXT, HB_DIFF_PROTECT);

// 6. 读取电能质量
PowerQualityData pqData;
hbClient->ReadPowerQuality(pqData);
std::cout << "THD_V: " << pqData.thd_v[0] << "%" << std::endl;

// 7. 读取维护信息
MaintenanceInfo maintInfo;
hbClient->ReadMaintenanceInfo(maintInfo);
std::cout << "Next maintenance: " << maintInfo.nextMaintDate << std::endl;

// 8. 执行湖北控制
HBControlCmd cmd;
cmd.deviceAddr = 0x4201;
cmd.controlType = HB_CONTROL_OPEN;
hbClient->HuBeiControl(cmd);
```

## 注意事项
- 严格遵守湖北电网103规约
- 注意湖北特殊的地址编码
- 正确处理湖北扩展类型
- 使用湖北指定的校验方式
- 注意时区和时标处理

## 相关模块
- ZcsLn103MsCliPro: 辽宁103主站客户端
- ZcsHlj103MsCliPro: 黑龙江103主站客户端
- ZcsFuJian103MsCliPro: 福建103主站客户端
