# ZcsGw103MsCliPro - 国网103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsGw103MsCliPro  
- **主要功能**: 实现国家电网IEC103协议主站客户端，采集国网标准设备数据
- **设计目的**: 符合国网统一标准，实现与国网设备的标准化通信

## 核心组件

### 1. 国网客户端架构
```cpp
class CGw103MasterClient {
public:
    // 初始化国网客户端
    int Initialize(const GwConfig& config);
    
    // 连接国网设备
    int ConnectGwDevice(const std::string& ip, int port);
    
    // 国网标准总召
    int GwGeneralInterrogation();
    
    // 读取国网数据
    int ReadGwData(const GwDataRequest& request);
    
    // 执行国网控制
    int ExecuteGwControl(const GwControlCmd& cmd);
    
private:
    // 国网配置
    GwConfig m_config;
    
    // 国网协议处理器
    GwProtocolHandler m_handler;
};
```

### 2. 国网数据模型
```cpp
namespace GwDataModel {
    // 国网数据点类型
    enum GwPointType {
        GW_YX = 1,      // 遥信
        GW_YC = 2,      // 遥测
        GW_YK = 3,      // 遥控
        GW_YT = 4,      // 遥调
        GW_DD = 5       // 电度
    };
    
    // 国网数据结构
    struct GwDataPoint {
        uint32_t pointId;       // 点号
        GwPointType type;       // 类型
        std::string name;       // 名称
        union {
            bool boolVal;       // 遥信值
            float floatVal;     // 遥测值
            int intVal;         // 整数值
            double doubleVal;   // 电度值
        } value;
        uint8_t quality;        // 品质
        uint64_t timestamp;     // 时标
    };
}
```

## 国网标准实现

### 1. 国网认证流程
```cpp
class CGwAuthentication {
public:
    // 国网统一认证
    int AuthenticateWithGw() {
        // 1. 获取认证服务器
        std::string authServer = GetGwAuthServer();
        
        // 2. 申请认证令牌
        std::string token = RequestAuthToken(authServer);
        
        // 3. 验证令牌
        if (!VerifyToken(token)) {
            return ERR_AUTH_FAILED;
        }
        
        // 4. 建立安全通道
        EstablishSecureChannel(token);
        
        return SUCCESS;
    }
    
private:
    std::string m_authToken;
    SecureChannel m_secureChannel;
};
```

### 2. 国网数据采集
```cpp
class CGwDataCollection {
public:
    // 标准化数据采集
    int CollectGwData() {
        // 1. 执行总召唤
        PerformGeneralCall();
        
        // 2. 采集遥信数据
        CollectYXData();
        
        // 3. 采集遥测数据
        CollectYCData();
        
        // 4. 采集电度数据
        CollectDDData();
        
        // 5. 数据质量检查
        CheckDataQuality();
        
        return SUCCESS;
    }
    
    // 周期性采集
    int PeriodicCollection() {
        // 遥测数据：5秒周期
        ScheduleTask(CollectYC, 5000);
        
        // 遥信数据：变化上送
        EnableChangeDetection();
        
        // 电度数据：15分钟周期
        ScheduleTask(CollectDD, 900000);
        
        return SUCCESS;
    }
    
private:
    DataCollector m_collector;
    TaskScheduler m_scheduler;
};
```

### 3. 国网控制执行
```cpp
class CGwControlExecutor {
public:
    // 遥控操作
    int ExecuteYKControl(const YKCommand& cmd) {
        // 1. 权限检查
        if (!CheckYKPermission(cmd)) {
            return ERR_NO_PERMISSION;
        }
        
        // 2. 防误校验
        if (!AntiMisoperationCheck(cmd)) {
            return ERR_MISOPERATION;
        }
        
        // 3. 选择操作
        SelectControl(cmd.pointId);
        
        // 4. 执行操作
        ExecuteControl(cmd.pointId, cmd.value);
        
        // 5. 确认结果
        return ConfirmControlResult(cmd.pointId);
    }
    
    // 遥调操作
    int ExecuteYTControl(const YTCommand& cmd) {
        // 遥调预置
        PresetValue(cmd.pointId, cmd.targetValue);
        
        // 遥调执行
        ExecuteAdjust(cmd.pointId);
        
        return SUCCESS;
    }
    
private:
    ControlValidator m_validator;
    AntiMisoperationSystem m_antiMis;
};
```

## 数据质量管理

### 1. 品质判断
```cpp
class CGwQualityManager {
public:
    // 品质位定义
    enum QualityBits {
        QDS_GOOD = 0x00,        // 好
        QDS_INVALID = 0x01,     // 无效
        QDS_QUESTIONABLE = 0x02,// 可疑
        QDS_OVERFLOW = 0x04,    // 溢出
        QDS_OUTOFRANGE = 0x08,  // 越限
        QDS_BADREFERENCE = 0x10 // 坏基准
    };
    
    // 品质评估
    uint8_t EvaluateQuality(const GwDataPoint& point) {
        uint8_t quality = QDS_GOOD;
        
        // 检查数据有效性
        if (!IsDataValid(point)) {
            quality |= QDS_INVALID;
        }
        
        // 检查数据范围
        if (IsOutOfRange(point)) {
            quality |= QDS_OUTOFRANGE;
        }
        
        // 检查时标
        if (IsTimeInvalid(point)) {
            quality |= QDS_QUESTIONABLE;
        }
        
        return quality;
    }
    
private:
    QualityChecker m_checker;
};
```

## 配置参数

### 国网103配置
```ini
[Gw103_Config]
# 基本通信参数
DeviceAddress = 1           # 装置地址
LinkAddress = 1             # 链路地址
BaudRate = 9600            # 波特率

# 国网参数
GwStationCode = 110000      # 国网站点代码
GwAreaCode = 010           # 国网地区代码
GwProtocolVersion = 2.0     # 国网协议版本

# 认证参数
AuthServerUrl = https://auth.sgcc.com.cn
AuthMethod = TOKEN          # 认证方式
CertificatePath = /etc/gw103/cert.pem

# 采集参数
YCPeriod = 5               # 遥测周期(秒)
DDPeriod = 900             # 电度周期(秒)
GeneralCallPeriod = 60     # 总召周期(秒)

# 控制参数
EnableYKControl = true      # 启用遥控
EnableYTControl = true      # 启用遥调
ControlTimeout = 10000      # 控制超时(ms)
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建国网客户端
auto gwClient = std::make_unique<CGw103MasterClient>();

// 2. 加载配置
GwConfig config;
config.LoadFromFile("gw103_config.ini");
gwClient->Initialize(config);

// 3. 国网认证
gwClient->AuthenticateWithGw();

// 4. 连接设备
gwClient->ConnectGwDevice("192.168.1.100", 2404);

// 5. 执行总召
gwClient->GwGeneralInterrogation();

// 6. 读取数据
GwDataRequest request;
request.pointType = GW_YC;
request.startId = 1;
request.count = 100;
GwDataResponse response;
gwClient->ReadGwData(request, response);

// 7. 执行控制
GwControlCmd cmd;
cmd.type = GW_YK;
cmd.pointId = 1001;
cmd.value = 1;  // 合闸
gwClient->ExecuteGwControl(cmd);
```

## 注意事项
- 严格遵守国网技术规范
- 确保认证机制正确配置
- 注意防误操作校验
- 定期进行数据质量检查
- 保持与国网标准同步更新

## 相关模块
- ZcsGw103MmSrvPro: 国网103主主站服务端
- ZcsGw103MmCliPro: 国网103主主站客户端
