# ZcsFuJian103MsCliPro - 福建电网103主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsFuJian103MsCliPro
- **主要功能**: 实现福建电网IEC103协议主站客户端，适应沿海台风多发地区特性
- **设计目的**: 满足福建电网在台风、盐雾等特殊环境下的运行需求

## 核心组件

### 1. 福建电网特殊要求
- **台风防护**：台风监测与防护措施
- **盐雾腐蚀**：盐雾腐蚀监测与防护
- **防雷保护**：雷电监测与防护系统
- **海岛供电**：海岛微网特殊控制

### 2. 客户端架构
```cpp
class CFuJian103Client {
public:
    // 初始化福建客户端
    int Initialize(const FjConfig& config);
    
    // 台风模式控制
    int TyphoonModeControl(const TyphoonCmd& cmd);
    
    // 盐雾监测
    int SaltFogMonitoring(SaltFogData& data);
    
    // 防雷监测
    int LightningProtection(LightningData& data);
    
    // 海岛微网控制
    int IslandMicrogridControl(const MicrogridCmd& cmd);
    
    // 潮汐影响评估
    int TidalImpactAssessment(TidalData& data);
    
private:
    // 福建配置
    FjConfig m_config;
    
    // 台风防护控制器
    TyphoonProtectionController m_typhoonCtrl;
    
    // 盐雾监测器
    SaltFogMonitor m_saltMonitor;
};
```

### 3. 福建扩展数据类型
```cpp
namespace FjExtTypes {
    // 福建功能类型
    enum FjFunctionType {
        // 标准功能类型
        STANDARD_BASE = 0,
        
        // 福建扩展功能类型
        FJ_TYPHOON_PROTECT = 190,   // 台风防护
        FJ_SALT_FOG = 191,          // 盐雾监测
        FJ_LIGHTNING = 192,         // 防雷系统
        FJ_ISLAND_GRID = 193,       // 海岛电网
        FJ_TIDAL_IMPACT = 194,      // 潮汐影响
        FJ_COASTAL_ENV = 195        // 沿海环境
    };
    
    // 福建信息序号
    enum FjInfoNumber {
        // 台风监测信息
        FJ_WIND_SPEED = 270,        // 风速
        FJ_WIND_PRESSURE = 271,     // 风压
        FJ_TYPHOON_LEVEL = 272,     // 台风等级
        
        // 盐雾信息
        FJ_SALT_DENSITY = 273,      // 盐密度
        FJ_HUMIDITY = 274,          // 湿度
        FJ_CORROSION_RATE = 275,    // 腐蚀速率
        
        // 雷电信息
        FJ_LIGHTNING_COUNT = 276,   // 雷击次数
        FJ_LIGHTNING_CURRENT = 277, // 雷电流
        FJ_GROUND_RESISTANCE = 278  // 接地电阻
    };
}
```

## 台风防护系统

### 1. 台风监测与防护
```cpp
class CTyphoonProtection {
public:
    // 台风数据
    struct TyphoonData {
        int level;                  // 台风等级(1-17级)
        float windSpeed;            // 风速(m/s)
        float windPressure;         // 风压(Pa)
        float distance;             // 距离(km)
        int direction;              // 方向(度)
        float arrivalTime;          // 预计到达时间(小时)
        std::string warningLevel;   // 预警级别
    };
    
    // 台风防护策略
    int ExecuteTyphoonProtection(const TyphoonData& data) {
        // 1. 评估台风影响
        int impactLevel = AssessTyphoonImpact(data);
        
        // 2. 执行防护措施
        switch (impactLevel) {
            case 1:  // 蓝色预警
                return BlueWarningProtection();
            case 2:  // 黄色预警
                return YellowWarningProtection();
            case 3:  // 橙色预警
                return OrangeWarningProtection();
            case 4:  // 红色预警
                return RedWarningProtection();
        }
        
        return SUCCESS;
    }
    
    // 设备加固
    int EquipmentReinforcement() {
        // 加固室外设备
        ReinforceOutdoorEquipment();
        
        // 检查支撑结构
        CheckSupportStructures();
        
        // 清理排水系统
        ClearDrainageSystem();
        
        // 备用电源准备
        PrepareBackupPower();
        
        return SUCCESS;
    }
    
    // 线路防风措施
    int LineWindProtection(int lineId) {
        // 降低线路负荷
        ReduceLineLoad(lineId, 0.7);  // 降至70%
        
        // 调整保护定值
        AdjustProtectionSettings(lineId, TYPHOON_MODE);
        
        // 启动防舞动装置
        EnableAntiGallopingDevice(lineId);
        
        return SUCCESS;
    }
    
private:
    TyphoonStrategy m_strategy;
    std::vector<ProtectionDevice> m_protectionDevices;
};
```

### 2. 盐雾腐蚀监测
```cpp
class CSaltFogMonitor {
public:
    // 盐雾数据
    struct SaltFogData {
        float saltDensity;          // 等值盐密(mg/cm²)
        float ashDensity;           // 灰密(mg/cm²)
        float leakageCurrent;       // 泄漏电流(mA)
        float surfaceResistance;    // 表面电阻(MΩ)
        int pollutionLevel;         // 污秽等级(0-IV)
        bool cleaningNeeded;        // 需要清洗
    };
    
    // 监测盐雾污秽
    int MonitorSaltFogPollution(SaltFogData& data) {
        // 1. 测量盐密度
        MeasureSaltDensity(data);
        
        // 2. 测量泄漏电流
        MeasureLeakageCurrent(data);
        
        // 3. 评估污秽等级
        EvaluatePollutionLevel(data);
        
        // 4. 清洗决策
        if (data.pollutionLevel >= 3 || data.saltDensity > 0.1) {
            data.cleaningNeeded = true;
            ScheduleCleaning();
        }
        
        // 5. 防腐措施
        if (data.saltDensity > 0.05) {
            ApplyAntiCorrosionMeasures();
        }
        
        return SUCCESS;
    }
    
    // 绝缘子清洗控制
    int InsulatorCleaning(int lineId) {
        // 带电水冲洗
        if (IsLiveWashingAvailable(lineId)) {
            return LiveWaterWashing(lineId);
        }
        
        // 停电清洗
        return OfflineCleaning(lineId);
    }
    
    // 防腐涂层监测
    int CoatingMonitoring() {
        // 检查涂层完整性
        CheckCoatingIntegrity();
        
        // 评估腐蚀程度
        AssessCorrosionLevel();
        
        // 涂层修复建议
        GenerateMaintenanceAdvice();
        
        return SUCCESS;
    }
    
private:
    std::vector<SaltSensor> m_saltSensors;
    CleaningScheduler m_cleaningScheduler;
};
```

### 3. 防雷系统
```cpp
class CLightningProtection {
public:
    // 雷电数据
    struct LightningData {
        uint32_t strikeCount;       // 雷击次数
        float peakCurrent;          // 峰值电流(kA)
        float energy;               // 能量(kJ)
        float groundResistance;     // 接地电阻(Ω)
        int protectionLevel;        // 防护等级
        bool surgeDetected;         // 检测到浪涌
    };
    
    // 雷电监测与防护
    int LightningMonitoring(LightningData& data) {
        // 1. 雷电定位
        LocateLightningStrikes(data);
        
        // 2. 雷电流监测
        MonitorLightningCurrent(data);
        
        // 3. 接地电阻测量
        MeasureGroundResistance(data);
        
        // 4. 防雷评估
        AssessProtectionEffectiveness(data);
        
        // 5. 浪涌保护
        if (data.surgeDetected) {
            ActivateSurgeProtection();
        }
        
        return SUCCESS;
    }
    
    // 防雷装置管理
    int ManageLightningDevices() {
        // 检查避雷器状态
        CheckArresterStatus();
        
        // 检查接地系统
        CheckGroundingSystem();
        
        // 检查等电位连接
        CheckEquipotentialBonding();
        
        return SUCCESS;
    }
    
private:
    LightningDetectionSystem m_detectionSystem;
    std::vector<SurgeArrester> m_arresters;
};
```

## 海岛微网控制

### 1. 海岛微网管理
```cpp
class CIslandMicrogrid {
public:
    // 微网运行模式
    enum MicrogridMode {
        GRID_CONNECTED,     // 并网运行
        ISLAND_MODE,        // 孤岛运行
        TRANSITION_MODE     // 切换模式
    };
    
    // 微网控制
    int MicrogridControl(const MicrogridCmd& cmd) {
        switch (cmd.mode) {
            case GRID_CONNECTED:
                return GridConnectedOperation();
            case ISLAND_MODE:
                return IslandOperation();
            case TRANSITION_MODE:
                return ModeTransition(cmd);
        }
        return SUCCESS;
    }
    
    // 孤岛运行控制
    int IslandOperation() {
        // 1. 频率控制
        MaintainFrequency(50.0);
        
        // 2. 电压控制
        MaintainVoltage(10.5);
        
        // 3. 负荷平衡
        BalanceLoadGeneration();
        
        // 4. 储能调度
        DispatchEnergyStorage();
        
        // 5. 可再生能源管理
        ManageRenewableEnergy();
        
        return SUCCESS;
    }
    
    // 新能源接入
    int RenewableIntegration() {
        // 光伏发电控制
        ControlSolarGeneration();
        
        // 风力发电控制
        ControlWindGeneration();
        
        // 潮汐发电控制
        ControlTidalGeneration();
        
        // 储能协调
        CoordinateEnergyStorage();
        
        return SUCCESS;
    }
    
private:
    MicrogridController m_controller;
    EnergyManagementSystem m_ems;
};
```

### 2. 潮汐影响管理
```cpp
class CTidalImpactManager {
public:
    // 潮汐数据
    struct TidalData {
        float currentLevel;         // 当前潮位(m)
        float highTide;             // 高潮位(m)
        float lowTide;              // 低潮位(m)
        float tidalRange;           // 潮差(m)
        std::string tidalPhase;     // 潮汐相位
        bool floodRisk;            // 洪水风险
    };
    
    // 潮汐影响评估
    int AssessTidalImpact(TidalData& data) {
        // 1. 获取潮汐数据
        GetTidalInformation(data);
        
        // 2. 评估设备影响
        AssessEquipmentImpact(data);
        
        // 3. 评估线路影响
        AssessLineImpact(data);
        
        // 4. 洪水风险评估
        if (data.currentLevel > data.highTide * 0.9) {
            data.floodRisk = true;
            InitiateFloodProtection();
        }
        
        return SUCCESS;
    }
    
    // 海缆监测
    int SubmarineCableMonitoring() {
        // 监测海缆状态
        MonitorCableStatus();
        
        // 检测锚害风险
        DetectAnchorDamageRisk();
        
        // 海水腐蚀监测
        MonitorSeawaterCorrosion();
        
        return SUCCESS;
    }
    
private:
    TidalPredictionSystem m_tidalSystem;
    SubmarineCableMonitor m_cableMonitor;
};
```

## 配置参数

### 福建103配置
```ini
[FuJian103_Config]
# 基本通信参数
DeviceAddress = 1           # 装置地址
LinkAddress = 1             # 链路地址
BaudRate = 9600            # 波特率

# 福建扩展参数
FjStationCode = 3501        # 福建站点代码
FjAreaCode = 0591           # 福建地区代码
EnableFjExtension = true    # 启用福建扩展
FjProtocolVersion = 2.3     # 福建协议版本

# 台风防护参数
TyphoonWarningEnabled = true    # 台风预警
WindSpeedLimit = 25             # 风速限值(m/s)
WindPressureLimit = 1500        # 风压限值(Pa)
ReinforcementLevel = 3          # 加固等级

# 盐雾监测参数
SaltDensityLimit = 0.1          # 盐密度限值(mg/cm²)
CleaningThreshold = 0.08        # 清洗阈值(mg/cm²)
MonitoringPeriod = 86400        # 监测周期(秒)
AutoCleaningEnabled = true      # 自动清洗

# 防雷参数
LightningDetectionEnabled = true # 雷电检测
GroundResistanceLimit = 10      # 接地电阻限值(Ω)
SurgeProtectionLevel = 2        # 浪涌保护等级
ArresterCheckPeriod = 2592000   # 避雷器检查周期(秒)

# 海岛微网参数
MicrogridEnabled = true         # 微网功能
IslandDetectionTime = 100       # 孤岛检测时间(ms)
FrequencyRange = [49.5, 50.5]   # 频率范围(Hz)
VoltageRange = [0.95, 1.05]     # 电压范围(pu)

# 潮汐监测参数
TidalMonitoringEnabled = true   # 潮汐监测
HighTideWarning = 5.0           # 高潮位预警(m)
FloodProtectionLevel = 6.0      # 防洪水位(m)
SubmarineCableCheck = true      # 海缆检查
```

## 错误处理

### 福建特定错误码
```cpp
enum FjErrorCode {
    FJ_ERR_TYPHOON = 0x5001,        // 台风防护错误
    FJ_ERR_SALT_FOG = 0x5002,       // 盐雾监测错误
    FJ_ERR_LIGHTNING = 0x5003,      // 防雷系统错误
    FJ_ERR_MICROGRID = 0x5004,      // 微网控制错误
    FJ_ERR_TIDAL = 0x5005,          // 潮汐影响错误
    FJ_ERR_SUBMARINE = 0x5006,      // 海缆故障
    FJ_ERR_ISLAND_MODE = 0x5007     // 孤岛运行错误
};
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建福建客户端
auto fjClient = std::make_unique<CFuJian103Client>();

// 2. 加载配置
FjConfig config;
config.LoadFromFile("fujian103_config.ini");
fjClient->Initialize(config);

// 3. 连接设备
fjClient->Connect("192.168.1.100", 2404);

// 4. 台风防护
TyphoonData typhoonData;
fjClient->GetTyphoonInfo(typhoonData);
if (typhoonData.level >= 12) {  // 12级以上台风
    TyphoonCmd cmd;
    cmd.action = ENABLE_PROTECTION;
    cmd.level = typhoonData.level;
    fjClient->TyphoonModeControl(cmd);
}

// 5. 盐雾监测
SaltFogData saltData;
fjClient->SaltFogMonitoring(saltData);
if (saltData.cleaningNeeded) {
    std::cout << "Insulator cleaning required! Salt density: " 
              << saltData.saltDensity << "mg/cm²" << std::endl;
    fjClient->ScheduleInsulatorCleaning();
}

// 6. 防雷监测
LightningData lightningData;
fjClient->LightningProtection(lightningData);
std::cout << "Lightning strikes: " << lightningData.strikeCount 
          << ", Ground resistance: " << lightningData.groundResistance 
          << "Ω" << std::endl;

// 7. 海岛微网控制
if (fjClient->DetectIslandCondition()) {
    MicrogridCmd mgCmd;
    mgCmd.mode = ISLAND_MODE;
    mgCmd.action = START_ISLAND_OPERATION;
    fjClient->IslandMicrogridControl(mgCmd);
}

// 8. 潮汐影响评估
TidalData tidalData;
fjClient->TidalImpactAssessment(tidalData);
if (tidalData.floodRisk) {
    std::cout << "Flood risk detected! Current level: " 
              << tidalData.currentLevel << "m" << std::endl;
}
```

## 注意事项
- 台风季节加强设备巡检
- 定期清洗盐雾污秽设备
- 雷雨季节检查防雷设施
- 海岛微网注意孤岛检测
- 关注潮汐对海缆影响

## 相关模块
- ZcsHuBei103MsCliPro: 湖北103主站客户端
- ZcsLn103MsCliPro: 辽宁103主站客户端
- ZcsHlj103MsCliPro: 黑龙江103主站客户端
