# ZcsServer/pro - 协议实现模块详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro
- **主要功能**: 实现各种通信协议的客户端和服务端
- **设计目的**: 提供多协议支持，实现与不同厂家、不同标准的设备通信

## 目录结构

```
ZcsServer/pro/
├── common/                      # 协议公共组件
├── Zcs104VlanSrv/              # IEC 104 VLAN服务端
├── ZcsCmsCliPro/               # CMS客户端协议
├── ZcsD5kMsgItf/               # D5K消息接口
├── ZcsFoshan104SrvPro/         # 佛山104服务端协议
├── ZcsFuJian103MsCliPro/       # 福建103主站客户端
├── ZcsGspCliPro/               # GSP客户端协议
├── ZcsGspTransHdl/             # GSP传输处理
├── ZcsGw103MmCliPro/           # 国网103主从客户端
├── ZcsGw103MmSrvPro/           # 国网103主从服务端
├── ZcsGw103MsCliPro/           # 国网103主站客户端
├── ZcsHlj103MsCliPro/          # 黑龙江103主站客户端
├── ZcsHuBei103MsCliPro/        # 湖北103主站客户端
├── ZcsLn103MsCliPro/           # 辽宁103主站客户端
├── ZcsMmCliProFlow/            # 主从客户端流程
├── ZcsNw103MmCliPro/           # 南网103主从客户端
├── ZcsNw103MmSrvPro/           # 南网103主从服务端
├── ZcsNw103MsCliPro/           # 南网103主站客户端
├── ZcsNw103v3MsCliPro/         # 南网103v3主站客户端
├── ZcsPro103SrvFlow/           # 103服务端流程
├── ZcsPro61850Cli/             # IEC 61850客户端
├── ZcsPro61850CliNW/           # IEC 61850南网客户端
├── ZcsPro61850CliTC/           # IEC 61850天诚客户端
├── ZcsProApciHdl/              # APCI处理
├── ZcsProMainFlow/             # 主流程
├── ZcsProSttpClient/           # STTP客户端
├── ZcsSbItfCli/                # 设备接口客户端
├── ZcsSttpSrvPro/              # STTP服务端
└── ZcsWebSrvPro/               # Web服务端
```

## 协议分类

### 1. IEC 60870系列协议
- **IEC 60870-5-103**: 继电保护信息接口
- **IEC 60870-5-104**: 网络访问传输协议
- **IEC 60870-5-101**: 基本远动任务配套标准

### 2. IEC 61850协议
- 面向变电站自动化的通信网络和系统
- 支持MMS、GOOSE、SMV等服务

### 3. 专用协议
- **STTP**: 许继专用传输协议
- **D5K**: D5000平台消息接口
- **GSP**: 通用服务协议

### 4. 地区定制协议
- 国网标准协议
- 南网标准协议
- 各省网定制协议

## pro/common - 协议公共组件分析

### 核心文件说明

#### 1. 协议定义文件
- **Zx103ProtocolDef.h**: IEC 103协议定义
- **iec61850define.h**: IEC 61850协议定义
- **ZxAPCIWrapper.h**: APCI层封装

#### 2. 消息处理
- **Zx103MsgAttach.h**: 103消息附件
- **Zx103MsgAttachFactory.h**: 103消息工厂
- **Zx104MsgAttachFactory.h**: 104消息工厂
- **ZxSttpServerMsgHandler.h**: STTP服务端消息处理

#### 3. ASDU处理
- **ZxSrvCASDU103.h**: 103 ASDU处理
- **ZxSrvCASDU101.h**: 101 ASDU处理
- **ZxSrvCASDU15.h**: ASDU类型15处理
- **ZxSrvMASDU16.h**: ASDU类型16处理
- **Zx104FoShanASDU1.h**: 佛山104 ASDU处理

#### 4. 操作封装
- **ZxProOperation.h**: 协议操作基类
- **ZxProResultOperation.h**: 结果操作处理
- **ZxSubStationPublisher.h**: 子站发布者

#### 5. 数据库功能
- **Zx61850DBFunction.h**: 61850数据库功能

#### 6. 流程控制
- **ZxProSrv103FlowWrapper.h**: 103服务流程封装
- **ZxMsgBusLinkControl.h**: 消息总线链路控制

## 主要协议模块详细分析

### 1. Zcs104VlanSrv - IEC 104 VLAN服务端

#### 功能特点
- 支持VLAN网络隔离
- 多连接管理
- APCI/ASDU层处理
- 支持总召、时钟同步、测试等功能

#### 核心组件
```cpp
// APCI层处理
class CAPCIHandler {
    // U帧、S帧、I帧处理
    int HandleUFrame(APCI_U_FRAME* frame);
    int HandleSFrame(APCI_S_FRAME* frame);
    int HandleIFrame(APCI_I_FRAME* frame);
};

// ASDU层处理
class CASDUHandler {
    // 各种ASDU类型处理
    int HandleType1(ASDU_TYPE_1* asdu);   // 单点信息
    int HandleType30(ASDU_TYPE_30* asdu); // 带时标单点信息
    int HandleType100(ASDU_TYPE_100* asdu); // 总召命令
};
```

### 2. ZcsPro61850Cli - IEC 61850客户端

#### 功能特点
- MMS协议栈实现
- SCL文件解析
- 数据模型管理
- 报告、控制、日志服务

#### 核心组件
```cpp
// MMS客户端
class CMmsClient {
    // 连接管理
    int Connect(const char* ip, int port);
    int Disconnect();
    
    // 服务调用
    int Read(ObjectReference& ref, MmsValue& value);
    int Write(ObjectReference& ref, MmsValue& value);
    int GetNameList(ObjectClass objClass);
};

// 数据模型
class CIedModel {
    // IED、LD、LN、DO、DA层次结构
    IED* CreateIED(const char* name);
    LogicalDevice* CreateLD(IED* ied, const char* name);
    LogicalNode* CreateLN(LogicalDevice* ld, const char* name);
};
```

### 3. ZcsGw103MsCliPro - 国网103主站客户端

#### 功能特点
- 符合国网103规约标准
- 支持主站功能
- 故障录波文件传输
- 定值管理

#### 核心组件
```cpp
// 103客户端
class C103Client {
    // 链路管理
    int EstablishLink();
    int ResetLink();
    
    // 数据采集
    int GeneralInterrogation(); // 总召
    int ClockSync();            // 时钟同步
    
    // 文件传输
    int RequestFile(const char* fileName);
    int ReceiveFile(FileData& data);
};
```

### 4. ZcsSttpSrvPro - STTP服务端

#### 功能特点
- 许继专用协议
- 高效数据传输
- 支持长短连接
- 数据压缩

#### 核心组件
```cpp
// STTP服务端
class CSttpServer {
    // 连接管理
    int Listen(int port);
    int Accept(ClientInfo& client);
    
    // 消息处理
    int HandleLoginMsg(STTPMSG* msg);
    int HandleDataMsg(STTPMSG* msg);
    int HandleHeartbeatMsg(STTPMSG* msg);
    
    // 数据发送
    int SendResponse(ClientInfo& client, STTPMSG* msg);
};
```

## 协议转换机制

### 1. 协议适配层
```cpp
class CProtocolAdapter {
    // 协议转换接口
    virtual int ConvertToInternal(void* protocolData, InternalData& internal) = 0;
    virtual int ConvertFromInternal(InternalData& internal, void* protocolData) = 0;
};
```

### 2. 统一数据模型
```cpp
struct UnifiedDataModel {
    DataType type;          // 数据类型
    TimeStamp timestamp;    // 时标
    Quality quality;        // 品质
    Value value;           // 值
    Identifier id;         // 标识
};
```

## 协议扩展机制

### 1. 动态加载
- 协议模块以动态库形式存在
- 运行时加载和卸载
- 统一的接口规范

### 2. 配置驱动
- XML/INI配置文件
- 协议参数可配置
- 映射关系可定制

### 3. 插件架构
```cpp
// 协议插件接口
class IProtocolPlugin {
public:
    virtual int Init(const Config& config) = 0;
    virtual int Start() = 0;
    virtual int Stop() = 0;
    virtual int ProcessData(const Data& data) = 0;
};
```

## 性能优化

### 1. 连接池
- 预建立连接
- 连接复用
- 负载均衡

### 2. 缓冲区管理
- 环形缓冲区
- 零拷贝技术
- 内存池

### 3. 并发处理
- 多线程/多进程
- 异步IO
- 事件驱动

## 安全机制

### 1. 认证授权
- 用户认证
- 权限控制
- 会话管理

### 2. 数据加密
- TLS/SSL支持
- 国密算法
- 数据完整性校验

### 3. 防护措施
- 防重放攻击
- 防篡改
- 审计日志

## 故障处理

### 1. 链路监测
- 心跳机制
- 链路状态检测
- 自动重连

### 2. 数据校验
- CRC校验
- 序号检查
- 超时处理

### 3. 异常恢复
- 断点续传
- 数据重发
- 状态同步

## 配置示例

### IEC 104配置
```ini
[IEC104]
LocalIP=*************
LocalPort=2404
MaxConnections=10
T0=30  ; 连接建立超时
T1=15  ; 发送或测试APDU超时
T2=10  ; 无数据报文时确认超时
T3=20  ; 测试帧发送间隔
K=12   ; 发送序号与接收序号最大差值
W=8    ; 接收序号确认阈值
```

### IEC 61850配置
```xml
<IED name="PROT" manufacturer="XJ">
    <AccessPoint name="S1">
        <Server>
            <LDevice inst="LD0">
                <LN lnClass="PDIS" lnType="PDIS1" inst="1">
                    <DO name="Str" type="ACD"/>
                    <DO name="Op" type="ACT"/>
                </LN>
            </LDevice>
        </Server>
    </AccessPoint>
</IED>
```

## 使用注意事项

1. **协议版本兼容**: 注意不同版本协议的差异
2. **参数配置**: 根据实际网络环境调整超时参数
3. **性能调优**: 根据数据量调整缓冲区大小
4. **安全配置**: 生产环境必须启用安全机制
5. **日志级别**: 生产环境避免过度日志影响性能
