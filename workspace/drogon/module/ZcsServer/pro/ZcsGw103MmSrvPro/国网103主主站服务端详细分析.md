# ZcsGw103MmSrvPro - 国网103主主站服务端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsGw103MmSrvPro
- **主要功能**: 实现国家电网IEC103协议主站到主站服务端，提供数据服务
- **设计目的**: 作为国网系统中的主站服务端，向其他主站提供标准化数据服务

## 核心组件

### 1. 国网服务端架构
```cpp
class CGw103MmServer {
public:
    // 启动国网服务
    int StartGwService(const GwServerConfig& config);
    
    // 停止服务
    int StopService();
    
    // 处理国网主站请求
    int ProcessGwMasterRequest(const GwRequest& request);
    
    // 数据发布服务
    int PublishGwData(const Gw103Data& data);
    
    // 权限管理
    int ManageGwPermissions(const PermissionRequest& req);
    
private:
    // 国网服务配置
    GwServerConfig m_config;
    
    // 客户端管理器
    GwClientManager m_clientMgr;
    
    // 数据服务提供者
    GwDataProvider m_dataProvider;
};
```

### 2. 国网数据模型
```cpp
namespace GwDataModel {
    // 国网数据分类
    enum DataCategory {
        GW_REALTIME = 1,        // 实时数据
        GW_SOE = 2,             // 事件顺序记录
        GW_FAULT = 3,           // 故障数据
        GW_WAVE = 4,            // 录波数据
        GW_STATISTICS = 5,      // 统计数据
        GW_PLAN = 6            // 计划数据
    };
    
    // 国网数据结构
    struct GwDataItem {
        uint32_t dataId;        // 数据标识
        DataCategory category;  // 数据类别
        uint32_t stationId;     // 厂站标识
        uint32_t deviceId;      // 设备标识
        std::vector<uint8_t> value; // 数据值
        uint64_t timestamp;     // 时标
        uint8_t quality;        // 品质
        uint8_t priority;       // 优先级
    };
}
```

### 3. 国网认证机制
```cpp
class CGwAuthentication {
public:
    // 国网认证方式
    enum AuthMethod {
        GW_CERTIFICATE = 1,     // 证书认证
        GW_TOKEN = 2,          // 令牌认证
        GW_DYNAMIC = 3         // 动态口令
    };
    
    // 执行国网认证
    int AuthenticateGwClient(const GwAuthRequest& request) {
        switch (request.method) {
            case GW_CERTIFICATE:
                return CertificateAuth(request.certificate);
            case GW_TOKEN:
                return TokenAuth(request.token);
            case GW_DYNAMIC:
                return DynamicPasswordAuth(request.dynamicPwd);
        }
        return ERR_AUTH_FAILED;
    }
    
    // 权限验证
    int VerifyGwPermission(const std::string& clientId, 
                          const GwOperation& operation) {
        // 获取客户端权限
        auto permissions = GetClientPermissions(clientId);
        
        // 验证操作权限
        return CheckOperationPermission(permissions, operation);
    }
    
private:
    // 证书管理
    CertificateManager m_certMgr;
    
    // 权限映射表
    std::map<std::string, GwPermissions> m_permissionMap;
};
```

## 数据服务实现

### 1. 实时数据服务
```cpp
class CGwRealtimeService {
public:
    // 提供实时数据
    int ProvideRealtimeData(const DataRequest& request, 
                           DataResponse& response) {
        // 1. 验证请求合法性
        if (!ValidateRequest(request)) {
            return ERR_INVALID_REQUEST;
        }
        
        // 2. 检查数据权限
        if (!CheckDataPermission(request.clientId, request.dataIds)) {
            return ERR_NO_PERMISSION;
        }
        
        // 3. 获取实时数据
        std::vector<GwDataItem> dataItems;
        GetRealtimeData(request.dataIds, dataItems);
        
        // 4. 数据过滤和转换
        FilterAndTransform(dataItems, request.filter);
        
        // 5. 打包响应
        PackageResponse(dataItems, response);
        
        return SUCCESS;
    }
    
    // 数据订阅服务
    int SubscribeRealtimeData(const SubscribeRequest& request) {
        // 添加订阅关系
        AddSubscription(request.clientId, request.dataPattern);
        
        // 启动推送
        StartDataPush(request.clientId);
        
        return SUCCESS;
    }
    
private:
    // 实时数据缓存
    RealtimeDataCache m_dataCache;
    
    // 订阅管理
    SubscriptionManager m_subMgr;
};
```

### 2. SOE事件服务
```cpp
class CGwSOEService {
public:
    // SOE事件结构
    struct SOEEvent {
        uint32_t eventId;       // 事件ID
        uint32_t deviceId;      // 设备ID
        std::string eventType;  // 事件类型
        std::string description;// 描述
        uint64_t timestamp;     // 时标(微秒精度)
        uint8_t eventLevel;     // 事件级别
    };
    
    // 查询SOE事件
    int QuerySOEEvents(const SOEQuery& query, 
                      std::vector<SOEEvent>& events) {
        // 1. 时间范围过滤
        FilterByTimeRange(query.startTime, query.endTime);
        
        // 2. 设备过滤
        FilterByDevice(query.deviceIds);
        
        // 3. 事件类型过滤
        FilterByEventType(query.eventTypes);
        
        // 4. 查询数据库
        QueryFromDatabase(events);
        
        // 5. 排序
        SortByTimestamp(events);
        
        return SUCCESS;
    }
    
    // 实时SOE推送
    int PushSOEEvent(const SOEEvent& event) {
        // 获取SOE订阅者
        auto subscribers = GetSOESubscribers();
        
        // 推送给所有订阅者
        for (const auto& subscriber : subscribers) {
            SendSOEToClient(subscriber, event);
        }
        
        // 存储到数据库
        StoreSOEToDatabase(event);
        
        return SUCCESS;
    }
    
private:
    SOEDatabase m_soeDb;
    std::vector<std::string> m_soeSubscribers;
};
```

### 3. 故障录波服务
```cpp
class CGwFaultRecorderService {
public:
    // 故障录波信息
    struct FaultRecord {
        uint32_t recordId;      // 录波ID
        uint32_t deviceId;      // 装置ID
        std::string faultType;  // 故障类型
        uint64_t faultTime;     // 故障时间
        float faultCurrent;     // 故障电流
        float faultVoltage;     // 故障电压
        std::vector<uint8_t> waveformData; // 波形数据
    };
    
    // 查询故障录波
    int QueryFaultRecords(const FaultQuery& query,
                         std::vector<FaultRecord>& records) {
        // 查询录波目录
        QueryRecordDirectory(query, records);
        
        return SUCCESS;
    }
    
    // 传输录波文件
    int TransferFaultRecord(int recordId, 
                           std::vector<uint8_t>& data) {
        // 1. 查找录波文件
        std::string filePath = GetRecordFilePath(recordId);
        
        // 2. 读取文件数据
        ReadRecordFile(filePath, data);
        
        // 3. 转换为COMTRADE格式
        ConvertToComtrade(data);
        
        return SUCCESS;
    }
    
    // 故障分析服务
    int AnalyzeFault(int recordId, FaultAnalysis& analysis) {
        // 获取录波数据
        FaultRecord record;
        GetFaultRecord(recordId, record);
        
        // 执行故障分析
        PerformFaultAnalysis(record, analysis);
        
        return SUCCESS;
    }
    
private:
    FaultRecorderDatabase m_recorderDb;
    FaultAnalyzer m_analyzer;
};
```

## 国网标准接口

### 1. 数据交换接口
```cpp
class CGwDataExchange {
public:
    // 国网数据交换格式
    struct GwExchangeFormat {
        std::string version;    // 格式版本
        std::string encoding;   // 编码方式
        std::string compression;// 压缩方式
        bool encrypted;        // 是否加密
    };
    
    // 导出数据
    int ExportGwData(const ExportRequest& request,
                    std::vector<uint8_t>& exportData) {
        // 1. 查询数据
        std::vector<GwDataItem> dataItems;
        QueryData(request.query, dataItems);
        
        // 2. 格式转换
        ConvertToGwFormat(dataItems, request.format);
        
        // 3. 数据压缩
        if (request.compress) {
            CompressData(exportData);
        }
        
        // 4. 数据加密
        if (request.encrypt) {
            EncryptData(exportData);
        }
        
        return SUCCESS;
    }
    
    // 导入数据
    int ImportGwData(const std::vector<uint8_t>& importData) {
        // 1. 数据解密
        std::vector<uint8_t> decrypted;
        DecryptData(importData, decrypted);
        
        // 2. 数据解压
        std::vector<uint8_t> decompressed;
        DecompressData(decrypted, decompressed);
        
        // 3. 格式解析
        std::vector<GwDataItem> dataItems;
        ParseGwFormat(decompressed, dataItems);
        
        // 4. 数据验证
        ValidateImportData(dataItems);
        
        // 5. 存储数据
        StoreImportedData(dataItems);
        
        return SUCCESS;
    }
    
private:
    DataConverter m_converter;
    DataCompressor m_compressor;
    DataEncryptor m_encryptor;
};
```

### 2. 统计分析服务
```cpp
class CGwStatisticsService {
public:
    // 统计指标
    struct Statistics {
        float average;          // 平均值
        float max;             // 最大值
        float min;             // 最小值
        float variance;        // 方差
        float standardDev;     // 标准差
        uint32_t sampleCount;  // 样本数
    };
    
    // 计算统计数据
    int CalculateStatistics(const StatQuery& query,
                           Statistics& stats) {
        // 获取历史数据
        std::vector<float> dataPoints;
        GetHistoricalData(query, dataPoints);
        
        // 计算统计指标
        CalculateMetrics(dataPoints, stats);
        
        return SUCCESS;
    }
    
    // 生成统计报表
    int GenerateReport(const ReportRequest& request,
                      ReportData& report) {
        // 收集统计数据
        CollectStatisticsData(request, report);
        
        // 生成图表
        GenerateCharts(report);
        
        // 格式化报表
        FormatReport(report, request.format);
        
        return SUCCESS;
    }
    
private:
    StatisticsCalculator m_calculator;
    ReportGenerator m_reportGen;
};
```

## 配置参数

### 国网服务端配置
```ini
[Gw103MmServer_Config]
# 服务配置
ServerPort = 2404              # 服务端口
MaxClients = 200               # 最大客户端数
ThreadPoolSize = 32            # 线程池大小

# 国网认证配置
AuthMethod = CERTIFICATE       # 认证方式
CertPath = /etc/gw103/certs/   # 证书路径
TokenExpiry = 86400            # 令牌过期时间(秒)

# 数据服务配置
RealtimeCacheSize = 100000     # 实时数据缓存大小
SOEBufferSize = 50000          # SOE缓冲区大小
FaultRecordPath = /data/fault/ # 故障录波路径

# 国网标准配置
GwProtocolVersion = 2.0        # 国网协议版本
DataExchangeFormat = JSON      # 数据交换格式
CompressionEnabled = true      # 启用压缩
EncryptionEnabled = true       # 启用加密

# 性能配置
MaxDataRate = 10000            # 最大数据速率(条/秒)
BatchSize = 1000              # 批处理大小
CacheExpiry = 3600            # 缓存过期时间(秒)

# 统计配置
StatisticsPeriod = 300         # 统计周期(秒)
ReportGenerationTime = 0200    # 报表生成时间
HistoricalDataDays = 30        # 历史数据保留天数
```

## 高可用与负载均衡

### 1. 集群管理
```cpp
class CGwClusterManager {
public:
    // 集群节点状态
    enum NodeState {
        MASTER,         // 主节点
        SLAVE,          // 从节点
        CANDIDATE       // 候选节点
    };
    
    // 集群选举
    int ElectMaster() {
        // Raft算法选举
        return RaftElection();
    }
    
    // 数据同步
    int SyncClusterData() {
        // 主从同步
        if (m_nodeState == MASTER) {
            BroadcastToSlaves();
        } else {
            SyncFromMaster();
        }
        return SUCCESS;
    }
    
    // 故障转移
    int Failover() {
        // 检测主节点故障
        if (IsMasterFailed()) {
            // 触发选举
            ElectMaster();
            
            // 接管服务
            TakeoverService();
        }
        return SUCCESS;
    }
    
private:
    NodeState m_nodeState;
    std::vector<ClusterNode> m_clusterNodes;
};
```

## 使用示例

### 服务端启动流程
```cpp
// 1. 创建国网服务端
auto gwServer = std::make_unique<CGw103MmServer>();

// 2. 加载配置
GwServerConfig config;
config.LoadFromFile("gw103mm_server.ini");

// 3. 初始化服务
gwServer->Initialize(config);

// 4. 注册服务处理器
gwServer->RegisterHandler(GW_REALTIME, HandleRealtimeRequest);
gwServer->RegisterHandler(GW_SOE, HandleSOERequest);
gwServer->RegisterHandler(GW_FAULT, HandleFaultRequest);

// 5. 设置认证回调
gwServer->SetAuthCallback([](const GwAuthRequest& req) {
    return AuthenticateGwClient(req);
});

// 6. 启动服务
gwServer->StartGwService(config);

// 7. 发布数据示例
Gw103Data data;
data.category = GW_REALTIME;
data.stationId = 1001;
data.deviceId = 2001;
data.value = 220.5;
gwServer->PublishGwData(data);

// 8. 监控服务状态
auto status = gwServer->GetServerStatus();
std::cout << "Connected clients: " << status.clientCount << std::endl;
std::cout << "Data rate: " << status.dataRate << " items/sec" << std::endl;
```

## 注意事项
- 严格遵守国网数据标准
- 确保认证机制安全可靠
- 注意数据权限管理
- 定期备份重要数据
- 监控服务性能指标

## 相关模块
- ZcsGw103MmCliPro: 国网103主主站客户端
- ZcsGw103MsCliPro: 国网103主站客户端
- ZcsNw103MmSrvPro: 南网103主主站服务端
