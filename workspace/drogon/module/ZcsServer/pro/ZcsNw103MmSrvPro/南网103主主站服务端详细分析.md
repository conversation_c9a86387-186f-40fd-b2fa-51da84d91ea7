# ZcsNw103MmSrvPro - 南网103主主站服务端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsNw103MmSrvPro
- **主要功能**: 实现南网IEC103协议的主站到主站服务端，接收其他主站的连接请求
- **设计目的**: 作为数据提供方，向其他主站提供103协议数据服务

## 核心组件

### 1. 服务端架构
```cpp
class CNw103MmServer {
public:
    // 启动服务
    int StartService(int port);
    
    // 停止服务
    int StopService();
    
    // 接受客户端连接
    int AcceptClient(ClientConnection& client);
    
    // 处理客户端请求
    int ProcessClientRequest(const ClientRequest& request);
    
    // 推送数据给客户端
    int PushDataToClient(const std::string& clientId, 
                        const Nw103Data& data);
    
private:
    // 监听套接字
    ServerSocket m_listenSocket;
    
    // 客户端管理器
    ClientManager m_clientMgr;
    
    // 请求处理器
    RequestProcessor m_processor;
};
```

### 2. 客户端管理
```cpp
class CMmClientManager {
public:
    // 客户端信息
    struct ClientInfo {
        std::string clientId;       // 客户端标识
        std::string stationName;    // 主站名称
        std::string ipAddress;      // IP地址
        int port;                   // 端口号
        PermissionLevel permission; // 权限级别
        ConnectionState state;      // 连接状态
        uint64_t connectTime;       // 连接时间
        uint64_t lastActiveTime;    // 最后活动时间
    };
    
    // 注册客户端
    int RegisterClient(const ClientInfo& info);
    
    // 注销客户端
    int UnregisterClient(const std::string& clientId);
    
    // 获取客户端信息
    ClientInfo* GetClientInfo(const std::string& clientId);
    
    // 广播数据
    int BroadcastData(const Nw103Data& data);
    
private:
    std::map<std::string, ClientInfo> m_clients;
    std::mutex m_clientMutex;
};
```

### 3. 请求处理器
```cpp
class CMmRequestProcessor {
public:
    // 处理数据订阅请求
    int ProcessSubscribeRequest(const SubscribeRequest& req,
                               SubscribeResponse& resp);
    
    // 处理数据查询请求
    int ProcessQueryRequest(const QueryRequest& req,
                           QueryResponse& resp);
    
    // 处理控制请求
    int ProcessControlRequest(const ControlRequest& req,
                             ControlResponse& resp);
    
    // 处理配置同步请求
    int ProcessConfigSyncRequest(const ConfigSyncRequest& req,
                                ConfigSyncResponse& resp);
    
private:
    // 请求验证
    bool ValidateRequest(const Request& req);
    
    // 权限检查
    bool CheckPermission(const std::string& clientId,
                        const Request& req);
};
```

## 数据服务实现

### 1. 数据发布服务
```cpp
class CMmDataPublisher {
public:
    // 发布实时数据
    int PublishRealtimeData(const RealtimeData& data) {
        // 1. 获取订阅列表
        auto subscribers = GetSubscribers(data.pointId);
        
        // 2. 遍历订阅者
        for (const auto& subscriber : subscribers) {
            // 3. 检查权限
            if (!HasPermission(subscriber, data)) {
                continue;
            }
            
            // 4. 应用过滤
            if (!PassFilter(subscriber.filter, data)) {
                continue;
            }
            
            // 5. 推送数据
            PushToSubscriber(subscriber, data);
        }
        
        return SUCCESS;
    }
    
    // 管理订阅
    int ManageSubscription(const std::string& clientId,
                          const Subscription& sub) {
        if (sub.action == SUBSCRIBE) {
            AddSubscription(clientId, sub);
        } else {
            RemoveSubscription(clientId, sub);
        }
        return SUCCESS;
    }
    
private:
    // 订阅映射表
    std::map<uint32_t, std::vector<Subscriber>> m_subscriptions;
};
```

### 2. 历史数据服务
```cpp
class CMmHistoricalService {
public:
    // 查询历史数据
    int QueryHistoricalData(const HistoricalQuery& query,
                           HistoricalResult& result) {
        // 1. 验证查询参数
        if (!ValidateQuery(query)) {
            return ERR_INVALID_QUERY;
        }
        
        // 2. 从数据库查询
        std::vector<HistoricalRecord> records;
        QueryFromDatabase(query, records);
        
        // 3. 数据处理
        ProcessHistoricalData(records, result);
        
        return SUCCESS;
    }
    
    // 导出历史数据
    int ExportHistoricalData(const ExportRequest& request,
                            std::vector<uint8_t>& data) {
        // 查询数据
        HistoricalResult result;
        QueryHistoricalData(request.query, result);
        
        // 格式转换
        ConvertToExportFormat(result, request.format, data);
        
        return SUCCESS;
    }
    
private:
    DatabaseConnection m_dbConn;
};
```

### 3. 事件服务
```cpp
class CMmEventService {
public:
    // 事件通知
    int NotifyEvent(const EventData& event) {
        // 1. 记录事件
        LogEvent(event);
        
        // 2. 获取事件订阅者
        auto subscribers = GetEventSubscribers(event.type);
        
        // 3. 推送事件
        for (const auto& subscriber : subscribers) {
            SendEventNotification(subscriber, event);
        }
        
        return SUCCESS;
    }
    
    // 查询事件
    int QueryEvents(const EventQuery& query,
                   std::vector<EventData>& events) {
        return QueryEventDatabase(query, events);
    }
    
private:
    EventDatabase m_eventDb;
    std::map<EventType, std::vector<std::string>> m_eventSubscribers;
};
```

## 安全机制

### 1. 认证服务
```cpp
class CMmAuthenticationService {
public:
    // 客户端认证
    int AuthenticateClient(const AuthRequest& request,
                          AuthResponse& response) {
        // 1. 验证证书
        if (m_config.useCertificate) {
            if (!VerifyCertificate(request.certificate)) {
                response.result = AUTH_FAILED;
                return ERR_AUTH_FAILED;
            }
        }
        
        // 2. 验证用户名密码
        if (!VerifyCredentials(request.username, request.password)) {
            response.result = AUTH_FAILED;
            return ERR_AUTH_FAILED;
        }
        
        // 3. 生成会话令牌
        response.sessionToken = GenerateSessionToken();
        response.result = AUTH_SUCCESS;
        
        // 4. 记录认证信息
        RecordAuthentication(request, response);
        
        return SUCCESS;
    }
    
    // 权限验证
    bool VerifyPermission(const std::string& sessionToken,
                         const Permission& required) {
        auto session = GetSession(sessionToken);
        if (!session) {
            return false;
        }
        
        return session->permission >= required;
    }
    
private:
    AuthConfig m_config;
    std::map<std::string, Session> m_sessions;
};
```

### 2. 访问控制
```cpp
class CMmAccessControl {
public:
    // 访问控制列表
    struct ACL {
        std::string resource;       // 资源标识
        std::string principal;      // 主体标识
        Permission permission;       // 权限
        bool allow;                 // 允许/拒绝
    };
    
    // 检查访问权限
    bool CheckAccess(const std::string& principal,
                    const std::string& resource,
                    const Permission& action) {
        // 查找ACL规则
        auto rules = FindACLRules(principal, resource);
        
        // 应用规则
        for (const auto& rule : rules) {
            if (rule.permission == action) {
                return rule.allow;
            }
        }
        
        // 默认拒绝
        return false;
    }
    
    // 更新ACL
    int UpdateACL(const ACL& acl) {
        m_aclList.push_back(acl);
        return SUCCESS;
    }
    
private:
    std::vector<ACL> m_aclList;
};
```

## 负载均衡

### 1. 连接负载均衡
```cpp
class CMmLoadBalancer {
public:
    // 负载均衡策略
    enum Strategy {
        ROUND_ROBIN,        // 轮询
        LEAST_CONNECTIONS,  // 最少连接
        WEIGHTED,          // 加权
        HASH_BASED         // 哈希
    };
    
    // 选择服务器
    ServerInfo* SelectServer(const ClientRequest& request) {
        switch (m_strategy) {
            case ROUND_ROBIN:
                return RoundRobinSelect();
            case LEAST_CONNECTIONS:
                return LeastConnectionsSelect();
            case WEIGHTED:
                return WeightedSelect();
            case HASH_BASED:
                return HashBasedSelect(request);
        }
        return nullptr;
    }
    
    // 更新服务器状态
    void UpdateServerStatus(const std::string& serverId,
                          const ServerStatus& status) {
        m_serverStatus[serverId] = status;
    }
    
private:
    Strategy m_strategy;
    std::vector<ServerInfo> m_servers;
    std::map<std::string, ServerStatus> m_serverStatus;
};
```

## 监控与统计

### 1. 性能监控
```cpp
class CMmPerformanceMonitor {
public:
    // 性能指标
    struct Metrics {
        uint64_t totalRequests;     // 总请求数
        uint64_t successRequests;   // 成功请求数
        uint64_t failedRequests;    // 失败请求数
        double avgResponseTime;     // 平均响应时间
        double maxResponseTime;     // 最大响应时间
        uint64_t bytesReceived;     // 接收字节数
        uint64_t bytesSent;        // 发送字节数
        uint32_t activeConnections; // 活动连接数
    };
    
    // 更新指标
    void UpdateMetrics(const RequestInfo& info) {
        m_metrics.totalRequests++;
        if (info.success) {
            m_metrics.successRequests++;
        } else {
            m_metrics.failedRequests++;
        }
        UpdateResponseTime(info.responseTime);
    }
    
    // 获取指标
    Metrics GetMetrics() const {
        return m_metrics;
    }
    
private:
    Metrics m_metrics;
    std::mutex m_metricsMutex;
};
```

### 2. 日志服务
```cpp
class CMmLoggingService {
public:
    // 记录请求日志
    void LogRequest(const ClientRequest& request) {
        LogEntry entry;
        entry.timestamp = GetCurrentTime();
        entry.clientId = request.clientId;
        entry.requestType = request.type;
        entry.details = FormatRequest(request);
        
        WriteLog(entry);
    }
    
    // 记录错误日志
    void LogError(const ErrorInfo& error) {
        ErrorEntry entry;
        entry.timestamp = GetCurrentTime();
        entry.errorCode = error.code;
        entry.errorMsg = error.message;
        entry.stackTrace = error.stackTrace;
        
        WriteErrorLog(entry);
    }
    
private:
    LogFile m_requestLog;
    LogFile m_errorLog;
};
```

## 配置参数

### 服务端配置
```ini
[Nw103MmServer_Config]
# 服务配置
ServerPort = 2404              # 服务端口
MaxConnections = 100           # 最大连接数
ConnectionTimeout = 30000      # 连接超时(ms)
RequestTimeout = 10000         # 请求超时(ms)

# 认证配置
EnableAuthentication = true    # 启用认证
AuthenticationMode = MIXED     # 认证模式(CERT/PASSWORD/MIXED)
SessionTimeout = 3600000       # 会话超时(ms)

# 数据服务配置
EnableRealtimeService = true   # 启用实时数据服务
EnableHistoricalService = true # 启用历史数据服务
EnableEventService = true      # 启用事件服务
MaxSubscriptions = 10000       # 最大订阅数

# 负载均衡配置
EnableLoadBalancing = true     # 启用负载均衡
LoadBalanceStrategy = LEAST_CONNECTIONS  # 负载均衡策略

# 性能配置
ThreadPoolSize = 16            # 线程池大小
SendBufferSize = 65536        # 发送缓冲区大小
RecvBufferSize = 65536        # 接收缓冲区大小

# 日志配置
LogLevel = INFO               # 日志级别
LogPath = /var/log/nw103mm/   # 日志路径
MaxLogSize = 100              # 最大日志大小(MB)
LogRotateCount = 10           # 日志轮转数量
```

## 高可用设计

### 1. 主备切换
```cpp
class CMmHighAvailability {
public:
    // 主备状态
    enum HAState {
        ACTIVE,     // 主用
        STANDBY,    // 备用
        SWITCHING   // 切换中
    };
    
    // 执行切换
    int PerformSwitchover() {
        // 1. 通知客户端
        NotifyClientsOfSwitchover();
        
        // 2. 同步状态
        SyncStateToStandby();
        
        // 3. 切换角色
        SwitchRole();
        
        // 4. 恢复服务
        RestoreService();
        
        return SUCCESS;
    }
    
    // 健康检查
    void HealthCheck() {
        if (!IsHealthy()) {
            TriggerSwitchover();
        }
    }
    
private:
    HAState m_state;
    std::string m_peerId;
};
```

## 使用示例

### 服务端启动流程
```cpp
// 1. 创建服务端
auto server = std::make_unique<CNw103MmServer>();

// 2. 加载配置
Nw103MmServerConfig config;
config.LoadFromFile("nw103mm_server.ini");
server->Configure(config);

// 3. 注册服务处理器
server->RegisterHandler(MM_DATA_SUBSCRIBE, 
    [](const Request& req, Response& resp) {
        // 处理数据订阅请求
        return ProcessSubscribe(req, resp);
    });

// 4. 设置认证回调
server->SetAuthCallback([](const AuthRequest& req) {
    // 自定义认证逻辑
    return AuthenticateClient(req);
});

// 5. 启动服务
server->StartService(2404);

// 6. 发布数据
Nw103Data data;
data.pointId = 1001;
data.value = 220.5;
data.quality = GOOD;
server->PublishData(data);

// 7. 监控服务状态
auto metrics = server->GetMetrics();
std::cout << "Active connections: " << metrics.activeConnections << std::endl;
```

## 注意事项
- 合理配置最大连接数
- 注意权限管理
- 监控服务性能
- 定期清理过期会话
- 备份重要配置

## 相关模块
- ZcsNw103MmCliPro: 南网103主主站客户端
- ZcsGw103MmSrvPro: 国网103主主站服务端
- ZcsPro103SrvFlow: 103服务端流程
