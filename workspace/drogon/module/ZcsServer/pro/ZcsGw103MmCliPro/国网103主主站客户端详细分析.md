# ZcsGw103MmCliPro - 国网103主主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsGw103MmCliPro
- **主要功能**: 实现国网IEC103协议的主站到主站客户端，支持国网主站间数据交换
- **设计目的**: 实现国网调度中心与地区主站之间的标准化通信

## 核心组件

### 1. 国网主主站客户端架构
```cpp
class CGw103MmClient {
public:
    // 连接国网主站
    int ConnectToGwMaster(const std::string& masterId);
    
    // 国网数据订阅
    int SubscribeGwData(const GwDataFilter& filter);
    
    // 国网数据发布
    int PublishToGwMaster(const GwData& data);
    
    // 国网控制转发
    int ForwardGwControl(const GwControlCmd& cmd);
    
private:
    // 国网认证
    GwAuthenticator m_auth;
    
    // 数据交换管理
    GwDataExchange m_exchange;
};
```

### 2. 国网数据交换协议
```cpp
namespace GwExchangeProtocol {
    // 交换数据类型
    enum ExchangeType {
        GW_REALTIME_EXCHANGE = 1,   // 实时数据交换
        GW_PLAN_EXCHANGE = 2,       // 计划数据交换
        GW_FAULT_EXCHANGE = 3,      // 故障数据交换
        GW_DISPATCH_EXCHANGE = 4    // 调度数据交换
    };
    
    // 交换数据包
    struct ExchangePacket {
        std::string sourceStation;  // 源主站
        std::string targetStation;  // 目标主站
        ExchangeType type;          // 交换类型
        std::vector<uint8_t> data; // 数据内容
        uint64_t timestamp;         // 时标
        std::string signature;      // 数字签名
    };
}
```

## 国网主站间通信实现

### 1. 调度数据交换
```cpp
class CGwDispatchExchange {
public:
    // 发送调度指令
    int SendDispatchCommand(const DispatchCmd& cmd) {
        // 1. 权限验证
        if (!VerifyDispatchPermission(cmd)) {
            return ERR_NO_PERMISSION;
        }
        
        // 2. 指令签名
        SignDispatchCommand(cmd);
        
        // 3. 发送到目标主站
        return SendToTargetMaster(cmd);
    }
    
    // 接收调度反馈
    int ReceiveDispatchFeedback(DispatchFeedback& feedback) {
        // 接收反馈数据
        ReceiveFeedback(feedback);
        
        // 验证签名
        if (!VerifySignature(feedback)) {
            return ERR_INVALID_SIGNATURE;
        }
        
        return SUCCESS;
    }
    
private:
    DispatchValidator m_validator;
    SignatureManager m_signMgr;
};
```

### 2. 国网安全机制
```cpp
class CGwSecurityManager {
public:
    // 数字签名
    std::string SignData(const std::vector<uint8_t>& data) {
        // 使用国网CA证书签名
        return m_ca.Sign(data);
    }
    
    // 数据加密
    int EncryptData(std::vector<uint8_t>& data) {
        // 使用国密算法SM4加密
        return m_sm4.Encrypt(data);
    }
    
    // 身份认证
    int AuthenticateStation(const std::string& stationId) {
        // 国网统一身份认证
        return m_authService.Authenticate(stationId);
    }
    
private:
    GwCAService m_ca;
    SM4Encryptor m_sm4;
    GwAuthService m_authService;
};
```

## 配置参数

### 国网主主站配置
```ini
[Gw103Mm_Config]
# 本地主站配置
LocalStationId = GW_DISPATCH_BJ    # 本地主站ID
LocalStationName = 北京调度中心     # 本地主站名称

# 对端主站配置
PeerStationId = GW_REGIONAL_HB     # 对端主站ID
PeerStationIP = **********         # 对端IP

# 安全配置
EnableEncryption = true            # 启用加密
EncryptionAlgorithm = SM4          # 国密算法
CertificatePath = /etc/gw/ca.pem   # CA证书路径

# 数据交换配置
ExchangeMode = BIDIRECTIONAL       # 双向交换
DataCompressionEnabled = true      # 数据压缩
MaxExchangeRate = 10000            # 最大交换速率
```

## 使用示例

```cpp
// 1. 创建国网主主站客户端
auto gwMmClient = std::make_unique<CGw103MmClient>();

// 2. 初始化配置
GwMmConfig config;
config.LoadFromFile("gw103mm_config.ini");
gwMmClient->Initialize(config);

// 3. 连接对端主站
gwMmClient->ConnectToGwMaster("GW_REGIONAL_HB");

// 4. 订阅数据
GwDataFilter filter;
filter.stationPattern = "HB_*";
filter.dataType = GW_REALTIME_EXCHANGE;
gwMmClient->SubscribeGwData(filter);

// 5. 发送调度指令
DispatchCmd cmd;
cmd.targetStation = "HB_SUBSTATION_01";
cmd.commandType = DISPATCH_LOAD_CONTROL;
gwMmClient->SendDispatchCommand(cmd);
```

## 注意事项
- 确保国网CA证书有效
- 使用国密算法进行加密
- 严格执行权限管理
- 保证数据交换的实时性

## 相关模块
- ZcsGw103MmSrvPro: 国网103主主站服务端
- ZcsGw103MsCliPro: 国网103主站客户端
