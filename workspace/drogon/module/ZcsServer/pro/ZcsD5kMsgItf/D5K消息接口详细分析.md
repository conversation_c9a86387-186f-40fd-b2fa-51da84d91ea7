# ZcsD5kMsgItf - D5K消息接口详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsD5kMsgItf
- **主要功能**: 实现与D5000平台的消息总线交互接口
- **设计目的**: 提供D5K总线消息收发、文件传输、包处理等功能

## 核心文件分析

### 1. ZcsD5kMsgItf.h/cpp - D5K消息接口主类

#### 类定义
```cpp
class CZcsD5kMsgItf {
private:
    // 总线交换对象
    CZXLoadBusSwapLib* m_pLoadBusSwapLib;  // 总线库加载器
    IZxBusSwap* m_pBusSwap;                // 总线交换接口
    
    // 观察者和发布者
    CZcsD5kMsgItfObserver* m_pObserver;    // 观察者
    CZxRetransmitPublisher* m_pPublisher;  // 转发发布者
    
    // 数据库访问
    CXJDBFacade* m_pDBFacade;              // 数据库接口
    
    // 包处理
    CZxPackageHdl* m_pPackageHdl;          // 包处理器
    
    // 文件发送
    CSendFile* m_pSendFile;                // 文件发送器
    
    // 命令缓存
    TSttpCommandCache m_mapCmdCache;       // 命令缓存映射
    CXJLock m_lockCache;                   // 缓存锁
    
    // 消息队列
    queue<BUS_RECV_STTPFULLDATA_INFO> m_queueRecvData;  // 接收队列
    CXJLock m_lockRecvQueue;               // 接收队列锁
    
    // 配置参数
    STRUCT_XJPROMSGBUSSERVER m_serverConfig; // 服务器配置
    TIME_OUT m_timeout;                    // 超时配置
    
    // 线程管理
    THREAD_HANDLE m_hProcessThread;        // 处理线程
    bool m_bExit;                          // 退出标志
    
public:
    // 生命周期管理
    int Start(const STRUCT_XJPROMSGBUSSERVER& pServer);
    int End();
    
    // 日志管理
    void WriteLog(const char* pLog, int nLevel);
    
protected:
    // 初始化函数
    bool InitLogFile(const STRUCT_XJPROMSGBUSSERVER& pServer);
    bool InitD5kBusSwapObj();
    bool InitObserver();
    bool InitPublisher();
    bool InitPackageHandler();
    
    // 释放函数
    bool ReleasD5kBusSwapObj();
    void ReleaseObserver();
    bool ReleasePublisher();
    void ReleasePackageHandler();
    
    // 消息处理
    int ProcessRecvData();
    int HandleSttpMessage(const STTP_FULL_DATA& sttpData);
    int HandleCommandMessage(const STTP_FULL_DATA& sttpData);
    int HandleFileMessage(const STTP_FULL_DATA& sttpData);
    
    // 发布功能
    int PublishToSubscribers(const STTP_FULL_DATA& sttpData);
    
    // 命令缓存管理
    int AddToCommandCache(const SSttpCommand& cmd);
    int GetFromCommandCache(UINT uRII, SSttpCommand& cmd);
    int RemoveFromCommandCache(UINT uRII);
    
    // 回调函数
    static int OnRecvD5kMsg(LPVOID pRegObj, D5K_MSG& msg, 
                           string& strNodeName, BUS_ADDITION_INFO& info);
    static int OnRecvSttpMsg(LPVOID pRegObj, STTP_FULL_DATA& sttpData,
                           string& strNodeName, BUS_ADDITION_INFO& info);
    static int OnBusLinkStatusChange(LPVOID pRegObj, int nStatus, void* pReserved);
    
    // 线程函数
    static THREAD_FUNC ProcessThread(LPVOID pParam);
    void ProcessLoop();
};
```

#### 数据结构定义
```cpp
// STTP命令缓存
typedef struct _STTP_COMMAND_CACHE_TYPE {
    UINT uRII;                      // 请求标识
    UINT uMsgId;                    // 消息ID
    bool bIsFull;                   // 是否完整
    string sNodeName;               // 节点名称
    string strDevID;                // 设备ID
    vector<STTPMSG> vResultList;    // 结果列表
    void* pReserve;                 // 保留字段
} SSttpCommand;

// 总线接收数据信息
typedef struct _BUS_RECV_STTPFULLDATA_INFO {
    STTP_FULL_DATA sttp_data;       // STTP数据
    string strNodeName;             // 节点名称
    BUS_ADDITION_INFO addition_info; // 附加信息
} BUS_RECV_STTPFULLDATA_INFO;

// 超时配置
typedef struct _TIME_OUT {
    UINT nCallWaveFile;             // 召唤录波文件超时
    UINT nCallWaveList;             // 召唤录波列表超时
    _TIME_OUT() {
        nCallWaveList = 120;
        nCallWaveFile = 120;
    }
} TIME_OUT;

// 厂站信息
typedef struct _STN_INF {
    string strStnId;                // 厂站ID
    vector<string> vDevId;          // 设备ID列表
} STN_INF;
```

### 2. ZcsD5kMsgItfObserver.h/cpp - D5K消息观察者

#### 类定义
```cpp
class CZcsD5kMsgItfObserver : public CXJObserver {
private:
    CZcsD5kMsgItf* m_pD5kMsgItf;    // 消息接口指针
    
public:
    CZcsD5kMsgItfObserver(CZcsD5kMsgItf* pItf);
    virtual ~CZcsD5kMsgItfObserver();
    
    // 观察者接口实现
    virtual void Update(const STTP_FULL_DATA& data) override;
    virtual void OnConnect(const string& publisher) override;
    virtual void OnDisconnect(const string& publisher) override;
    
    // 消息处理
    int ProcessD5kMessage(const D5K_MSG& msg);
    int ProcessSttpMessage(const STTP_FULL_DATA& sttpData);
    
    // 订阅管理
    int Subscribe(const string& topic);
    int Unsubscribe(const string& topic);
};
```

### 3. ZxPackageHdl.h/cpp - 包处理器

#### 类定义
```cpp
class CZxPackageHdl {
private:
    // 包缓存
    struct PACKAGE_INFO {
        int nPackageID;             // 包ID
        int nTotalFragments;        // 总分片数
        int nReceivedFragments;     // 已接收分片数
        map<int, FRAGMENT> fragments; // 分片映射
        time_t tCreateTime;         // 创建时间
    };
    
    map<int, PACKAGE_INFO> m_mapPackages;  // 包信息映射
    CXJLock m_lockPackages;                // 包锁
    
    // 配置
    int m_nMaxPackageSize;          // 最大包大小
    int m_nFragmentSize;           // 分片大小
    int m_nTimeout;                // 超时时间
    
public:
    // 包处理接口
    int HandlePackageStart(const PACKAGE_START& start);
    int HandlePackageFragment(const PACKAGE_FRAGMENT& fragment);
    int HandlePackageEnd(const PACKAGE_END& end);
    
    // 分片管理
    int AddFragment(int packageID, const FRAGMENT& fragment);
    bool IsPackageComplete(int packageID);
    int AssemblePackage(int packageID, BYTE* buffer, int& len);
    
    // 包发送
    int SendPackage(const BYTE* data, int len, int destID);
    int SplitToFragments(const BYTE* data, int len, vector<FRAGMENT>& fragments);
    
    // 超时处理
    void CheckTimeout();
    void RemoveTimeoutPackages();
    
    // 包类型处理
    int ProcessWaveFilePackage(const WAVE_PACKAGE& package);
    int ProcessDataPackage(const DATA_PACKAGE& package);
    int ProcessCommandPackage(const CMD_PACKAGE& package);
};
```

#### 包处理流程
```cpp
int CZxPackageHdl::HandlePackageFragment(const PACKAGE_FRAGMENT& fragment) {
    CCsLocker lock(&m_lockPackages);
    
    // 1. 查找包信息
    auto it = m_mapPackages.find(fragment.nPackageID);
    if (it == m_mapPackages.end()) {
        // 新包，创建包信息
        PACKAGE_INFO info;
        info.nPackageID = fragment.nPackageID;
        info.nTotalFragments = fragment.nTotalFragments;
        info.nReceivedFragments = 0;
        info.tCreateTime = time(NULL);
        m_mapPackages[fragment.nPackageID] = info;
        it = m_mapPackages.find(fragment.nPackageID);
    }
    
    // 2. 添加分片
    FRAGMENT frag;
    frag.nIndex = fragment.nIndex;
    frag.nSize = fragment.nSize;
    frag.pData = new BYTE[fragment.nSize];
    memcpy(frag.pData, fragment.pData, fragment.nSize);
    
    it->second.fragments[fragment.nIndex] = frag;
    it->second.nReceivedFragments++;
    
    // 3. 检查是否完整
    if (IsPackageComplete(fragment.nPackageID)) {
        // 组装包
        BYTE* buffer = new BYTE[m_nMaxPackageSize];
        int len = 0;
        if (AssemblePackage(fragment.nPackageID, buffer, len) == 0) {
            // 处理完整包
            ProcessCompletePackage(buffer, len);
        }
        delete[] buffer;
        
        // 清理包信息
        CleanupPackage(fragment.nPackageID);
    }
    
    return 0;
}
```

### 4. SendFile.h/cpp - 文件发送器

#### 类定义
```cpp
class CSendFile {
private:
    // 文件传输状态
    enum FileTransferState {
        FTS_IDLE,           // 空闲
        FTS_SENDING,        // 发送中
        FTS_RECEIVING,      // 接收中
        FTS_COMPLETE,       // 完成
        FTS_ERROR          // 错误
    };
    
    struct FILE_TRANSFER_INFO {
        string fileName;            // 文件名
        string filePath;           // 文件路径
        FILE* pFile;               // 文件指针
        long fileSize;             // 文件大小
        long transferredSize;      // 已传输大小
        FileTransferState state;   // 传输状态
        time_t startTime;          // 开始时间
        int destID;                // 目标ID
    };
    
    map<int, FILE_TRANSFER_INFO> m_mapTransfers;  // 传输映射
    CXJLock m_lockTransfers;                      // 传输锁
    
public:
    // 文件发送
    int SendFile(const string& fileName, int destID);
    int SendFileBlock(int transferID, const BYTE* data, int len);
    int SendFileComplete(int transferID);
    
    // 文件接收
    int ReceiveFileStart(const FILE_START& start);
    int ReceiveFileBlock(const FILE_BLOCK& block);
    int ReceiveFileComplete(const FILE_COMPLETE& complete);
    
    // 传输管理
    int CreateTransfer(const string& fileName, int destID);
    int CancelTransfer(int transferID);
    FILE_TRANSFER_INFO* GetTransferInfo(int transferID);
    
    // 进度查询
    float GetTransferProgress(int transferID);
    int GetTransferSpeed(int transferID);
    
    // 错误处理
    void HandleTransferError(int transferID, int errorCode);
    void RetryTransfer(int transferID);
};
```

#### 文件发送实现
```cpp
int CSendFile::SendFile(const string& fileName, int destID) {
    // 1. 打开文件
    FILE* pFile = fopen(fileName.c_str(), "rb");
    if (!pFile) {
        WriteLog("Failed to open file", LOG_ERROR);
        return -1;
    }
    
    // 2. 获取文件大小
    fseek(pFile, 0, SEEK_END);
    long fileSize = ftell(pFile);
    fseek(pFile, 0, SEEK_SET);
    
    // 3. 创建传输信息
    int transferID = CreateTransfer(fileName, destID);
    FILE_TRANSFER_INFO* pInfo = GetTransferInfo(transferID);
    pInfo->pFile = pFile;
    pInfo->fileSize = fileSize;
    pInfo->state = FTS_SENDING;
    pInfo->startTime = time(NULL);
    
    // 4. 发送文件开始消息
    FILE_START start;
    start.transferID = transferID;
    start.fileName = fileName;
    start.fileSize = fileSize;
    SendFileStart(start, destID);
    
    // 5. 分块发送文件
    const int BLOCK_SIZE = 8192;
    BYTE buffer[BLOCK_SIZE];
    long sentSize = 0;
    
    while (sentSize < fileSize) {
        int readSize = fread(buffer, 1, BLOCK_SIZE, pFile);
        if (readSize > 0) {
            FILE_BLOCK block;
            block.transferID = transferID;
            block.offset = sentSize;
            block.size = readSize;
            block.pData = buffer;
            
            SendFileBlock(transferID, buffer, readSize);
            sentSize += readSize;
            pInfo->transferredSize = sentSize;
        } else {
            break;
        }
    }
    
    // 6. 发送文件完成消息
    FILE_COMPLETE complete;
    complete.transferID = transferID;
    complete.success = (sentSize == fileSize);
    SendFileComplete(transferID);
    
    // 7. 清理
    fclose(pFile);
    pInfo->state = FTS_COMPLETE;
    
    return 0;
}
```

## D5K总线集成

### 1. 总线初始化
```cpp
bool CZcsD5kMsgItf::InitD5kBusSwapObj() {
    // 1. 加载总线库
    m_pLoadBusSwapLib = new CZXLoadBusSwapLib(&m_logFile);
    if (!m_pLoadBusSwapLib->LoadShareLib(D5K_BUSSWAP_TYPE)) {
        WriteLog("Load D5K bus library failed", LOG_ERROR);
        return false;
    }
    
    // 2. 创建总线实例
    m_pBusSwap = m_pLoadBusSwapLib->CreateBusIns(&m_logFile, m_serverConfig.szLogPath);
    if (!m_pBusSwap) {
        WriteLog("Create bus instance failed", LOG_ERROR);
        return false;
    }
    
    // 3. 初始化应用节点
    APP_NODE_INFO nodeInfo;
    strcpy(nodeInfo.szNodeName, m_serverConfig.szNodeName);
    nodeInfo.nNodeType = NODE_TYPE_SERVICE;
    nodeInfo.nGroupID = m_serverConfig.nGroupID;
    
    if (m_pBusSwap->InitAppNode(nodeInfo) < 0) {
        WriteLog("Init app node failed", LOG_ERROR);
        return false;
    }
    
    // 4. 注册回调
    m_pBusSwap->RegRecvD5kMsgCallBak(OnRecvD5kMsg, this);
    m_pBusSwap->RegRecvSttpMsgCallBak(OnRecvSttpMsg, this);
    m_pBusSwap->RegBusLinkStatusChgCallBak(OnBusLinkStatusChange, this);
    
    // 5. 启动总线
    if (m_pBusSwap->StartBusSwap() < 0) {
        WriteLog("Start bus swap failed", LOG_ERROR);
        return false;
    }
    
    return true;
}
```

### 2. 消息接收处理
```cpp
int CZcsD5kMsgItf::OnRecvSttpMsg(LPVOID pRegObj, 
                                 STTP_FULL_DATA& sttpData,
                                 string& strNodeName, 
                                 BUS_ADDITION_INFO& info) {
    CZcsD5kMsgItf* pThis = (CZcsD5kMsgItf*)pRegObj;
    
    // 1. 创建接收信息
    BUS_RECV_STTPFULLDATA_INFO recvInfo;
    recvInfo.sttp_data = sttpData;
    recvInfo.strNodeName = strNodeName;
    recvInfo.addition_info = info;
    
    // 2. 加入接收队列
    {
        CCsLocker lock(&pThis->m_lockRecvQueue);
        pThis->m_queueRecvData.push(recvInfo);
    }
    
    // 3. 触发处理
    pThis->m_semProcess.Post();
    
    return 0;
}
```

## 性能优化

### 1. 异步处理
- 消息接收和处理分离
- 使用队列缓冲
- 独立处理线程

### 2. 包分片传输
- 大数据分片
- 并行传输
- 自动重组

### 3. 缓存机制
- 命令缓存
- 文件缓存
- 结果缓存

## 配置示例

```ini
[D5kMsgItf]
NodeName=D5kMsgItf
NodeType=Service
GroupID=1

[BusSwap]
BusType=D5K
MaxMsgSize=65536
Timeout=30

[FileTransfer]
MaxFileSize=104857600
BlockSize=8192
TransferTimeout=300

[Package]
MaxPackageSize=10485760
FragmentSize=8192
PackageTimeout=120
```

## 使用注意事项

1. **总线连接**: 确保D5K总线正常运行
2. **节点配置**: 正确配置节点名称和组ID
3. **文件传输**: 大文件传输注意超时设置
4. **包处理**: 分片包需要正确的序号管理
5. **内存管理**: 及时清理传输完成的缓存
