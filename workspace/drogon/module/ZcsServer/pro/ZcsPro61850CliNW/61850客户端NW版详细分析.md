# ZcsPro61850CliNW - IEC 61850客户端NW版详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsPro61850CliNW
- **主要功能**: IEC 61850协议客户端网络优化版本，支持广域网通信
- **设计目的**: 针对网络环境复杂、带宽受限的广域网场景优化

## 核心组件

### 1. NW版本特性
- **网络优化**：适应广域网高延迟环境
- **带宽控制**：智能带宽管理
- **断线重连**：增强的重连机制
- **数据压缩**：支持数据压缩传输

### 2. 网络适配层
```cpp
class CNWNetworkAdapter {
public:
    // 网络质量检测
    int DetectNetworkQuality(NetworkQuality& quality);
    
    // 自适应传输
    int AdaptiveTransmit(const DataPacket& packet);
    
    // 带宽控制
    int SetBandwidthLimit(int maxBandwidth);
    
    // 压缩传输
    int CompressAndSend(const uint8_t* data, int length);
    
private:
    // 网络状态
    NetworkState m_netState;
    
    // 带宽管理器
    BandwidthManager m_bwManager;
    
    // 压缩引擎
    CompressionEngine m_compressor;
};
```

### 3. 连接管理优化
```cpp
class CNWConnectionManager {
public:
    // 多路径连接
    int EstablishMultiPath(const std::vector<std::string>& servers);
    
    // 智能重连
    int SmartReconnect();
    
    // 连接池管理
    int ManageConnectionPool();
    
    // 负载均衡
    int LoadBalance(const DataRequest& request);
    
private:
    // 连接池
    std::vector<Connection*> m_connectionPool;
    
    // 重连策略
    ReconnectStrategy m_reconnStrategy;
    
    // 负载均衡器
    LoadBalancer m_loadBalancer;
};
```

## NW版本特殊功能

### 1. 数据同步机制
```cpp
class CNWDataSynchronizer {
public:
    // 增量同步
    int IncrementalSync(const std::string& lastSyncPoint);
    
    // 全量同步
    int FullSync();
    
    // 冲突解决
    int ResolveConflict(const DataConflict& conflict);
    
    // 同步状态查询
    SyncStatus GetSyncStatus();
    
private:
    // 同步队列
    std::queue<SyncTask> m_syncQueue;
    
    // 版本控制
    VersionController m_versionCtrl;
};
```

### 2. 缓存策略
```cpp
class CNWCacheStrategy {
public:
    // 智能预取
    int PrefetchData(const PredictionModel& model);
    
    // 分级缓存
    int HierarchicalCache(const DataItem& item);
    
    // 缓存同步
    int SyncCache(const std::string& peerId);
    
    // 缓存淘汰
    int EvictCache(size_t targetSize);
    
private:
    // L1缓存（内存）
    MemoryCache m_l1Cache;
    
    // L2缓存（磁盘）
    DiskCache m_l2Cache;
    
    // 缓存策略
    CachePolicy m_policy;
};
```

### 3. QoS保障
```cpp
class CNWQoSManager {
public:
    // 设置QoS级别
    int SetQoSLevel(QoSLevel level);
    
    // 优先级队列
    int PriorityEnqueue(const DataPacket& packet, int priority);
    
    // 流量整形
    int TrafficShaping(int rate);
    
    // 拥塞控制
    int CongestionControl();
    
private:
    // QoS配置
    QoSConfig m_qosConfig;
    
    // 优先级队列
    std::priority_queue<QueueItem> m_priorityQueue;
};
```

## 广域网优化策略

### 1. 传输优化
- **TCP优化**：调整TCP参数适应高延迟
- **UDP选项**：关键数据使用UDP传输
- **多路复用**：单连接承载多路数据流

### 2. 数据优化
```cpp
// NW数据优化器
class CNWDataOptimizer {
public:
    // 数据压缩
    int CompressData(const uint8_t* input, int inLen, 
                     uint8_t* output, int& outLen);
    
    // 数据去重
    int DeduplicateData(DataSet& dataset);
    
    // 批量打包
    int BatchPackaging(const std::vector<DataItem>& items);
    
    // 增量传输
    int DeltaTransmit(const DataSnapshot& prev, 
                      const DataSnapshot& curr);
};
```

## 安全增强

### 1. 加密传输
```cpp
class CNWSecurityLayer {
public:
    // TLS/SSL加密
    int EnableTLS(const TLSConfig& config);
    
    // 数据加密
    int EncryptData(const uint8_t* plain, int len, 
                    uint8_t* cipher);
    
    // 身份认证
    int Authenticate(const Credentials& creds);
    
    // 访问控制
    int CheckAccess(const AccessRequest& request);
    
private:
    // 加密上下文
    CryptoContext m_cryptoCtx;
    
    // 证书管理
    CertificateManager m_certMgr;
};
```

### 2. VPN支持
- **自动VPN检测**
- **VPN穿透优化**
- **多VPN路径支持**

## 配置参数

### NW版本特定配置
```ini
[NW_Config]
# 网络配置
MaxBandwidth = 10240      # 最大带宽(KB/s)
NetworkTimeout = 30000    # 网络超时(ms)
RetryCount = 5           # 重试次数
RetryInterval = 5000     # 重试间隔(ms)

# 缓存配置
L1CacheSize = 100        # L1缓存大小(MB)
L2CacheSize = 1024       # L2缓存大小(MB)
CacheExpireTime = 3600   # 缓存过期时间(秒)

# 压缩配置
EnableCompression = true  # 启用压缩
CompressionLevel = 6     # 压缩级别(1-9)
MinCompressSize = 1024   # 最小压缩大小(字节)

# QoS配置
EnableQoS = true         # 启用QoS
DefaultPriority = 5      # 默认优先级(1-10)
```

## 监控与诊断

### 1. 网络监控
```cpp
class CNWMonitor {
public:
    // 带宽监控
    BandwidthStats GetBandwidthStats();
    
    // 延迟监控
    LatencyStats GetLatencyStats();
    
    // 丢包率监控
    PacketLossStats GetPacketLossStats();
    
    // 连接状态
    ConnectionStats GetConnectionStats();
    
private:
    // 统计收集器
    StatsCollector m_statsCollector;
};
```

### 2. 性能指标
- **吞吐量**：实时/平均吞吐量
- **延迟**：往返时间(RTT)
- **丢包率**：数据包丢失率
- **连接稳定性**：连接保持时间

## 故障恢复

### 1. 自动故障转移
```cpp
class CNWFailover {
public:
    // 主备切换
    int SwitchToBackup();
    
    // 故障检测
    int DetectFailure();
    
    // 状态同步
    int SyncState(const std::string& peerId);
    
    // 恢复检查
    int CheckRecovery();
    
private:
    // 故障检测器
    FailureDetector m_detector;
    
    // 备份服务器列表
    std::vector<BackupServer> m_backupServers;
};
```

## 使用示例

### NW客户端使用流程
```cpp
// 1. 创建NW客户端
auto nwClient = std::make_unique<CNWClient61850>();

// 2. 配置网络参数
NWConfig config;
config.maxBandwidth = 10240;  // 10MB/s
config.enableCompression = true;
config.enableQoS = true;
nwClient->Configure(config);

// 3. 建立连接（支持多服务器）
std::vector<std::string> servers = {
    "primary.server.com:102",
    "backup.server.com:102"
};
nwClient->ConnectMultiPath(servers);

// 4. 设置QoS
nwClient->SetQoS(QOS_HIGH);

// 5. 订阅数据（带缓存）
nwClient->SubscribeWithCache("IED1/MMXU1", [](const Data& data) {
    ProcessData(data);
});

// 6. 监控网络状态
auto stats = nwClient->GetNetworkStats();
if (stats.bandwidth < 1024) {  // 低于1MB/s
    nwClient->EnableCompression(true);
}
```

## 与其他版本对比

### 功能对比
| 功能 | 标准版 | TC版 | NW版 |
|-----|--------|------|------|
| 网络优化 | 基础 | 基础 | 高级 |
| 数据压缩 | 不支持 | 不支持 | 支持 |
| 断线重连 | 简单 | 简单 | 智能 |
| QoS控制 | 不支持 | 不支持 | 支持 |
| 多路径 | 不支持 | 不支持 | 支持 |

## 注意事项
- 适用于广域网环境
- 注意带宽限制设置
- 合理配置缓存大小
- 监控网络质量
- 启用必要的安全措施

## 相关模块
- ZcsPro61850Cli: 标准61850客户端
- ZcsPro61850CliTC: TC版61850客户端
- ZcsProMainFlow: 协议主流程
