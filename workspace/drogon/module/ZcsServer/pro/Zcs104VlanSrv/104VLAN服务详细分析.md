# Zcs104VlanSrv - IEC 104 VLAN服务详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/Zcs104VlanSrv
- **主要功能**: 基于IEC 104和103协议的电力系统通信服务，支持VLAN隔离和数据透传
- **设计目的**: 实现电力调度系统中的厂站通信、协议转换和安全传输

## 版本信息
- **当前版本**: 1.0.39 (2025-07-22)
- **编译平台**: Linux (支持AIX、SUN、HP等多平台)
- **开发语言**: C/C++
- **特殊功能**: SM2国密加密、MMS协议解析

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Zcs104VlanSrv 主程序                      │
├─────────────────────────────────────────────────────────────┤
│  Main.cpp (程序入口)                                         │
│  ├── 信号处理 (Signal Handler)                               │
│  ├── 服务管理 (Service Management)                           │
│  └── 进程管理 (Process Management)                           │
├─────────────────────────────────────────────────────────────┤
│  TaskMngr (任务管理器)                                        │
│  ├── 配置管理 (Configuration Management)                     │
│  ├── 线程管理 (Thread Management)                            │
│  ├── 厂站连接管理 (Station Connection Management)            │
│  └── 消息总线接口 (Message Bus Interface)                    │
├─────────────────────────────────────────────────────────────┤
│  协议处理层 (Protocol Layer)                                 │
│  ├── IEC 104 APCI Handler                                   │
│  ├── IEC 103 ASDU Handler                                   │
│  ├── MMS 协议解析器                                          │
│  └── SM2 加密模块                                            │
├─────────────────────────────────────────────────────────────┤
│  数据管理层 (Data Management Layer)                          │
│  ├── DeviceObj (设备对象管理)                                │
│  ├── DevFlowManage (设备流量管理)                            │
│  ├── Push/Push103 (数据推送模块)                             │
│  └── Msg103 (103消息管理)                                   │
├─────────────────────────────────────────────────────────────┤
│  网络通信层 (Network Layer)                                  │
│  ├── TCP Socket 管理                                        │
│  ├── 连接状态监控                                            │
│  └── 数据收发缓存                                            │
└─────────────────────────────────────────────────────────────┘
```

## 核心文件分析

### 1. Main.cpp - 程序入口

#### 主要功能
- 程序初始化和启动
- 信号处理注册
- Windows服务支持
- 进程管理

#### 关键代码结构
```cpp
int main(int argc, char* argv[]) {
    // 1. 参数处理
    ParseArguments(argc, argv);
    
    // 2. 信号注册
    RegisterSignalHandlers();
    
    // 3. 初始化任务管理器
    TaskMngr* taskMgr = new TaskMngr();
    taskMgr->Init();
    
    // 4. 启动服务
    taskMgr->Start();
    
    // 5. 主循环
    while (!g_bExit) {
        OS_Sleep(1000);
        taskMgr->CheckStatus();
    }
    
    // 6. 清理退出
    taskMgr->Stop();
    delete taskMgr;
    
    return 0;
}
```

### 2. DevFlowManage.h/cpp - 设备流量管理

#### 类定义
```cpp
class DevFlowManage {
private:
    // 设备映射表
    map<int, DeviceObj*> m_mapDevices;    // 设备对象映射
    CXJLock m_lockDevices;                // 设备锁
    
    // 线程管理
    THREAD_HANDLE m_linkThdHandle;        // 链路监测线程
    THREAD_HANDLE m_dealForDatHandle;     // 数据处理线程
    
    // 回调函数
    void* m_pFrontObj;                    // 前置对象
    PW_SEND2FNT_RECALL m_pSend2FntFun;   // 发送到前置回调
    PW_DEVSTATECHANGE_RECALL m_pDevStateFun; // 设备状态变化回调
    
    void* m_pStnObj;                      // 站端对象
    PW_SEND2STN_RECALL m_pSend2StnFun;   // 发送到站端回调
    PW_SENDCONNECT_RECALL m_pConnectFun;  // 连接回调
    PW_SENDCONNECTCLOSE_RECALL m_pCloseFun; // 断开回调
    
public:
    // 初始化和释放
    void _init(CMessageLog* pLogFile, bool* bExit, const string& config);
    void release();
    void reset();
    
    // 设备管理
    void addSecDev(string& chNo, string& name, string& proType);
    
    // 消息管理
    bool addStnMsg(int nChNo, string& msg);  // 添加站端消息
    bool addFntMsg(int nChNo, string msg);   // 添加前置消息
    
    // 连接状态管理
    bool setDevStnSta(int nChNo, LinkStatus sta);  // 设置站端状态
    LinkStatus getDevStnSta(int nChNo);            // 获取站端状态
    bool setDevFntSta(int nChNo, bool sta);        // 设置前置状态
    void resetStnSta();                            // 重置所有状态
    
    // 回调设置
    void setSendToFrontFun(void* obj, PW_SEND2FNT_RECALL pFun);
    void setNotifyDevStateFun(void* obj, PW_DEVSTATECHANGE_RECALL pFun);
    void setSendToStnFun(void* obj, PW_SEND2STN_RECALL pFun);
    void setSendConnectFun(void* obj, PW_SENDCONNECT_RECALL pFun);
    void setSendConnCloseFun(void* obj, PW_SENDCONNECTCLOSE_RECALL pFun);
    
protected:
    // 线程函数
    static THREAD_FUNC handleLinkThread(LPVOID pParam);
    static THREAD_FUNC datHandCentThread(LPVOID pParam);
    
    // 数据处理
    void send2StnDeal(DeviceObj* ptrObj);  // 站端数据处理
    void send2FntDeal(DeviceObj* ptrObj);  // 前置数据处理
};
```

#### 主要功能
1. **设备管理**: 管理多个设备对象，维护设备状态
2. **流量控制**: 控制站端和前置之间的数据流
3. **连接监控**: 监控设备连接状态，自动重连
4. **数据缓存**: 缓存待发送数据，保证数据不丢失

### 3. DeviceObj.h/cpp - 设备对象

#### 类定义
```cpp
class DeviceObj {
private:
    int m_nChNo;                    // 通道号
    string m_strName;               // 设备名称
    string m_strProType;            // 协议类型
    
    LinkStatus m_stnStatus;         // 站端连接状态
    bool m_fntStatus;               // 前置连接状态
    
    queue<string> m_stnMsgQueue;    // 站端消息队列
    queue<string> m_fntMsgQueue;    // 前置消息队列
    CXJLock m_lockQueue;            // 队列锁
    
    time_t m_lastActiveTime;        // 最后活动时间
    
public:
    // 构造和析构
    DeviceObj(int chNo, const string& name, const string& proType);
    ~DeviceObj();
    
    // 属性访问
    int getChNo() const { return m_nChNo; }
    string getName() const { return m_strName; }
    string getProType() const { return m_strProType; }
    
    // 状态管理
    void setStnStatus(LinkStatus status);
    LinkStatus getStnStatus() const;
    void setFntStatus(bool status);
    bool getFntStatus() const;
    
    // 消息管理
    bool pushStnMsg(const string& msg);
    bool popStnMsg(string& msg);
    bool pushFntMsg(const string& msg);
    bool popFntMsg(string& msg);
    
    // 活动时间
    void updateActiveTime();
    bool isTimeout(int seconds) const;
};
```

#### 连接状态枚举
```cpp
enum LinkStatus {
    LINK_UNKNOWN = 0,      // 未知状态
    LINK_CONNECTING = 1,   // 正在连接
    LINK_CONNECTED = 2,    // 已连接
    LINK_DISCONNECTED = 3, // 已断开
    LINK_ERROR = 4        // 错误状态
};
```

### 4. Push.h/cpp - 102协议数据推送

#### 类定义
```cpp
class push {
private:
    // 网络相关
    SOCKET m_socket;                // 套接字
    string m_strIP;                 // IP地址
    int m_nPort;                    // 端口
    
    // 协议处理
    APCI_Handler* m_pAPCIHandler;   // APCI处理器
    ASDU_Handler* m_pASDUHandler;   // ASDU处理器
    
    // 数据缓存
    queue<BYTE*> m_sendQueue;       // 发送队列
    queue<BYTE*> m_recvQueue;       // 接收队列
    
public:
    // 连接管理
    bool connect(const string& ip, int port);
    void disconnect();
    bool isConnected() const;
    
    // 数据发送
    bool sendData(const BYTE* data, int len);
    bool sendIFrame(const ASDU* asdu);
    bool sendSFrame();
    bool sendUFrame(U_FUNC func);
    
    // 数据接收
    int recvData(BYTE* buffer, int maxLen);
    bool processRecvData();
    
    // 协议处理
    bool handleAPCI(const APCI* apci);
    bool handleASDU(const ASDU* asdu);
};
```

### 5. Push103.h/cpp - 103协议数据推送

#### 类定义
```cpp
class push103 {
private:
    // 设备管理
    DevFlowManage* m_pDevFlowMgr;   // 设备流量管理器
    
    // 协议转换
    Protocol103* m_pProtocol103;     // 103协议处理器
    
    // 消息缓存
    map<int, MSG103_CACHE> m_mapCache; // 消息缓存映射
    
public:
    // 初始化
    bool init(DevFlowManage* pMgr);
    
    // 消息处理
    bool send2Fnt(string& msg, int chNo);
    void devStateChange(int chNo, int state);
    
    // 协议转换
    bool convert104To103(const BYTE* data104, int len, BYTE* data103, int& outLen);
    bool convert103To104(const BYTE* data103, int len, BYTE* data104, int& outLen);
    
    // 缓存管理
    void cacheMessage(int chNo, const MSG103& msg);
    bool getCachedMessage(int chNo, MSG103& msg);
};
```

### 6. Msg103.h/cpp - 103消息管理

#### 消息结构
```cpp
struct MSG103 {
    BYTE typeId;         // 类型标识
    BYTE vsq;           // 可变结构限定词
    BYTE cot;           // 传送原因
    BYTE addr;          // 公共地址
    BYTE fun;           // 功能类型
    BYTE inf;           // 信息序号
    vector<BYTE> data;  // 数据内容
    
    // 序列化和反序列化
    bool serialize(BYTE* buffer, int& len);
    bool deserialize(const BYTE* buffer, int len);
};
```

### 7. SecDevFlowModule.h/cpp - 安全设备流量模块

#### 安全功能
```cpp
class SecDevFlowModule {
private:
    // SM2加密
    SM2_Crypto* m_pSM2;             // SM2加密对象
    
    // 安全策略
    SecurityPolicy m_policy;        // 安全策略
    
public:
    // 加密解密
    bool encrypt(const BYTE* plain, int plainLen, BYTE* cipher, int& cipherLen);
    bool decrypt(const BYTE* cipher, int cipherLen, BYTE* plain, int& plainLen);
    
    // 签名验证
    bool sign(const BYTE* data, int len, BYTE* signature, int& sigLen);
    bool verify(const BYTE* data, int len, const BYTE* signature, int sigLen);
    
    // 安全检查
    bool checkSecurity(const DeviceObj* dev);
    bool validateMessage(const MSG103& msg);
};
```

## 数据流程

### 1. 站端到前置数据流
```
站端设备 → DeviceObj::pushStnMsg() → 站端消息队列
    ↓
DevFlowManage::datHandCentThread() → 数据处理线程
    ↓
DevFlowManage::send2FntDeal() → 前置数据处理
    ↓
回调函数 m_pSend2FntFun() → 发送到前置
    ↓
前置设备
```

### 2. 前置到站端数据流
```
前置设备 → DeviceObj::pushFntMsg() → 前置消息队列
    ↓
DevFlowManage::datHandCentThread() → 数据处理线程
    ↓
DevFlowManage::send2StnDeal() → 站端数据处理
    ↓
回调函数 m_pSend2StnFun() → 发送到站端
    ↓
站端设备
```

### 3. 连接状态管理流程
```
DevFlowManage::handleLinkThread() → 链路监测线程
    ↓
检查设备超时 → isTimeout()
    ↓
状态变化 → setDevStnSta() / setDevFntSta()
    ↓
通知回调 → m_pDevStateFun() / m_pConnectFun()
```

## MMS协议支持

### MMS目录结构
```
MMS/
├── MmsParser.cpp/.h        # MMS协议解析器
├── MmsEncoder.cpp/.h       # MMS编码器
├── MmsDecoder.cpp/.h       # MMS解码器
└── MmsDataTypes.h          # MMS数据类型定义
```

### MMS功能
- 读写服务
- 目录服务
- 文件服务
- 日志服务
- 变量访问

## APCI目录结构
```
APCI/
├── ApciHandler.cpp/.h      # APCI处理器
├── ApciFrame.cpp/.h        # APCI帧定义
├── ApciEncoder.cpp/.h      # APCI编码器
└── ApciDecoder.cpp/.h      # APCI解码器
```

## 配置文件

### 主配置文件
```ini
[General]
MaxDevices=100              # 最大设备数
MaxTimeOut=60              # 超时时间(秒)
LogLevel=3                 # 日志级别

[Network]
ListenPort=2404            # 监听端口
MaxConnections=50          # 最大连接数
BufferSize=8192           # 缓冲区大小

[Security]
EnableSM2=true            # 启用SM2加密
CertPath=/etc/certs/      # 证书路径

[Protocol]
Support103=true           # 支持103协议
Support104=true           # 支持104协议
SupportMMS=true          # 支持MMS协议
```

## 性能优化

### 1. 多线程架构
- 链路监测线程：独立监控连接状态
- 数据处理线程：并行处理数据转发
- 消息队列：解耦收发，提高吞吐量

### 2. 缓存机制
- 消息缓存：防止数据丢失
- 状态缓存：减少状态查询
- 协议缓存：复用协议对象

### 3. 连接池
- 预建立连接
- 连接复用
- 自动重连

## 安全特性

### 1. SM2国密算法
- 数据加密传输
- 数字签名验证
- 密钥协商

### 2. VLAN隔离
- 网络层隔离
- 设备分组管理
- 访问控制

### 3. 安全审计
- 操作日志记录
- 异常行为检测
- 安全事件告警

## 故障处理

### 1. 连接异常
- 自动重连机制
- 断线重传
- 状态恢复

### 2. 数据异常
- CRC校验
- 序号检查
- 重复过滤

### 3. 系统异常
- 异常捕获
- 优雅降级
- 故障隔离

## 使用注意事项

1. **配置检查**: 启动前检查配置文件完整性
2. **端口冲突**: 确保监听端口未被占用
3. **权限设置**: 程序需要网络访问权限
4. **日志管理**: 定期清理日志文件
5. **性能监控**: 监控CPU和内存使用
6. **安全更新**: 及时更新安全补丁
