# Zcs104VlanSrv - IEC 104 VLAN协议服务详细分析

## 概述

Zcs104VlanSrv是Dragon项目中专门处理IEC 104 over VLAN协议的服务实现，主要用于湖南地区的电力系统通信。该服务实现了IEC 60870-5-104标准协议，支持通过VLAN网络进行电力设备的数据采集和控制。

## 模块文件结构

```
Zcs104VlanSrv/
├── APCI/                           # APCI层协议处理
│   ├── pro/                        # 协议处理实现
│   │   ├── ZcsHn104VlanCliPro/    # 湖南104 VLAN客户端协议
│   │   └── ZcsHn104VlanFlow/      # 湖南104 VLAN流程处理
├── DevFlowManage.h/.cpp           # 设备流程管理
├── DeviceObj.h/.cpp               # 设备对象抽象
├── Main.cpp                       # 主程序入口
├── MMS/                          # MMS协议支持
├── Msg103.h/.cpp                 # 103消息处理
├── push.h/.cpp                   # 数据推送处理
├── push103.h/.cpp                # 103协议推送
├── SecDevFlowModule.h/.cpp       # 安全设备流程模块
├── SM2/                          # SM2加密算法支持
├── TaskMngr.h/.cpp               # 任务管理器
├── Zcs104VlanSrv.ini             # 配置文件
├── Zcs104VlanSrv.sh              # 启动脚本
└── 更新历史和编译配置文件
```

## 核心架构设计

### 1. IEC 104协议栈实现

IEC 60870-5-104协议是建立在TCP/IP基础上的电力系统通信协议，该服务实现了完整的协议栈：

**协议层次结构**：
```
应用层 (Application Layer)
    ↓ ASDU (Application Service Data Unit)
传输层 (Transport Layer) 
    ↓ APCI (Application Protocol Control Information)
网络层 (Network Layer)
    ↓ TCP/IP
物理层 (Physical Layer)
    ↓ VLAN以太网
```

### 2. 主程序入口分析

#### Main.cpp - 程序启动入口
**功能**：程序的主入口，负责初始化和启动各个组件

**启动流程**：
```cpp
int main(int argc, char* argv[])
{
    // 1. 解析命令行参数
    if (!ParseCommandLine(argc, argv)) {
        PrintUsage();
        return -1;
    }
    
    // 2. 读取配置文件
    if (!LoadConfiguration("Zcs104VlanSrv.ini")) {
        printf("配置文件加载失败\n");
        return -1;
    }
    
    // 3. 初始化日志系统
    if (!InitializeLogging()) {
        printf("日志系统初始化失败\n");
        return -1;
    }
    
    // 4. 初始化设备管理器
    CDeviceManager deviceMgr;
    if (deviceMgr.Initialize() != 0) {
        WriteLog("设备管理器初始化失败", LOG_ERROR);
        return -1;
    }
    
    // 5. 启动任务管理器
    CTaskManager taskMgr;
    if (taskMgr.Start() != 0) {
        WriteLog("任务管理器启动失败", LOG_ERROR);
        return -1;
    }
    
    // 6. 启动设备流程管理器
    CDevFlowManager flowMgr;
    if (flowMgr.Start() != 0) {
        WriteLog("流程管理器启动失败", LOG_ERROR);
        return -1;
    }
    
    // 7. 注册信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    // 8. 主循环
    while (g_running) {
        // 定期检查各组件状态
        CheckComponentStatus();
        sleep(1);
    }
    
    // 9. 清理资源
    Cleanup();
    return 0;
}
```

### 3. 设备对象管理

#### DeviceObj.h/.cpp - 设备对象抽象
**功能**：定义电力设备的抽象基类和具体实现

**设备对象架构**：
```cpp
class CDeviceObj {
protected:
    string m_deviceId;           // 设备ID
    string m_deviceName;         // 设备名称
    DEVICE_TYPE m_deviceType;    // 设备类型
    DEVICE_STATUS m_status;      // 设备状态
    SOCKET m_socket;            // 通信套接字
    time_t m_lastCommTime;      // 最后通信时间
    
public:
    // 纯虚函数，子类必须实现
    virtual int Connect() = 0;              // 连接设备
    virtual int Disconnect() = 0;           // 断开连接
    virtual int SendCommand(const CMD_PACKET& cmd) = 0;  // 发送命令
    virtual int ProcessReceivedData(const char* data, int length) = 0; // 处理接收数据
    
    // 公共方法
    DEVICE_STATUS GetStatus() const { return m_status; }
    bool IsConnected() const { return m_status == DEVICE_CONNECTED; }
    time_t GetLastCommTime() const { return m_lastCommTime; }
};

// IEC 104设备具体实现
class C104Device : public CDeviceObj {
private:
    C104ProtocolHandler m_protocolHandler;  // 104协议处理器
    CAPCIHandler m_apciHandler;            // APCI处理器
    CASPDUHandler m_asduHandler;           // ASDU处理器
    
public:
    // 连接到IEC 104设备
    virtual int Connect() override {
        // 1. 创建TCP套接字
        m_socket = socket(AF_INET, SOCK_STREAM, 0);
        if (m_socket == INVALID_SOCKET) {
            WriteLog("创建套接字失败", LOG_ERROR);
            return -1;
        }
        
        // 2. 连接到设备
        sockaddr_in deviceAddr;
        deviceAddr.sin_family = AF_INET;
        deviceAddr.sin_addr.s_addr = inet_addr(m_deviceIP.c_str());
        deviceAddr.sin_port = htons(m_devicePort);
        
        if (connect(m_socket, (sockaddr*)&deviceAddr, sizeof(deviceAddr)) != 0) {
            WriteLog("连接设备失败: %s:%d", m_deviceIP.c_str(), m_devicePort);
            return -1;
        }
        
        // 3. 启动接收线程
        m_recvThread = CreateThread(NULL, 0, RecvThreadProc, this, 0, NULL);
        
        // 4. 发送启动报文
        Send_STARTDT_ACT();
        
        m_status = DEVICE_CONNECTED;
        WriteLog("成功连接到设备 %s", m_deviceName.c_str());
        return 0;
    }
    
    // 发送IEC 104命令
    virtual int SendCommand(const CMD_PACKET& cmd) override {
        // 1. 构造ASDU
        ASDU_PACKET asdu;
        if (BuildASDU(cmd, asdu) != 0) {
            return -1;
        }
        
        // 2. 添加APCI头
        APCI_PACKET apci;
        apci.start = 0x68;
        apci.length = sizeof(asdu);
        apci.control = BuildControlField(FRAME_TYPE_I);
        
        // 3. 发送数据
        char sendBuffer[256];
        memcpy(sendBuffer, &apci, sizeof(apci));
        memcpy(sendBuffer + sizeof(apci), &asdu, sizeof(asdu));
        
        return send(m_socket, sendBuffer, sizeof(apci) + sizeof(asdu), 0);
    }
    
    // 接收线程处理函数
    static DWORD WINAPI RecvThreadProc(LPVOID param) {
        C104Device* pThis = (C104Device*)param;
        char recvBuffer[1024];
        
        while (pThis->m_running) {
            int recvLen = recv(pThis->m_socket, recvBuffer, sizeof(recvBuffer), 0);
            if (recvLen > 0) {
                pThis->ProcessReceivedData(recvBuffer, recvLen);
            }
            else if (recvLen == 0) {
                // 连接断开
                pThis->m_status = DEVICE_DISCONNECTED;
                break;
            }
        }
        return 0;
    }
};
```

### 4. 协议处理层

#### APCI层处理 (push.h/.cpp)

**功能**：处理IEC 104协议的应用协议控制信息层

**APCI帧格式**：
```
+-------+-------+-------+-------+-------+-------+
| START | LENGTH|    CONTROL FIELD    | ASDU... |
| 0x68  |  LEN  | CF1 | CF2 | CF3 | CF4 |        |
+-------+-------+-------+-------+-------+-------+
```

**实现示例**：
```cpp
class CAPCIHandler {
private:
    WORD m_sendSequence;     // 发送序列号
    WORD m_recvSequence;     // 接收序列号
    
public:
    // 处理接收到的APCI帧
    int ProcessAPCIFrame(const char* data, int length) {
        if (length < 6) {
            WriteLog("APCI帧长度不足", LOG_ERROR);
            return -1;
        }
        
        APCI_HEADER* header = (APCI_HEADER*)data;
        
        // 检查起始字符
        if (header->start != 0x68) {
            WriteLog("无效的APCI帧起始字符", LOG_ERROR);
            return -1;
        }
        
        // 解析控制域
        CONTROL_FIELD control;
        control.byte1 = header->control[0];
        control.byte2 = header->control[1];
        control.byte3 = header->control[2];
        control.byte4 = header->control[3];
        
        // 根据帧类型处理
        if (IsIFrame(control)) {
            return ProcessIFrame(control, data + 6, header->length - 4);
        }
        else if (IsSFrame(control)) {
            return ProcessSFrame(control);
        }
        else if (IsUFrame(control)) {
            return ProcessUFrame(control);
        }
        
        return 0;
    }
    
    // 处理I帧（信息传输帧）
    int ProcessIFrame(const CONTROL_FIELD& control, const char* asduData, int asduLength) {
        // 1. 检查序列号
        WORD recvSeq = ExtractReceiveSequence(control);
        if (recvSeq != m_sendSequence) {
            WriteLog("接收序列号不匹配: 期望=%d, 实际=%d", m_sendSequence, recvSeq);
            // 发送确认帧
            SendSFrame(m_recvSequence);
            return -1;
        }
        
        // 2. 更新接收序列号
        m_recvSequence = ExtractSendSequence(control) + 1;
        
        // 3. 处理ASDU数据
        return ProcessASPDU(asduData, asduLength);
    }
    
    // 发送I帧
    int SendIFrame(const ASDU_PACKET& asdu) {
        APCI_PACKET packet;
        packet.start = 0x68;
        packet.length = sizeof(asdu) + 4;
        
        // 构造控制域
        CONTROL_FIELD control = {0};
        SetSendSequence(control, m_sendSequence);
        SetReceiveSequence(control, m_recvSequence);
        
        memcpy(packet.control, &control, 4);
        
        // 发送数据
        char sendBuffer[256];
        memcpy(sendBuffer, &packet, 6);
        memcpy(sendBuffer + 6, &asdu, sizeof(asdu));
        
        int result = send(m_socket, sendBuffer, packet.length + 2, 0);
        if (result > 0) {
            m_sendSequence++;
        }
        
        return result;
    }
};
```

### 5. ASDU处理层

**功能**：处理应用服务数据单元，实现具体的电力系统功能

**ASDU结构**：
```cpp
struct ASDU_HEADER {
    BYTE typeId;        // 类型标识
    BYTE vsq;           // 可变结构限定词
    BYTE cot;           // 传送原因
    BYTE orgAddr;       // 源地址
    WORD comAddr;       // 公共地址
};

class CASPDUHandler {
public:
    // 处理不同类型的ASDU
    int ProcessASPDU(const char* data, int length) {
        ASDU_HEADER* header = (ASDU_HEADER*)data;
        
        switch (header->typeId) {
            case ASDU_TYPE_M_SP_NA_1:     // 单点信息
                return ProcessSinglePointInfo(data, length);
                
            case ASDU_TYPE_M_DP_NA_1:     // 双点信息  
                return ProcessDoublePointInfo(data, length);
                
            case ASDU_TYPE_M_ME_NA_1:     // 测量值，标准化值
                return ProcessNormalizedValue(data, length);
                
            case ASDU_TYPE_M_ME_NB_1:     // 测量值，标度化值
                return ProcessScaledValue(data, length);
                
            case ASDU_TYPE_C_SC_NA_1:     // 单命令
                return ProcessSingleCommand(data, length);
                
            case ASDU_TYPE_C_DC_NA_1:     // 双命令
                return ProcessDoubleCommand(data, length);
        }
        
        return 0;
    }
    
    // 处理单点信息
    int ProcessSinglePointInfo(const char* data, int length) {
        ASDU_HEADER* header = (ASDU_HEADER*)data;
        SINGLE_POINT_INFO* info = (SINGLE_POINT_INFO*)(data + sizeof(ASDU_HEADER));
        
        // 提取信息元素数量
        int elementCount = header->vsq & 0x7F;
        bool isSequence = (header->vsq & 0x80) != 0;
        
        for (int i = 0; i < elementCount; i++) {
            // 构造STTP消息
            STTP_FULL_DATA sttpData;
            BuildSinglePointSttp(info[i], sttpData);
            
            // 发送到消息总线
            SendToMessageBus(sttpData);
        }
        
        return 0;
    }
    
    // 处理单命令
    int ProcessSingleCommand(const char* data, int length) {
        ASDU_HEADER* header = (ASDU_HEADER*)data;
        SINGLE_COMMAND* cmd = (SINGLE_COMMAND*)(data + sizeof(ASDU_HEADER));
        
        // 执行命令
        int result = ExecuteSingleCommand(cmd);
        
        // 发送命令确认
        SendCommandConfirmation(header->typeId, header->cot, result);
        
        return 0;
    }
};
```

### 6. 数据推送机制

#### push.h/.cpp - 数据推送处理
**功能**：实现数据的推送和转发机制

```cpp
class CDataPusher {
private:
    CMessageQueue m_pushQueue;      // 推送队列
    HANDLE m_pushThread;           // 推送线程
    vector<PUSH_TARGET> m_targets; // 推送目标列表
    
public:
    // 添加数据到推送队列
    void PushData(const PUSH_DATA& data) {
        m_pushQueue.Push(data);
    }
    
    // 推送线程处理函数
    static DWORD WINAPI PushThreadProc(LPVOID param) {
        CDataPusher* pThis = (CDataPusher*)param;
        
        while (pThis->m_running) {
            PUSH_DATA data;
            if (pThis->m_pushQueue.Pop(data, 100)) {
                // 推送到所有目标
                for (auto& target : pThis->m_targets) {
                    pThis->PushToTarget(data, target);
                }
            }
        }
        return 0;
    }
    
    // 推送到指定目标
    void PushToTarget(const PUSH_DATA& data, const PUSH_TARGET& target) {
        switch (target.type) {
            case TARGET_MESSAGE_BUS:
                PushToMessageBus(data, target);
                break;
            case TARGET_DATABASE:
                PushToDatabase(data, target);
                break;
            case TARGET_FILE:
                PushToFile(data, target);
                break;
        }
    }
};
```

### 7. 设备流程管理

#### DevFlowManage.h/.cpp - 设备流程管理器
**功能**：管理设备的通信流程和状态转换

```cpp
class CDevFlowManager {
private:
    map<string, CDeviceFlow*> m_deviceFlows;  // 设备流程映射
    HANDLE m_manageThread;                    // 管理线程
    
public:
    // 启动设备流程
    int StartDeviceFlow(const string& deviceId) {
        DEVICE_CONFIG config = GetDeviceConfig(deviceId);
        
        CDeviceFlow* pFlow = new CDeviceFlow(deviceId, config);
        if (pFlow->Start() == 0) {
            m_deviceFlows[deviceId] = pFlow;
            WriteLog("设备流程启动成功: %s", deviceId.c_str());
            return 0;
        }
        
        delete pFlow;
        return -1;
    }
    
    // 停止设备流程  
    void StopDeviceFlow(const string& deviceId) {
        auto it = m_deviceFlows.find(deviceId);
        if (it != m_deviceFlows.end()) {
            it->second->Stop();
            delete it->second;
            m_deviceFlows.erase(it);
        }
    }
    
    // 设备流程管理线程
    static DWORD WINAPI ManageThreadProc(LPVOID param) {
        CDevFlowManager* pThis = (CDevFlowManager*)param;
        
        while (pThis->m_running) {
            // 检查所有设备流程状态
            for (auto& pair : pThis->m_deviceFlows) {
                CDeviceFlow* pFlow = pair.second;
                
                if (pFlow->GetStatus() == FLOW_STATUS_ERROR) {
                    // 流程异常，尝试重启
                    WriteLog("设备流程异常，准备重启: %s", pair.first.c_str());
                    pThis->RestartDeviceFlow(pair.first);
                }
            }
            
            Sleep(FLOW_CHECK_INTERVAL);
        }
        return 0;
    }
};
```

### 8. 任务管理器

#### TaskMngr.h/.cpp - 任务管理器
**功能**：管理系统中的各种任务和定时器

```cpp
class CTaskManager {
private:
    struct TASK_INFO {
        string taskId;
        TASK_TYPE type;
        time_t nextRunTime;
        int interval;           // 执行间隔(秒)
        TASK_CALLBACK callback;
        void* userData;
        bool enabled;
    };
    
    map<string, TASK_INFO> m_tasks;
    HANDLE m_taskThread;
    
public:
    // 添加定时任务
    int AddTask(const string& taskId, TASK_TYPE type, int interval, 
                TASK_CALLBACK callback, void* userData) {
        TASK_INFO task;
        task.taskId = taskId;
        task.type = type;
        task.interval = interval;
        task.nextRunTime = time(NULL) + interval;
        task.callback = callback;
        task.userData = userData;
        task.enabled = true;
        
        m_tasks[taskId] = task;
        return 0;
    }
    
    // 任务执行线程
    static DWORD WINAPI TaskThreadProc(LPVOID param) {
        CTaskManager* pThis = (CTaskManager*)param;
        
        while (pThis->m_running) {
            time_t currentTime = time(NULL);
            
            for (auto& pair : pThis->m_tasks) {
                TASK_INFO& task = pair.second;
                
                if (task.enabled && currentTime >= task.nextRunTime) {
                    // 执行任务
                    try {
                        task.callback(task.taskId.c_str(), task.userData);
                        
                        // 更新下次执行时间
                        task.nextRunTime = currentTime + task.interval;
                    }
                    catch (...) {
                        WriteLog("任务执行异常: %s", task.taskId.c_str());
                    }
                }
            }
            
            Sleep(1000); // 每秒检查一次
        }
        return 0;
    }
};
```

## 配置文件分析

### Zcs104VlanSrv.ini配置参数
```ini
[SYSTEM_CONFIG]
log_level=3                    # 日志级别
log_path=./logs               # 日志路径
max_device_count=100          # 最大设备数量

[NETWORK_CONFIG]
listen_port=2404              # IEC 104标准端口
connection_timeout=30         # 连接超时时间
heartbeat_interval=20         # 心跳间隔
max_retry_count=3             # 最大重试次数

[PROTOCOL_CONFIG]
apci_timeout=15               # APCI超时时间
asdu_address_length=2         # ASDU地址长度
cause_of_transmission_length=2 # 传送原因长度
common_address_length=2        # 公共地址长度

[DATABASE_CONFIG]
db_server=*************       # 数据库服务器
db_name=scada_db              # 数据库名称
connection_pool_size=10       # 连接池大小

[DEVICE_LIST]
device_001=************:2404  # 设备1地址
device_002=************:2404  # 设备2地址
```

## 安全机制

### 1. SM2加密支持
该服务集成了SM2国产密码算法，提供数据加密和数字签名功能：

```cpp
class CSM2Handler {
public:
    // SM2加密
    int EncryptData(const char* plainData, int plainLen, 
                   char* encryptedData, int& encryptedLen) {
        // 使用SM2公钥加密数据
        return SM2_Encrypt(m_publicKey, plainData, plainLen, 
                          encryptedData, &encryptedLen);
    }
    
    // SM2解密
    int DecryptData(const char* encryptedData, int encryptedLen,
                   char* plainData, int& plainLen) {
        // 使用SM2私钥解密数据
        return SM2_Decrypt(m_privateKey, encryptedData, encryptedLen,
                          plainData, &plainLen);
    }
};
```

### 2. 安全设备流程模块
SecDevFlowModule提供了安全的设备通信流程：

```cpp
class CSecDevFlowModule {
public:
    // 安全连接建立
    int EstablishSecureConnection(const string& deviceId) {
        // 1. 建立TCP连接
        if (EstablishTCPConnection(deviceId) != 0) {
            return -1;
        }
        
        // 2. 交换密钥
        if (ExchangeKeys(deviceId) != 0) {
            return -2;
        }
        
        // 3. 身份认证
        if (AuthenticateDevice(deviceId) != 0) {
            return -3;
        }
        
        // 4. 建立安全通道
        return EstablishSecureChannel(deviceId);
    }
};
```

## 性能优化和监控

### 1. 连接池管理
```cpp
class CConnectionPool {
private:
    queue<SOCKET> m_availableConnections;
    set<SOCKET> m_activeConnections;
    mutex m_poolMutex;
    
public:
    SOCKET GetConnection() {
        lock_guard<mutex> lock(m_poolMutex);
        
        if (!m_availableConnections.empty()) {
            SOCKET conn = m_availableConnections.front();
            m_availableConnections.pop();
            m_activeConnections.insert(conn);
            return conn;
        }
        
        // 创建新连接
        return CreateNewConnection();
    }
    
    void ReturnConnection(SOCKET conn) {
        lock_guard<mutex> lock(m_poolMutex);
        
        m_activeConnections.erase(conn);
        m_availableConnections.push(conn);
    }
};
```

### 2. 性能监控
```cpp
class CPerformanceMonitor {
private:
    struct PERF_COUNTER {
        DWORD messagesSent;
        DWORD messagesReceived;
        DWORD errors;
        DWORD reconnections;
        double avgResponseTime;
    };
    
    map<string, PERF_COUNTER> m_deviceCounters;
    
public:
    void UpdateCounter(const string& deviceId, COUNTER_TYPE type, DWORD value) {
        PERF_COUNTER& counter = m_deviceCounters[deviceId];
        
        switch (type) {
            case COUNTER_MESSAGE_SENT:
                counter.messagesSent += value;
                break;
            case COUNTER_MESSAGE_RECEIVED:
                counter.messagesReceived += value;
                break;
            case COUNTER_ERROR:
                counter.errors += value;
                break;
        }
    }
};
```

这个IEC 104 VLAN协议服务是Dragon系统处理湖南地区电力设备通信的核心组件，实现了标准的IEC 60870-5-104协议，具备完整的安全机制和性能优化措施。
