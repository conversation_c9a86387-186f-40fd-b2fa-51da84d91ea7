# ZcsNw103MmCliPro - 南网103主主站客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsNw103MmCliPro
- **主要功能**: 实现南网IEC103协议的主站到主站客户端，支持主站间数据交换
- **设计目的**: 实现调度主站与集控主站之间的103协议通信

## 核心组件

### 1. 主主站通信特性
- **双向通信**：支持主站间双向数据交换
- **权限分级**：不同主站具有不同权限级别
- **数据筛选**：按需传输特定数据
- **冗余支持**：支持主备通道切换

### 2. 客户端架构
```cpp
class CNw103MmClient {
public:
    // 连接到对端主站
    int ConnectToPeerMaster(const std::string& ip, int port);
    
    // 数据订阅
    int SubscribeData(const DataFilter& filter);
    
    // 数据发布
    int PublishData(const Nw103Data& data);
    
    // 控制转发
    int ForwardControl(const ControlCommand& cmd);
    
    // 事件同步
    int SyncEvents(const EventFilter& filter);
    
private:
    // 连接管理
    PeerConnectionManager m_connMgr;
    
    // 数据过滤器
    DataFilterEngine m_filterEngine;
    
    // 权限控制器
    PermissionController m_permCtrl;
};
```

### 3. 数据交换模型
```cpp
// 主站间数据交换结构
struct MmDataExchange {
    // 数据标识
    struct DataIdentifier {
        uint16_t stationId;     // 厂站标识
        uint16_t deviceId;      // 装置标识
        uint16_t pointId;       // 测点标识
    } id;
    
    // 数据内容
    union DataContent {
        float analogValue;      // 模拟量值
        bool digitalValue;      // 数字量值
        uint32_t counterValue;  // 计数器值
    } content;
    
    // 元数据
    struct MetaData {
        uint32_t timestamp;     // 时标
        uint8_t quality;        // 品质
        uint8_t source;         // 数据源
        uint8_t priority;       // 优先级
    } meta;
};
```

## 协议扩展实现

### 1. 主站间特殊ASDU
```cpp
// 主站间扩展ASDU类型
enum MmAsduType {
    // 标准103类型
    STANDARD_103_BASE = 0,
    
    // 主站间扩展类型
    MM_DATA_SUBSCRIBE = 200,    // 数据订阅
    MM_DATA_PUBLISH = 201,      // 数据发布
    MM_CONTROL_FORWARD = 202,   // 控制转发
    MM_EVENT_SYNC = 203,        // 事件同步
    MM_CONFIG_SYNC = 204,       // 配置同步
    MM_TIME_SYNC = 205,         // 时间同步
    MM_PERMISSION_REQ = 206,    // 权限请求
    MM_STATUS_REPORT = 207      // 状态报告
};
```

### 2. 数据过滤机制
```cpp
class CMmDataFilter {
public:
    // 过滤规则
    struct FilterRule {
        enum FilterType {
            BY_STATION,         // 按厂站过滤
            BY_DEVICE,          // 按装置过滤
            BY_TYPE,            // 按类型过滤
            BY_PRIORITY,        // 按优先级过滤
            BY_QUALITY          // 按品质过滤
        } type;
        
        std::vector<uint16_t> includeList;  // 包含列表
        std::vector<uint16_t> excludeList;  // 排除列表
    };
    
    // 应用过滤
    bool ApplyFilter(const MmDataExchange& data);
    
    // 更新过滤规则
    int UpdateRules(const std::vector<FilterRule>& rules);
    
private:
    std::vector<FilterRule> m_rules;
    std::map<std::string, bool> m_filterCache;
};
```

### 3. 权限管理
```cpp
class CMmPermissionManager {
public:
    // 权限级别
    enum PermissionLevel {
        LEVEL_READONLY = 0,     // 只读
        LEVEL_MONITOR = 1,      // 监视
        LEVEL_CONTROL = 2,      // 控制
        LEVEL_CONFIG = 3,       // 配置
        LEVEL_ADMIN = 4         // 管理
    };
    
    // 检查权限
    bool CheckPermission(const std::string& stationId, 
                         PermissionLevel required);
    
    // 申请权限
    int RequestPermission(const std::string& targetStation,
                         PermissionLevel level);
    
    // 授予权限
    int GrantPermission(const std::string& requestStation,
                       PermissionLevel level);
    
private:
    std::map<std::string, PermissionLevel> m_permissions;
};
```

## 数据同步策略

### 1. 实时数据同步
```cpp
class CMmRealtimeSync {
public:
    // 启动实时同步
    int StartRealtimeSync() {
        // 1. 建立数据通道
        EstablishDataChannel();
        
        // 2. 注册数据源
        RegisterDataSources();
        
        // 3. 启动推送线程
        StartPushThread();
        
        return SUCCESS;
    }
    
    // 数据推送
    void PushDataToMaster(const Nw103Data& data) {
        // 1. 检查订阅关系
        if (!IsSubscribed(data.pointId)) {
            return;
        }
        
        // 2. 应用过滤规则
        if (!PassFilter(data)) {
            return;
        }
        
        // 3. 打包数据
        MmDataPacket packet;
        PackageData(data, packet);
        
        // 4. 发送数据
        SendToPeer(packet);
    }
    
private:
    std::set<uint32_t> m_subscribedPoints;
    std::queue<MmDataPacket> m_sendQueue;
};
```

### 2. 历史数据同步
```cpp
class CMmHistoricalSync {
public:
    // 请求历史数据
    int RequestHistoricalData(const TimeRange& range,
                             const DataFilter& filter) {
        // 构造历史数据请求
        HistoricalRequest request;
        request.startTime = range.start;
        request.endTime = range.end;
        request.filter = filter;
        
        // 发送请求
        return SendHistoricalRequest(request);
    }
    
    // 处理历史数据响应
    int ProcessHistoricalResponse(const HistoricalData& data) {
        // 1. 验证数据完整性
        if (!ValidateData(data)) {
            return ERR_INVALID_DATA;
        }
        
        // 2. 存储历史数据
        StoreHistoricalData(data);
        
        // 3. 通知应用层
        NotifyHistoricalDataReady(data);
        
        return SUCCESS;
    }
    
private:
    HistoricalDataBuffer m_histBuffer;
};
```

## 控制协调机制

### 1. 控制转发
```cpp
class CMmControlForwarder {
public:
    // 转发控制命令
    int ForwardControl(const ControlCommand& cmd) {
        // 1. 权限检查
        if (!HasControlPermission(cmd.targetDevice)) {
            return ERR_NO_PERMISSION;
        }
        
        // 2. 冲突检测
        if (DetectControlConflict(cmd)) {
            return ERR_CONTROL_CONFLICT;
        }
        
        // 3. 记录控制日志
        LogControlCommand(cmd);
        
        // 4. 转发命令
        return SendControlToPeer(cmd);
    }
    
    // 处理控制结果
    int ProcessControlResult(const ControlResult& result) {
        // 更新控制状态
        UpdateControlStatus(result);
        
        // 通知请求方
        NotifyControlResult(result);
        
        return SUCCESS;
    }
    
private:
    std::map<uint32_t, ControlStatus> m_controlStatus;
};
```

### 2. 控制优先级
```cpp
struct ControlPriority {
    enum Level {
        EMERGENCY = 0,      // 紧急控制
        HIGH = 1,          // 高优先级
        NORMAL = 2,        // 普通优先级
        LOW = 3            // 低优先级
    };
    
    Level level;
    std::string sourceStation;
    uint32_t timestamp;
};
```

## 故障处理

### 1. 通信故障处理
```cpp
class CMmFaultHandler {
public:
    // 故障检测
    void DetectFaults() {
        // 检测通信超时
        if (IsCommTimeout()) {
            HandleCommTimeout();
        }
        
        // 检测数据异常
        if (IsDataAbnormal()) {
            HandleDataAbnormal();
        }
        
        // 检测主备切换
        if (NeedSwitchover()) {
            PerformSwitchover();
        }
    }
    
    // 故障恢复
    int RecoverFromFault() {
        // 1. 重建连接
        ReestablishConnection();
        
        // 2. 数据补传
        RequestMissingData();
        
        // 3. 状态同步
        SynchronizeState();
        
        return SUCCESS;
    }
    
private:
    FaultStatus m_faultStatus;
    RecoveryStrategy m_strategy;
};
```

## 配置参数

### 主主站客户端配置
```ini
[Nw103Mm_Config]
# 本地主站配置
LocalStationId = DISPATCH_01    # 本地主站标识
LocalStationName = 调度主站      # 本地主站名称
LocalPermissionLevel = 4        # 本地权限级别

# 对端主站配置
PeerStationId = CONTROL_01      # 对端主站标识
PeerStationIP = *************   # 对端IP地址
PeerStationPort = 2404          # 对端端口

# 通信参数
ConnectionMode = TCP            # 连接模式
EnableDualChannel = true        # 启用双通道
PrimaryChannelIP = *************
BackupChannelIP = *************
HeartbeatInterval = 5000        # 心跳间隔(ms)

# 数据同步参数
SyncMode = REALTIME            # 同步模式
DataBufferSize = 10000         # 数据缓冲区大小
FilterUpdateInterval = 60000   # 过滤规则更新间隔(ms)

# 控制参数
EnableControlForward = true     # 启用控制转发
ControlTimeout = 10000         # 控制超时(ms)
MaxControlQueue = 100          # 最大控制队列

# 安全参数
EnableEncryption = true        # 启用加密
AuthenticationMode = CERT      # 认证模式
CertificatePath = /certs/      # 证书路径
```

## 性能优化

### 1. 数据压缩
```cpp
class CMmDataCompressor {
public:
    // 压缩数据
    int CompressData(const std::vector<MmDataExchange>& data,
                     std::vector<uint8_t>& compressed) {
        // 使用LZ4压缩算法
        return LZ4_compress(data, compressed);
    }
    
    // 解压数据
    int DecompressData(const std::vector<uint8_t>& compressed,
                      std::vector<MmDataExchange>& data) {
        return LZ4_decompress(compressed, data);
    }
};
```

### 2. 批量传输
```cpp
class CMmBatchTransfer {
public:
    // 批量发送
    int BatchSend(const std::vector<MmDataPacket>& packets) {
        // 合并小包
        MergeSmallPackets(packets);
        
        // 批量传输
        return TransferBatch(packets);
    }
    
private:
    size_t m_batchSize = 100;
    size_t m_maxPacketSize = 65536;
};
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建主主站客户端
auto mmClient = std::make_unique<CNw103MmClient>();

// 2. 加载配置
Nw103MmConfig config;
config.LoadFromFile("nw103mm_config.ini");
mmClient->Initialize(config);

// 3. 连接对端主站
mmClient->ConnectToPeerMaster("*************", 2404);

// 4. 设置数据过滤
DataFilter filter;
filter.AddStation(1);  // 只接收1号站数据
filter.AddDeviceType(PROTECTION_DEVICE);
mmClient->SetDataFilter(filter);

// 5. 订阅数据
mmClient->SubscribeData(REALTIME_DATA);

// 6. 注册回调
mmClient->RegisterCallback([](const MmDataExchange& data) {
    std::cout << "Received data from peer: " 
              << data.id.pointId << " = " 
              << data.content.analogValue << std::endl;
});

// 7. 转发控制命令
ControlCommand cmd;
cmd.targetDevice = 100;
cmd.controlType = CONTROL_OPEN;
mmClient->ForwardControl(cmd);
```

## 注意事项
- 确保主站标识唯一性
- 合理设置权限级别
- 注意数据过滤配置
- 监控通信状态
- 处理控制冲突

## 相关模块
- ZcsNw103MmSrvPro: 南网103主主站服务端
- ZcsNw103MsCliPro: 南网103主站客户端
- ZcsMmCliProFlow: 主主站客户端流程
