# ZcsPro61850CliTC - IEC 61850客户端TC版详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsPro61850CliTC
- **主要功能**: IEC 61850协议客户端特定版本实现，支持TC（测控）设备通信
- **设计目的**: 针对测控装置的61850协议客户端实现，优化测控数据采集

## 核心组件

### 1. TC版本特性
- **测控数据优化**：专门针对测控数据的处理优化
- **实时性增强**：提高测控数据的实时性
- **批量处理**：支持测控点的批量操作

### 2. 数据模型扩展

#### 测控专用数据类
```cpp
// 测控数据对象
class CTCDataObject {
public:
    // 遥测数据
    struct TelemetryData {
        float value;           // 测量值
        uint32_t quality;      // 品质
        uint64_t timestamp;    // 时标
    };
    
    // 遥信数据
    struct StatusData {
        bool value;            // 状态值
        uint32_t quality;      // 品质
        uint64_t timestamp;    // 时标
    };
    
    // 遥控数据
    struct ControlData {
        int controlType;       // 控制类型
        int controlValue;      // 控制值
        bool selectBeforeOp;   // 选择后操作
    };
};
```

### 3. TC通信管理
```cpp
class CTCCommunicationManager {
public:
    // 初始化TC连接
    int InitializeTCConnection(const TCConfig& config);
    
    // 读取测控数据
    int ReadTCData(const std::string& reference, TCData& data);
    
    // 写入控制命令
    int WriteControl(const std::string& reference, const ControlCmd& cmd);
    
    // 订阅测控数据变化
    int SubscribeTCData(const std::vector<std::string>& refs);
    
private:
    // TC特定配置
    TCConfig m_config;
    
    // 数据缓存
    std::map<std::string, TCData> m_dataCache;
};
```

## TC版本特殊功能

### 1. 测控数据集定义
```cpp
// TC数据集配置
struct TCDataSetConfig {
    std::string name;                    // 数据集名称
    std::vector<std::string> members;    // 成员列表
    int updatePeriod;                    // 更新周期(ms)
    bool buffered;                       // 是否缓存
};
```

### 2. 快速扫描机制
- **周期扫描**：定时扫描测控点
- **变化扫描**：检测数据变化
- **事件驱动**：基于事件的数据更新

### 3. 控制操作优化
```cpp
class CTCControlHandler {
public:
    // 直接控制
    int DirectControl(const std::string& ref, int value);
    
    // 选择后操作控制
    int SelectBeforeOperateControl(const std::string& ref, int value);
    
    // 批量控制
    int BatchControl(const std::vector<ControlItem>& items);
    
    // 控制取消
    int CancelControl(const std::string& ref);
    
private:
    // 控制状态跟踪
    std::map<std::string, ControlState> m_controlStates;
};
```

## 数据映射机制

### 1. IED模型映射
```cpp
// TC版IED映射
class CTCIEDMapper {
public:
    // 加载IED配置
    int LoadIEDConfig(const std::string& scdFile);
    
    // 映射测点
    int MapDataPoint(const std::string& iedRef, int pointId);
    
    // 获取映射关系
    int GetMapping(int pointId, std::string& iedRef);
    
private:
    // 映射表
    std::map<int, std::string> m_pointToIED;
    std::map<std::string, int> m_iedToPoint;
};
```

### 2. 数据转换
- **数值转换**：工程值与原始值转换
- **类型转换**：61850类型与内部类型转换
- **品质映射**：品质位映射

## 性能优化

### 1. TC版本优化策略
- **连接池**：维护多个IED连接
- **数据缓存**：本地缓存频繁访问数据
- **异步处理**：非阻塞IO操作
- **批量传输**：合并小数据包

### 2. 内存管理
```cpp
// TC数据缓冲池
class CTCBufferPool {
public:
    // 获取缓冲区
    Buffer* GetBuffer(size_t size);
    
    // 释放缓冲区
    void ReleaseBuffer(Buffer* buffer);
    
    // 清理过期缓冲
    void CleanExpiredBuffers();
    
private:
    std::queue<Buffer*> m_freeBuffers;
    std::list<Buffer*> m_usedBuffers;
};
```

## 配置参数

### TC版本特定配置
```ini
[TC_Config]
# 测控相关配置
ScanPeriod = 100        # 扫描周期(ms)
ControlTimeout = 5000   # 控制超时(ms)
DataCacheSize = 10000   # 数据缓存大小
EnableBatchMode = true  # 启用批量模式

# 性能配置
MaxConnections = 10     # 最大连接数
BufferPoolSize = 100    # 缓冲池大小
AsyncThreads = 4        # 异步线程数
```

## 报告服务优化

### TC版报告配置
```cpp
struct TCReportConfig {
    bool dataChange;        // 数据变化触发
    bool qualityChange;     // 品质变化触发
    bool dataUpdate;        // 数据更新触发
    int bufferTime;        // 缓冲时间(ms)
    int triggerOptions;    // 触发选项
};
```

## 错误处理

### TC特定错误码
- `0x1001`: TC连接失败
- `0x1002`: 测控数据读取失败
- `0x1003`: 控制操作失败
- `0x1004`: 数据映射错误
- `0x1005`: 缓存溢出

## 使用示例

### TC客户端使用流程
```cpp
// 1. 创建TC客户端
auto tcClient = std::make_unique<CTCClient61850>();

// 2. 加载配置
TCConfig config;
config.LoadFromFile("tc_config.ini");
tcClient->Initialize(config);

// 3. 连接IED
tcClient->ConnectIED("192.168.1.100", 102);

// 4. 配置数据集
TCDataSetConfig dsConfig;
dsConfig.name = "MeasurementDS";
dsConfig.updatePeriod = 100;
tcClient->ConfigureDataSet(dsConfig);

// 5. 订阅数据
tcClient->SubscribeData([](const TCData& data) {
    // 处理接收的测控数据
    ProcessTCData(data);
});

// 6. 执行控制
ControlCmd cmd;
cmd.type = CONTROL_DIRECT;
cmd.value = 1;
tcClient->ExecuteControl("IED1/CTRL/GGIO1$CO$Pos", cmd);
```

## 与标准版本差异

### 功能差异
| 功能 | 标准版 | TC版 |
|-----|--------|------|
| 数据扫描 | 通用扫描 | 优化测控扫描 |
| 控制操作 | 标准控制 | 快速控制 |
| 数据缓存 | 基础缓存 | 智能缓存 |
| 批量处理 | 不支持 | 支持 |

## 注意事项
- TC版本专门优化测控场景
- 注意配置参数调优
- 合理设置缓存大小
- 监控连接状态
- 及时处理异常

## 相关模块
- ZcsPro61850Cli: 标准61850客户端
- ZcsPro61850CliNW: NW版61850客户端
- ZcsProMainFlow: 协议主流程
