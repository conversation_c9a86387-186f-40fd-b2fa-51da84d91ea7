# ZcsProSttpClient - STTP客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsProSttpClient
- **主要功能**: 实现STTP协议客户端，支持与STTP服务器的数据交互
- **设计目的**: 提供高效的STTP客户端实现，支持数据采集、控制命令、文件传输等功能

## 核心文件分析

### 1. ProDataStruct.h - 协议数据结构

#### 数据结构定义
```cpp
// STTP客户端配置
struct STTP_CLIENT_CONFIG {
    string strServerIP;              // 服务器IP
    int nServerPort;                // 服务器端口
    string strClientID;              // 客户端ID
    string strStationID;             // 厂站ID
    int nHeartbeatInterval;          // 心跳间隔
    int nReconnectInterval;          // 重连间隔
    int nTimeout;                    // 超时时间
    bool bEnableCompression;         // 启用压缩
    bool bEnableEncryption;          // 启用加密
};

// STTP连接信息
struct STTP_CONNECTION_INFO {
    SOCKET socket;                   // 套接字
    bool bConnected;                 // 连接状态
    time_t tConnectTime;             // 连接时间
    time_t tLastHeartbeat;           // 最后心跳时间
    int nSendSeq;                    // 发送序号
    int nRecvSeq;                    // 接收序号
    string strSessionID;             // 会话ID
};

// STTP数据点
struct STTP_DATA_POINT {
    int nPointID;                    // 点号
    int nPointType;                  // 点类型
    string strPointName;             // 点名
    double fValue;                   // 值
    int nQuality;                    // 品质
    time_t tTimestamp;               // 时标
    string strUnit;                  // 单位
    string strDescription;           // 描述
};

// STTP命令
struct STTP_COMMAND {
    int nCommandID;                  // 命令ID
    int nCommandType;                // 命令类型
    int nTargetID;                   // 目标ID
    double fValue;                   // 值
    string strParam;                 // 参数
    time_t tExecuteTime;             // 执行时间
};

// STTP文件传输
struct STTP_FILE_TRANSFER {
    int nTransferID;                 // 传输ID
    string strFileName;              // 文件名
    long nFileSize;                  // 文件大小
    int nBlockSize;                  // 块大小
    int nCurrentBlock;               // 当前块
    int nTotalBlocks;                // 总块数
    BYTE* pFileData;                 // 文件数据
    bool bComplete;                  // 是否完成
};
```

### 2. ZcsProSttpClientWay.cpp - STTP客户端实现

#### 类定义
```cpp
class CZcsProSttpClientWay {
private:
    // 配置和接口
    CLIENT_PRO_INTERFACE& m_ProInterface;    // 客户端接口
    STTP_CLIENT_CONFIG m_config;            // 客户端配置
    
    // 连接管理
    STTP_CONNECTION_INFO m_connInfo;        // 连接信息
    CXJLock m_lockConn;                     // 连接锁
    
    // STTP协议处理
    CSttpMsgParser* m_pMsgParser;           // 消息解析器
    CSttpMsgMaker* m_pMsgMaker;             // 消息生成器
    
    // 数据管理
    map<int, STTP_DATA_POINT> m_mapPoints;  // 数据点映射
    CXJLock m_lockPoints;                   // 数据点锁
    
    // 命令队列
    queue<STTP_COMMAND> m_queueCommands;    // 命令队列
    CXJLock m_lockCommands;                 // 命令锁
    
    // 文件传输
    map<int, STTP_FILE_TRANSFER> m_mapTransfers; // 文件传输映射
    CXJLock m_lockTransfers;                // 传输锁
    
    // 消息队列
    queue<STTPMSG> m_sendQueue;             // 发送队列
    queue<STTPMSG> m_recvQueue;             // 接收队列
    CXJLock m_lockSend;                     // 发送锁
    CXJLock m_lockRecv;                     // 接收锁
    
    // 线程管理
    THREAD_HANDLE m_hConnectThread;         // 连接线程
    THREAD_HANDLE m_hRecvThread;            // 接收线程
    THREAD_HANDLE m_hSendThread;            // 发送线程
    THREAD_HANDLE m_hHeartbeatThread;       // 心跳线程
    THREAD_HANDLE m_hProcessThread;         // 处理线程
    bool m_bExit;                           // 退出标志
    
    // 状态统计
    struct STATISTICS {
        long nTotalSent;                    // 总发送
        long nTotalRecv;                    // 总接收
        long nErrorCount;                   // 错误计数
        float fAvgResponseTime;             // 平均响应时间
    } m_statistics;
    
public:
    CZcsProSttpClientWay(CLIENT_PRO_INTERFACE& interface);
    ~CZcsProSttpClientWay();
    
    // 生命周期管理
    int Init();
    int Start();
    int Stop();
    int Release();
    
    // 连接管理
    int Connect();
    int Disconnect();
    int Reconnect();
    bool IsConnected() const;
    
    // 登录认证
    int Login();
    int Logout();
    
    // 数据操作
    int SendRealtimeData(const vector<STTP_DATA_POINT>& points);
    int SendHistoryData(const vector<STTP_DATA_POINT>& points);
    int SendAlarmData(const ALARM_DATA& alarm);
    int SendSOEData(const SOE_DATA& soe);
    
    // 命令处理
    int ExecuteCommand(const STTP_COMMAND& cmd);
    int QueryCommandResult(int cmdID, COMMAND_RESULT& result);
    
    // 文件传输
    int UploadFile(const string& localFile, const string& remoteFile);
    int DownloadFile(const string& remoteFile, const string& localFile);
    int GetFileList(const string& path, vector<FILE_INFO>& files);
    
    // 订阅管理
    int Subscribe(const vector<int>& pointIDs);
    int Unsubscribe(const vector<int>& pointIDs);
    
    // 心跳管理
    int SendHeartbeat();
    int CheckHeartbeat();
    
    // 消息处理
    int ProcessMessage(const STTPMSG& msg);
    int HandleDataMessage(const STTPMSG& msg);
    int HandleCommandMessage(const STTPMSG& msg);
    int HandleFileMessage(const STTPMSG& msg);
    int HandleResponseMessage(const STTPMSG& msg);
    
    // 线程函数
    static THREAD_FUNC ConnectThread(LPVOID pParam);
    static THREAD_FUNC RecvThread(LPVOID pParam);
    static THREAD_FUNC SendThread(LPVOID pParam);
    static THREAD_FUNC HeartbeatThread(LPVOID pParam);
    static THREAD_FUNC ProcessThread(LPVOID pParam);
    
private:
    // 内部实现
    void ConnectLoop();
    void RecvLoop();
    void SendLoop();
    void HeartbeatLoop();
    void ProcessLoop();
    
    // 消息构建
    STTPMSG BuildLoginMsg();
    STTPMSG BuildDataMsg(const vector<STTP_DATA_POINT>& points);
    STTPMSG BuildCommandMsg(const STTP_COMMAND& cmd);
    STTPMSG BuildFileMsg(const FILE_BLOCK& block);
    STTPMSG BuildHeartbeatMsg();
    
    // 数据转换
    int ConvertToSttpData(const UNIFIED_DATA& unified, STTP_DATA& sttp);
    int ConvertFromSttpData(const STTP_DATA& sttp, UNIFIED_DATA& unified);
    
    // 错误处理
    void HandleError(int errorCode, const string& errorMsg);
    void RecordStatistics(const STTPMSG& msg, bool bSend);
};
```

### 3. 核心功能实现

#### 连接和登录
```cpp
int CZcsProSttpClientWay::Connect() {
    // 1. 创建套接字
    m_connInfo.socket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_connInfo.socket == INVALID_SOCKET) {
        WriteLog("Create socket failed", LOG_ERROR);
        return -1;
    }
    
    // 2. 设置套接字选项
    int opt = 1;
    setsockopt(m_connInfo.socket, SOL_SOCKET, SO_REUSEADDR, 
              (char*)&opt, sizeof(opt));
    
    // 设置非阻塞模式
    SetNonBlocking(m_connInfo.socket);
    
    // 3. 连接服务器
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = inet_addr(m_config.strServerIP.c_str());
    serverAddr.sin_port = htons(m_config.nServerPort);
    
    int ret = connect(m_connInfo.socket, 
                     (sockaddr*)&serverAddr, sizeof(serverAddr));
    
    if (ret == SOCKET_ERROR) {
        int error = GetLastError();
        if (error != EINPROGRESS && error != EWOULDBLOCK) {
            CloseSocket(m_connInfo.socket);
            WriteLog("Connect failed", LOG_ERROR);
            return -1;
        }
    }
    
    // 4. 等待连接完成
    if (WaitForConnect(m_connInfo.socket, m_config.nTimeout) != 0) {
        CloseSocket(m_connInfo.socket);
        WriteLog("Connect timeout", LOG_ERROR);
        return -1;
    }
    
    // 5. 更新连接信息
    m_connInfo.bConnected = true;
    m_connInfo.tConnectTime = time(NULL);
    m_connInfo.nSendSeq = 0;
    m_connInfo.nRecvSeq = 0;
    
    // 6. 登录
    return Login();
}

int CZcsProSttpClientWay::Login() {
    // 1. 构建登录消息
    STTPMSG loginMsg = BuildLoginMsg();
    
    // 2. 发送登录消息
    if (SendMessage(loginMsg) != 0) {
        WriteLog("Send login message failed", LOG_ERROR);
        return -1;
    }
    
    // 3. 等待登录响应
    STTPMSG response;
    if (WaitForResponse(loginMsg.header.wRII, response, 
                       m_config.nTimeout) != 0) {
        WriteLog("Login timeout", LOG_ERROR);
        return -1;
    }
    
    // 4. 解析响应
    if (response.header.byType != STTP_LOGIN_RESPONSE) {
        WriteLog("Invalid login response", LOG_ERROR);
        return -1;
    }
    
    // 5. 提取会话ID
    m_connInfo.strSessionID = ExtractSessionID(response);
    
    WriteLog("Login success, session: " + m_connInfo.strSessionID, LOG_INFO);
    return 0;
}
```

#### 数据发送
```cpp
int CZcsProSttpClientWay::SendRealtimeData(
    const vector<STTP_DATA_POINT>& points) {
    
    // 1. 检查连接
    if (!IsConnected()) {
        WriteLog("Not connected", LOG_ERROR);
        return -1;
    }
    
    // 2. 构建数据消息
    STTPMSG dataMsg = BuildDataMsg(points);
    dataMsg.header.byType = STTP_REALTIME_DATA;
    
    // 3. 设置压缩
    if (m_config.bEnableCompression && points.size() > 10) {
        CompressMessage(dataMsg);
    }
    
    // 4. 设置加密
    if (m_config.bEnableEncryption) {
        EncryptMessage(dataMsg);
    }
    
    // 5. 加入发送队列
    {
        CCsLocker lock(&m_lockSend);
        m_sendQueue.push(dataMsg);
    }
    
    // 6. 触发发送
    m_semSend.Post();
    
    // 7. 更新统计
    m_statistics.nTotalSent++;
    
    return 0;
}
```

#### 文件上传
```cpp
int CZcsProSttpClientWay::UploadFile(
    const string& localFile, 
    const string& remoteFile) {
    
    // 1. 打开本地文件
    FILE* fp = fopen(localFile.c_str(), "rb");
    if (!fp) {
        WriteLog("Open file failed: " + localFile, LOG_ERROR);
        return -1;
    }
    
    // 2. 获取文件大小
    fseek(fp, 0, SEEK_END);
    long fileSize = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    
    // 3. 创建传输信息
    STTP_FILE_TRANSFER transfer;
    transfer.nTransferID = GenerateTransferID();
    transfer.strFileName = remoteFile;
    transfer.nFileSize = fileSize;
    transfer.nBlockSize = 8192;
    transfer.nTotalBlocks = (fileSize + transfer.nBlockSize - 1) / 
                           transfer.nBlockSize;
    transfer.nCurrentBlock = 0;
    transfer.bComplete = false;
    
    // 4. 发送文件开始消息
    STTPMSG startMsg;
    startMsg.header.byType = STTP_FILE_START;
    startMsg.header.wRII = GenerateRII();
    // 填充文件信息...
    SendMessage(startMsg);
    
    // 5. 分块发送文件
    BYTE buffer[8192];
    while (!feof(fp)) {
        int readSize = fread(buffer, 1, transfer.nBlockSize, fp);
        if (readSize > 0) {
            // 构建文件块消息
            FILE_BLOCK block;
            block.nTransferID = transfer.nTransferID;
            block.nBlockIndex = transfer.nCurrentBlock;
            block.nBlockSize = readSize;
            block.pData = buffer;
            
            STTPMSG blockMsg = BuildFileMsg(block);
            SendMessage(blockMsg);
            
            transfer.nCurrentBlock++;
            
            // 更新进度
            float progress = (float)transfer.nCurrentBlock / 
                           transfer.nTotalBlocks * 100;
            WriteLog("Upload progress: " + to_string(progress) + "%", LOG_INFO);
        }
    }
    
    // 6. 发送文件结束消息
    STTPMSG endMsg;
    endMsg.header.byType = STTP_FILE_END;
    endMsg.header.wRII = GenerateRII();
    // 填充传输ID...
    SendMessage(endMsg);
    
    // 7. 关闭文件
    fclose(fp);
    
    // 8. 等待确认
    STTPMSG response;
    if (WaitForResponse(endMsg.header.wRII, response, 
                       m_config.nTimeout) == 0) {
        if (response.header.byType == STTP_FILE_COMPLETE) {
            WriteLog("File upload complete: " + remoteFile, LOG_INFO);
            return 0;
        }
    }
    
    return -1;
}
```

#### 心跳管理
```cpp
void CZcsProSttpClientWay::HeartbeatLoop() {
    while (!m_bExit) {
        // 1. 等待心跳间隔
        Sleep(m_config.nHeartbeatInterval * 1000);
        
        // 2. 检查连接状态
        if (!IsConnected()) {
            continue;
        }
        
        // 3. 检查心跳超时
        time_t now = time(NULL);
        if (now - m_connInfo.tLastHeartbeat > m_config.nTimeout) {
            WriteLog("Heartbeat timeout, reconnecting...", LOG_WARN);
            Reconnect();
            continue;
        }
        
        // 4. 发送心跳
        if (SendHeartbeat() == 0) {
            m_connInfo.tLastHeartbeat = now;
        }
    }
}

int CZcsProSttpClientWay::SendHeartbeat() {
    STTPMSG heartbeatMsg = BuildHeartbeatMsg();
    return SendMessage(heartbeatMsg);
}
```

## 配置管理

### STTP客户端配置
```ini
[SttpClient]
# 服务器配置
ServerIP=*************
ServerPort=8080
ClientID=CLIENT001
StationID=STATION001

# 连接配置
ConnectTimeout=30
HeartbeatInterval=30
ReconnectInterval=10
MaxRetryCount=3

# 传输配置
EnableCompression=true
CompressionLevel=6
EnableEncryption=true
EncryptionKey=your-encryption-key

# 缓冲区配置
SendBufferSize=65536
RecvBufferSize=65536
MaxQueueSize=1000

# 文件传输
MaxFileSize=104857600
BlockSize=8192
TransferTimeout=300
```

## 性能优化

### 1. 连接池
- 预建立备用连接
- 连接复用
- 快速切换

### 2. 数据压缩
- 批量数据压缩
- 动态压缩策略
- 压缩级别调整

### 3. 异步处理
- 异步发送接收
- 消息队列缓冲
- 多线程处理

### 4. 批量传输
- 数据点批量发送
- 命令批量执行
- 文件分块传输

## 错误处理

### 1. 连接错误
- 自动重连机制
- 备用服务器切换
- 连接状态监控

### 2. 传输错误
- 消息重传
- 序号校验
- 超时处理

### 3. 数据错误
- CRC校验
- 数据验证
- 错误恢复

## 使用注意事项

1. **服务器配置**: 确保服务器地址和端口正确
2. **认证信息**: 正确配置客户端ID和认证信息
3. **心跳维护**: 合理设置心跳间隔
4. **文件大小**: 注意文件传输的大小限制
5. **并发控制**: 控制并发请求数量
6. **错误处理**: 正确处理各种错误情况
