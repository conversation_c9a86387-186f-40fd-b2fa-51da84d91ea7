# ZcsPro61850Cli - IEC 61850客户端详细分析

## 模块概述
- **模块位置**: module/ZcsServer/pro/ZcsPro61850Cli
- **主要功能**: 实现IEC 61850协议客户端，支持MMS通信
- **设计目的**: 连接IED设备，采集保护和测控数据

## 核心文件分析

### 1. ZcsPro61850Cli.h/cpp - 61850客户端入口

#### 动态库接口
```cpp
extern "C" int Run(CLIENT_PRO_INTERFACE* pProInterface);
```

#### 主要功能实现
```cpp
int Run(CLIENT_PRO_INTERFACE* pProInterface) {
    // 1. 参数验证
    if (NULL == pProInterface) {
        return R_INVALID_PARAMETER;
    }
    
    // 2. 初始化日志
    CMessageLog FlowLog;
    FlowLog.SetLogLevel(pProInterface->log_level);
    FlowLog.SetLogPath(szLogPath);
    FlowLog.SetLogSaveDays(pProInterface->ilog_day);
    FlowLog.Open("CXJPro61850Flow");
    
    // 3. 创建子站发布者
    CXJSubStationPublisher SubStationPublisher(
        pProInterface->iNO,
        pProInterface->pszStationId,
        FlowLog,
        pProInterface->iRunType
    );
    
    // 4. 初始化数据库访问
    CXJDBFacade DBFacade(&FlowLog);
    DBFacade.LoadLib();
    DBFacade.SetConnNum(1);
    
    // 5. 创建数据库功能对象
    CXJCommonDBFunction* m_pDBFunction = 
        new CXJCommonDBFunction(&DBFacade, &FlowLog);
    Zx61850DBFunction* m_pFunction61850 = 
        new Zx61850DBFunction(&DBFacade, &FlowLog);
    
    // 6. 创建并运行61850流程
    CXJPro61850Flow SttpWay(
        *pProInterface,
        SubStationPublisher,
        m_pDBFunction,
        m_pFunction61850,
        FlowLog
    );
    
    // 7. 启动协议运行
    int iRet = SttpWay.Run();
    
    // 8. 清理资源
    delete m_pDBFunction;
    delete m_pFunction61850;
    
    return iRet;
}
```

### 2. ZxPro61850Flow - 61850协议流程控制

#### 类结构（推测）
```cpp
class CXJPro61850Flow {
private:
    CLIENT_PRO_INTERFACE& m_ProInterface;        // 客户端接口
    CXJSubStationPublisher& m_SubStationPublisher; // 子站发布者
    CXJCommonDBFunction* m_pDBFunction;          // 通用数据库功能
    Zx61850DBFunction* m_pFunction61850;         // 61850数据库功能
    CMessageLog& m_LogFile;                      // 日志对象
    
    // MMS客户端
    MmsClient* m_pMmsClient;
    
    // IED连接
    IedConnection m_Connection;
    
    // 数据模型
    IedModel* m_pModel;
    
public:
    // 主运行函数
    int Run();
    
    // 连接管理
    int Connect();
    int Disconnect();
    
    // 服务调用
    int GetServerDirectory();
    int GetLogicalDeviceDirectory();
    int GetDataValues();
    
    // 报告处理
    int EnableReporting();
    int HandleReport(ClientReport* report);
    
    // 控制操作
    int SelectWithValue(const char* reference, MmsValue* value);
    int Operate(const char* reference);
    int Cancel(const char* reference);
};
```

### 3. Zx61850DBFunction - 61850数据库功能

#### 功能接口（推测）
```cpp
class Zx61850DBFunction {
private:
    CXJDBFacade* m_pDBFacade;
    CMessageLog* m_pLogFile;
    
public:
    // IED配置读取
    int GetIEDConfig(const char* iedName, IED_CONFIG& config);
    
    // 数据集配置
    int GetDataSetConfig(const char* dataSetName, DATASET_CONFIG& config);
    
    // 报告配置
    int GetReportConfig(const char* reportName, REPORT_CONFIG& config);
    
    // 点表映射
    int GetPointMapping(const char* reference, POINT_INFO& pointInfo);
    
    // 数据存储
    int SaveMmsData(const MMS_DATA& data);
    int SaveReport(const REPORT_DATA& report);
    int SaveGoose(const GOOSE_DATA& goose);
    int SaveSV(const SV_DATA& sv);
};
```

## 执行流程分析

### 1. 初始化流程
```
Run()
    ├── 参数验证
    ├── 日志系统初始化
    │   ├── 设置日志级别
    │   ├── 设置日志路径
    │   └── 设置保存天数
    ├── 创建发布者
    │   └── 注册到订阅中心
    ├── 数据库初始化
    │   ├── 加载数据库动态库
    │   ├── 设置连接数
    │   └── 连接数据库
    └── 创建61850流程对象
```

### 2. 连接建立流程
```
CXJPro61850Flow::Run()
    ├── 读取IED配置
    │   ├── IP地址
    │   ├── 端口号
    │   └── 认证信息
    ├── 创建MMS连接
    │   ├── IedConnection_create()
    │   └── IedConnection_connect()
    ├── 获取服务器目录
    │   └── IedConnection_getServerDirectory()
    ├── 读取数据模型
    │   ├── 逻辑设备
    │   ├── 逻辑节点
    │   └── 数据对象
    └── 订阅报告
        ├── 创建报告控制块
        └── 启用报告
```

### 3. 数据采集流程
```
数据采集循环
    ├── 定期读取
    │   ├── 遥测数据
    │   ├── 遥信数据
    │   └── 状态信息
    ├── 报告接收
    │   ├── 解析报告
    │   ├── 提取数据
    │   └── 更新缓存
    ├── GOOSE接收
    │   ├── 解析GOOSE
    │   └── 处理事件
    └── 数据处理
        ├── 格式转换
        ├── 质量判断
        └── 时标处理
```

### 4. 数据转换流程
```
MMS数据 → STTP数据
    ├── 解析MMS值
    │   ├── 类型识别
    │   ├── 值提取
    │   └── 质量提取
    ├── 映射转换
    │   ├── 查找点表
    │   ├── 地址映射
    │   └── 类型转换
    └── 生成STTP消息
        ├── 填充数据结构
        ├── 添加时标
        └── 设置质量码
```

### 5. 控制操作流程
```
接收控制命令
    ├── 命令验证
    │   ├── 权限检查
    │   ├── 参数检查
    │   └── 状态检查
    ├── 选择操作
    │   └── SelectWithValue()
    ├── 执行操作
    │   └── Operate()
    └── 结果反馈
        ├── 成功确认
        └── 失败原因
```

## 关键数据结构

### 1. IED配置
```cpp
struct IED_CONFIG {
    char iedName[64];          // IED名称
    char ipAddress[16];        // IP地址
    int port;                  // 端口号
    char apName[64];          // 访问点名称
    int authType;             // 认证类型
    char username[32];        // 用户名
    char password[32];        // 密码
};
```

### 2. 数据点信息
```cpp
struct POINT_INFO {
    int pointID;              // 点号
    char reference[256];      // 61850引用
    int dataType;             // 数据类型
    int fcType;               // 功能约束
    char ldName[64];          // 逻辑设备名
    char lnName[64];          // 逻辑节点名
    char doName[64];          // 数据对象名
    char daName[64];          // 数据属性名
};
```

### 3. 报告配置
```cpp
struct REPORT_CONFIG {
    char rptID[64];           // 报告ID
    char dataSet[128];        // 数据集
    int bufTime;              // 缓冲时间
    int intgPd;               // 完整性周期
    bool gi;                  // 总召使能
    int trgOps;               // 触发选项
};
```

## 支持的文件格式

### 1. SCL文件支持
- **ICD文件**: IED能力描述
- **SCD文件**: 变电站配置描述
- **CID文件**: 配置的IED描述

### 2. 压缩文件处理
```cpp
// 使用unzip库处理压缩的SCL文件
#include "unzip.h"

int ExtractSCLFile(const char* zipFile) {
    unzFile uf = unzOpen(zipFile);
    if (uf == NULL) return -1;
    
    // 解压SCL文件
    unzLocateFile(uf, "*.scd", 0);
    unzOpenCurrentFile(uf);
    // ... 读取文件内容
    unzCloseCurrentFile(uf);
    unzClose(uf);
    
    return 0;
}
```

## MMS服务实现

### 1. 读服务
```cpp
MmsValue* ReadValue(const char* reference) {
    IedClientError error;
    MmsValue* value = IedConnection_readObject(
        m_Connection, 
        &error, 
        reference
    );
    
    if (error != IED_ERROR_OK) {
        // 错误处理
        return NULL;
    }
    
    return value;
}
```

### 2. 写服务
```cpp
bool WriteValue(const char* reference, MmsValue* value) {
    IedClientError error;
    IedConnection_writeObject(
        m_Connection,
        &error,
        reference,
        value
    );
    
    return (error == IED_ERROR_OK);
}
```

### 3. 报告服务
```cpp
void ReportHandler(void* parameter, ClientReport report) {
    // 处理接收到的报告
    MmsValue* dataSetValues = ClientReport_getDataSetValues(report);
    
    // 遍历数据集值
    int valueCount = MmsValue_getArraySize(dataSetValues);
    for (int i = 0; i < valueCount; i++) {
        MmsValue* value = MmsValue_getElement(dataSetValues, i);
        // 处理单个值
    }
}
```

## 性能优化

### 1. 连接池管理
- 复用MMS连接
- 连接超时重连
- 心跳保活

### 2. 数据缓存
- 缓存数据模型
- 缓存点表映射
- 缓存最新值

### 3. 批量操作
- 批量读取数据
- 批量报告处理
- 批量数据存储

## 错误处理

### 1. 连接错误
```cpp
enum ConnectionError {
    CONN_SUCCESS = 0,
    CONN_TIMEOUT = -1,
    CONN_REFUSED = -2,
    CONN_AUTH_FAILED = -3,
    CONN_LOST = -4
};
```

### 2. 服务错误
```cpp
enum ServiceError {
    SVC_SUCCESS = 0,
    SVC_ACCESS_DENIED = -1,
    SVC_OBJECT_NOT_EXIST = -2,
    SVC_TYPE_CONFLICT = -3,
    SVC_TEMPORARILY_UNAVAILABLE = -4
};
```

## 配置示例

### 61850客户端配置
```ini
[IED]
Name=PROT_IED1
IP=*************
Port=102
APName=S1
AuthType=1
Username=admin
Password=admin123

[DataModel]
UseSCL=true
SCLFile=station.scd

[Report]
BufferTime=50
IntegrityPeriod=60000
GeneralInterrogation=true

[Connection]
ConnectTimeout=30
ReadTimeout=10
MaxRetry=3
HeartbeatInterval=30
```

## 使用注意事项

1. **SCL文件**: 确保SCL文件与实际IED配置一致
2. **时钟同步**: 保证客户端与IED时钟同步
3. **报告缓冲**: 合理设置报告缓冲时间
4. **连接数量**: 注意IED支持的最大连接数
5. **性能监控**: 监控MMS通信性能指标
