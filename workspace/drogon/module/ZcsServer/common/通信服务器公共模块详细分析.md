# ZcsServer/common - 通信服务器公共模块详细分析

## 模块概述
- **模块名称**: ZcsServer/common
- **主要功能**: 提供通信服务器各组件的公共定义、接口和基础功能
- **创建时间**: 2008-2015
- **设计目的**: 为ZcsServer各子模块提供统一的接口定义和公共功能

## 文件结构和功能

```
ZcsServer/common/
├── 核心定义文件
│   ├── CommuDef.h                    # 通信相关常量和结构定义
│   └── CsLocker.h                    # 线程锁定义
│
├── API接口文件
│   ├── ZxProtocolAPI.h               # 协议接口定义
│   ├── ZxSubscribeCenterAPI.h        # 订阅中心接口
│   ├── ZxDataHandlerAPI.h            # 数据处理接口
│   ├── ZxServerManagerAPI.h          # 服务器管理接口
│   ├── ZxClientManagerAPI.h          # 客户端管理接口
│   ├── ZxProMsgBusServerAPI.h        # 消息总线服务接口
│   ├── ZxProSttpSCServerAPI.h        # STTP短连接服务接口
│   └── ZxOscilloAnalystAPI.h         # 示波分析接口
│
├── 观察者模式实现
│   ├── ZxObserver.h/.cpp             # 观察者基类
│   ├── ZxPublisher.h/.cpp            # 发布者基类
│   └── ZxMsgCaster.h/.cpp            # 消息广播器
│
├── 工具类
│   ├── SttpMsgProductor.h/.cpp       # STTP消息生成器
│   └── ZxSrvOnlineManagerWrapper.h/.cpp  # 在线管理器封装
│
└── 更新历史
    └── ZxCommuServer_common_update_history.cpp
```

## 文件详细分析

### 1. CommuDef.h - 通信定义文件

#### 装置通信状态定义
```cpp
#define DEVICE_ON_COMMU_STATUS     0    // 正常状态
#define DEVICE_OFF_COMMU_STATUS    1    // 停运状态
#define DEVICE_UNKNOW_COMMU_STATUS 2    // 未知状态
```

#### 装置运行状态定义
```cpp
#define DEVICE_UNKNOW_STATUS  -1    // 未知状态
#define DEVICE_ON_STATUS      0     // 运行状态
#define DEVICE_CHECK_STATUS   1     // 检修状态
#define DEVICE_OFF_STATUS     2     // 停用状态
#define DEVICE_DEBUG_STATUS   3     // 调试状态
#define DEVICE_CODE_STATUS    4     // 对码表状态
```

#### 数据分类定义
```cpp
#define IPOFAS_ONE_CLASS_DATA     0      // 一类数据（重要）
#define IPOFAS_TWO_CLASS_DATA     1      // 二类数据（次要）
#define IPOFAS_THREE_CLASS_DATA   2      // 三类数据（一般）
#define IPOFAS_UNKNOW_CLASS_DATA  255    // 未知数据
```

#### 厂站通信状态常量
```cpp
const int STATION_COMMU_STATUS_UNK  = -1;   // 未知
const int STATION_COMMU_STATUS_OFF  = 0;    // 断开
const int STATION_COMMU_STATUS_ON   = 1;    // 正常
const int STATION_COMMU_STATUS_RESERVE = 2; // 备用
```

#### 厂站通信故障原因
```cpp
const int STATION_COMMU_REASON_NOFAULT                = 0;  // 正常
const int STATION_COMMU_REASON_OFFLINE                = 1;  // 备用
const int STATION_COMMU_REASON_CHANNEL_NOREACHPEER    = 2;  // 无法与子站通信
const int STATION_COMMU_REASON_CHANNEL_NOREACHGATEWAY = 3;  // 无法与网关通信
const int STATION_COMMU_REASON_SOFTWARE_CONNECTFAILE  = 4;  // 无法建立连接
const int STATION_COMMU_REASON_SOFTWARE_LINKFAILE     = 5;  // 初始化链路失败
const int STATION_COMMU_REASON_CHANNEL_NOREACHPORT    = 6;  // 无法与端口通信
```

#### CHN_LINK_STATUS - 通道链路状态
```cpp
typedef struct _CHN_LINK_STATUS {
    int nChNo;        // 通道号
    int nStatus;      // 状态(0-断开 1-正常 2-备用)
    int nReason;      // 状态变化原因
    time_t nTime;     // 状态变化时间
    int nReserve;     // 备用
    string strReserve;// 备用
}
```

#### STATION_LINK_STATUS - 厂站链路状态
```cpp
typedef struct _STATION_LINK_STATUS {
    CHN_LINK_STATUS stationStatus;           // 厂站最终合成状态
    vector<CHN_LINK_STATUS> chnStatulList;   // 厂站所属通道状态列表
    string strReserve;                       // 备用
}
```

#### 状态变化回调函数
```cpp
typedef void (*PFUN_STATION_LINK_CHANGE)(void* pRegObj, STATION_LINK_STATUS& link_status);
```

#### 动态库名称定义
```cpp
#ifdef OS_LINUX
#define LIB_MSGMGR_NAME          "libZcsMsgMngr.so"       // 报文中心
#define LIB_DATAHANDLER_NAME     "libZcsDataHdl.so"       // 数据处理中心
#define LIB_SUBSCRIBECENTER_NAME "libZcsSsbMngr.so"       // 订阅中心
#define LIB_CLIENTMGR_NAME       "libZcsCliMngr.so"       // 客户端管理器
#define LIB_SERVERMGR_NAME       "libZcsSrvMngr.so"       // 服务端管理器
#define LIB_WEBPROVIDER_NAME     "libZcsWebTrans.so"      // Web发布
#define LIB_STTPSCPRO_NAME       "libXJProSttpSCServer.so"// STTP短连接
#define LIB_MSGBUSPRO_NAME       "libZcsD5kMsgItf.so"     // D5K消息总线
#define LIB_STTPBUSPRO_NAME      "libZcsSbItfCli.so"      // STTP总线
#endif
```

#### STTP_PUBLISH_MSG - STTP发布消息
```cpp
typedef struct _STTP_PUBLISH_MSG {
    char observerId[13];    // 观察者ID
    STTPMSG msg;           // STTP报文数据
}
```

### 2. ZxProtocolAPI.h - 协议接口定义

#### 客户端类型定义
```cpp
#define CLIENT_UNKNOW  0    // 未知客户端
#define CLIENT_STTP    1    // STTP客户端
#define CLIENT_103     2    // 103客户端
```

#### 规约库模式定义
```cpp
#define UNKNOW_MODE  0    // 未知模式
#define CLIENT_MODE  1    // 客户端模式
#define SERVER_MODE  2    // 服务器端模式
```

#### 规约类型定义
```cpp
#define PRO_TYPE_STTP  0    // STTP协议
#define PRO_TYPE_103   1    // IEC 103协议
#define PRO_TYPE_104   2    // IEC 104协议
#define PRO_TYPE_101   3    // IEC 101协议
```

#### 站类型定义
```cpp
#define STATION_TYPE_SUB   1    // 子站
#define STATION_TYPE_MAIN  2    // 主站
```

#### SERVER_PRO_INTERFACE - 服务端规约接口
```cpp
struct SERVER_PRO_INTERFACE {
    int iNO;                              // 会话索引
    bool* p_bExit;                        // 退出标志
    char* pIp;                            // 客户端IP
    char chClientID[13];                  // 客户端标识
    void* pNetStream;                     // Socket连接
    void* pCom;                          // 串口指针
    char log_path[FILE_NAME_MAX_LEN];    // 日志路径
    int log_level;                       // 日志级别
    bool bRecordMsg;                     // 是否记录报文
    int iServerRole;                     // 服务器角色
    int iStationType;                    // 站类型标志
    int iDevNum;                         // 装置个数
    char (*pDevList)[13];                // 装置列表
    int ilog_day;                        // 日志保存天数
    int iReserved;                       // 链路地址
    bool biReserved;                     // 网络异常时是否保存
    char szReserved[FILE_NAME_MAX_LEN]; // 备用
};
```

#### CLIENT_PRO_INTERFACE - 客户端规约接口
```cpp
struct CLIENT_PRO_INTERFACE {
    int iNO;                                    // 会话索引
    bool* p_bExit;                              // 退出标志
    char* pszStationId;                         // 对端子站ID
    char* pszClientName;                        // 对端子站名称
    STRUCT_DESTINATION_SERVER* pDestination;    // 目标服务器信息
    char log_path[FILE_NAME_MAX_LEN];          // 日志路径
    int log_level;                             // 日志级别
    bool bRecordMsg;                           // 是否记录报文
    int iRunType;                              // 运行类型
    int ilog_day;                              // 日志保存天数
    int iReserved;                             // 链路地址
    bool biReserved;                           // 备用
    char szReserved[FILE_NAME_MAX_LEN];        // 网关列表
    int nAutoUpCylSstnStatus;                  // 厂站状态上送周期
    CGetDataType* pGetDataType;                // 数据类型匹配对象
};
```

### 3. ZxSubscribeCenterAPI.h - 订阅中心接口

#### STRUCT_XJSUBSCRIBECENTER - 订阅中心配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iServerRole;                     // 服务器角色
    int iCallAllTime;                    // 定时总招间隔(分钟)
    int iCheckTime;                      // 对时间隔(分钟)
    int iLogDay;                         // 日志保存天数
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
} STRUCT_XJSUBSCRIBECENTER;
```

#### 函数定义
```cpp
typedef int (*PFUNSTARTSUBSCRIBECENTER)(const STRUCT_XJSUBSCRIBECENTER&);
typedef int (*PFUNSTOPSUBSCRIBECENTER)();
```

### 4. ZxDataHandlerAPI.h - 数据处理接口

#### STRUCT_XJDATAHANDLER - 数据处理配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iServerRole;                     // 服务器角色
    bool bRecordMsg;                     // 是否记录报文
    int iLogDay;                         // 日志保存天数
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
} STRUCT_XJDATAHANDLER;
```

#### 函数定义
```cpp
typedef int (*PFUNSTARTDATAHANDLER)(const STRUCT_XJDATAHANDLER&);
typedef int (*PFUNSTOPDATAHANDLER)();
typedef void (*PFUNINSERTDATA)(const STTPMSG&, bool);
```

### 5. ZxServerManagerAPI.h - 服务器管理接口

#### STRUCT_XJSERVERMANAGER - 服务器管理配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iServerRole;                     // 服务器角色
    bool bRecordMsg;                     // 是否记录报文
    int iLogDay;                         // 日志保存天数
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
    vector<string>* pvMyStn;             // 本进程管理的厂站列表
} STRUCT_XJSERVERMANAGER;
```

### 6. 观察者模式实现

#### ZxObserver.h - 观察者基类

##### 类定义
```cpp
class CXJObserver {
protected:
    STTP_MESSAGE_QUEUE m_queueResultMsg;    // 结果队列
    STTP_MESSAGE_QUEUE m_queueAutoUpMsg;    // 自动上送队列
    CXJLock m_lockResultQueue;              // 结果队列锁
    CXJLock m_lockAutoUpQueue;              // 自动上送队列锁
    CLogFile& m_LogFile;                    // 日志文件
    
private:
    XJHANDLE m_hLibXJSubscribeCenter;       // 订阅中心库句柄
    POSTCOMMANDTOSECDEV m_pFunPostCommandToSecDev;      // 发送到设备函数
    POSTCOMMANDTOSTATION m_pFunPostCommandToStation;    // 发送到厂站函数
    ATTACHOBSERVER m_pFunAttachObserver;                // 连接观察者函数
    DETACHOBSERVER m_pFunDetachObserver;                // 分离观察者函数
};
```

##### 核心虚函数
```cpp
// 发布自动上送报文
virtual bool PostAutoUpMessage(const STTPMSG& pMsg) = 0;

// 发布结果报文
virtual bool PostResultMessage(const STTPMSG& pMsg) = 0;

// 写日志
virtual void WriteLog(const char* pLog, int nLevel) = 0;
```

##### 命令发送函数
```cpp
// 发送命令到二次设备
bool PostCommandToSecDev(const char* pPtId, STTP_PUBLISH_MSG& pMsg);

// 发送命令到厂站
bool PostCommandToStation(const char* pStationId, STTP_PUBLISH_MSG& pMsg);

// 连接观察者
bool AttachObserver(const char* pObserverId, CXJObserver* pObserver, const char* pClinetId);

// 分离观察者
bool DetachObserver(const char* pObserverId, const char* pClinetId);
```

#### ZxPublisher.h - 发布者基类

##### 类定义
```cpp
class CXJPublisher {
protected:
    bool m_bStarted;                                    // 启动标志
    bool m_bExit;                                       // 退出标志
    CXJDBFacade* m_pDBFacade;                          // 数据库接口
    CLogFile& m_LogFile;                               // 日志文件
    CLIENT_TYPE_SUBSCRIBE_VIEW m_viewClinetType;        // 客户端订阅视图
    DEV_SUBSCRIBE_VIEW m_viewDevOfClient;               // 设备-客户端视图
    DEV_SUBSCRIBE_VIEW m_viewDevNotForClient;           // 设备排除视图
    STTPTYPE_CLIENT_SUBSCRIBE_VIEW m_viewTypeForClient; // 类型-客户端视图
    set<string> m_setSecDev;                           // 设备列表
    int m_nServerMdl;                                  // 服务器模式
    bool m_bIsFilterSignal;                            // 是否过滤信号
    
private:
    XJHANDLE m_hLibXJSubscribeCenter;                  // 订阅中心库句柄
    PFUNREGISTERPUBLISHER m_pFunRegisterPublisher;     // 注册发布者函数
    PFUNDISCARDPUBLISHER m_pFunDiscardPublisher;       // 注销发布者函数
    PFUNREGISTERSECDEV m_pFunRegisterSecDev;           // 注册设备函数
    PFUNDISCARDSECDEV m_pFunDiscardSecDev;             // 注销设备函数
    PFUNFINDOBSERVER m_pFunFindObserver;               // 查找观察者函数
    PFUNPOSTAUTOUPTOCLIENT m_pFunPostAutoUpToClient;   // 发送到客户端函数
    XJHANDLE m_hLibXJDataHandle;                       // 数据处理库句柄
    PFUNINSERTDATA m_pFunInsertData;                   // 插入数据函数
};
```

##### 核心虚函数
```cpp
virtual bool StartService() = 0;
virtual bool EndService() = 0;
virtual bool PostAutoUploadMsg(const STTP_PUBLISH_MSG& pSttpMsg) = 0;
virtual bool PostCommand(const STTP_PUBLISH_MSG& pSttpMsg) = 0;
virtual bool PostResultMsg(const STTP_PUBLISH_MSG& pSttpMsg) = 0;
virtual bool GetCommand(STTP_PUBLISH_MSG& pSttpMsg) = 0;
virtual void WriteLog(const char* pLog, int nLevel) = 0;
```

##### 消息分发机制
```cpp
// 按信息类型分发厂站自动上送
virtual void DispatchStationAutoUpByInfoOrder(const STTPMSG& pMsg, 
                                             UINT pMsgID, 
                                             const string& pStationId);

// 按信息类型分发设备自动上送
virtual void DispatchSecDevAutoUpByInfoOrder(const STTPMSG& pMsg, 
                                            UINT pMsgID, 
                                            const string& pDevId);

// 分发自动上送到客户端
virtual void DispatchAutoUpToClient(const STTPMSG& pMsg,
                                   const string& pClientId,
                                   const string& pDevId);
```

##### 过滤机制
```cpp
// 通过调试状态过滤
bool FilterInfoByIsDebug(const char* pPtId, const char* pClientId);

// 通过信息种类过滤
bool FilterInfoByCategory(UINT pMsgID, STTPMSG& pMsg, 
                         STRUCT_STTP_TYPE_ORDER& pTypeOrder, 
                         const char* pClientId);

// 通过信息分类过滤
bool FilterInfoByInfoClass(STTPMSG& pMsg, STRUCT_STTP_TYPE_ORDER& pTypeOrder);

// 通过信息点配置过滤
bool FilterInfoByPoint(STTPMSG& pMsg, const char* pClientId);
```

### 7. SttpMsgProductor - STTP消息生成器

#### 主要功能
生成各类STTP协议报文，支持30多种不同类型的报文格式。

#### 常用报文生成函数

##### 通信状态类
```cpp
// 20144 - 厂站通讯状态变化上载通知
static int FillSttp20144Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszStationId, bool bIsLink, 
                           BYTE ErrCode, int nChnlType);

// 20146 - 厂站通讯状态下载通知
static int FillSttp20146Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszStationId, bool bIsLink, 
                           int nChnlType, int Reason);

// 20003 - 保护通讯状态下载要求
static int FillSttp20003Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszEquipId);
```

##### 定值管理类
```cpp
// 20106 - 召唤定值区号要求
static int FillSttp20106Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszEquipId, BYTE byCpu);

// 20051 - 保护定值修改要求
static int FillSttp20051Msg(STTPMSG* pMsg, unsigned int uRII,
                           char* pszEquipId, BYTE byZone, BYTE byCpu, 
                           BYTE bySource, char** ppId, char** ppVal, 
                           unsigned int count);

// 20055 - 保护定值切换执行要求
static int FillSttp20055Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszEquipId, BYTE byZone, BYTE byCpu);
```

##### 文件传输类
```cpp
// 00205 - 文件CS下载要求
static int FillSttp00205Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszSrcPathFile, char* pszDstPathFile, 
                           char* pszIp);

// 20025 - 录波文件列表下载要求
static int FillSttp20025Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszEquipId, BYTE bySource, 
                           SYSTIME stStart, SYSTIME stEnd);

// 20042 - 录波文件下载要求
static int FillSttp20042Msg(STTPMSG* pMsg, unsigned int uRII, 
                           char* pszEquipId, char* pszFileName, 
                           BYTE bySource, unsigned int nOffset, 
                           unsigned int nFileNo);
```

##### 通用功能类
```cpp
// 00101 - 登录报文
static int FillSttp00101Msg(STTPMSG* pMsg, unsigned int uRII,
                           char* pszModelName, char* pszUserName,
                           char* pszPassWord, char* pszIp1, short uPort1, 
                           char* pszIp2, short uPort2,
                           vector<string> vEquipIdList);

// 20069 - 控制报文通用返回通知
static int FillSttp20069Msg(STTPMSG* pMsg, unsigned int uRII, 
                           const char* pszEquipid, int iCpu, 
                           int iResult, int iCmdMsgId);

// 00901 - 结果确认通知
static int FillSttp00901Msg(STTPMSG* pMsg, unsigned int uRII,
                           int MsgId, int sttp_res, char* pVarData);
```

##### 工具函数
```cpp
// 设置报文RII值
static bool SetSttpMsgRII(STTPMSG* pSttpMsg, BYTE byRII);
static bool ReSetSttpMsgRII(STTPMSG* pSttpMsg, UINT byRII);

// 生成STTP时间格式
static int MakeSttp12ByteTime(char* pszTimeStr, SYSTIME stTime);

// 转换时间格式
static void ConvertTimeTo103(char* src, int nLength, char* dis, int nLen);
static void ConvertTimeToSttp(char* src, int nLength, char* dis, int nLen);
```

### 8. ZxMsgCaster - 消息广播器

#### 类定义
```cpp
class CXJMsgCaster {
private:
    bool m_InitSuccess;                    // 初始化成功标志
    string m_strLocalIP;                   // 本地IP
    int m_iLocalPort;                      // 本地端口
    string m_strRemoteIP;                  // 远程IP
    int m_iRemotePort;                     // 远程端口
    string m_strCastIP;                    // 广播IP
    int m_iCastPort;                       // 广播端口
    string m_strModelName;                 // 模块名称
    string m_strStationID;                 // 厂站ID
    int m_iCommuMode;                      // 通信模式
    CXJMsgMonitorWrapper m_iMsgMonitor;    // 消息监控器
};
```

#### 核心功能
```cpp
// 启动和停止
virtual bool Start(string& pError);
virtual bool End();

// 广播消息信息
virtual int CastMsgInfo(int pType, int pLevel, int pWay, 
                       const string& pMsg, const string& pDesc, 
                       string& pError);

// 广播103消息
virtual int Cast103MsgInfo(int pType, DWORD pSerialNO, int pWay, 
                          const MESSAGE103& pMsg, const string& pDesc, 
                          string& pError);

// 转换消息格式
virtual void ConvertBitMsgToHexString(const vector<BYTE>& pMsg, 
                                     string& pHexString);
```

#### 单例获取
```cpp
extern CXJMsgCaster* GetInstanceMsgCaster(string pStationID);
```

### 9. CsLocker - 栈式互斥锁

#### 设计理念
使用RAII（资源获取即初始化）原则，确保锁的正确释放。

#### 实现
```cpp
class CCsLocker {
public:
    CCsLocker(CLock* cs) : m_pcs(cs) { 
        m_pcs->lock();     // 构造时加锁
    }
    
    virtual ~CCsLocker() { 
        m_pcs->unlock();   // 析构时解锁
    }
    
private:
    CLock* m_pcs;
};
```

#### 使用示例
```cpp
void SafeOperation() {
    CCsLocker locker(&m_lock);  // 自动加锁
    
    // 执行需要同步的操作
    // ...
    
    // 函数结束时自动解锁，即使抛出异常也能保证解锁
}
```

### 10. ZxClientManagerAPI.h - 客户端管理接口

#### STRUCT_XJCLIENTMANAGER - 客户端管理配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iServerRole;                     // 服务器角色
    bool bRecordMsg;                     // 是否记录报文
    int iLogDay;                         // 日志保存天数
    bool bReserved;                      // 保留
    int iReserved;                       // 保留(已被占用作为工作区号)
    char reservedStr[256];               // 保留
    vector<string>* pvMyStn;             // 本进程管理的厂站列表
} STRUCT_XJCLIENTMANAGER;
```

#### 函数定义
```cpp
typedef int (*PFUNSTARTCLIENTMGR)(const STRUCT_XJCLIENTMANAGER&);
typedef int (*PFUNSTOPCLIENTMGR)();
```

### 11. ZxProMsgBusServerAPI.h - 消息总线服务接口

#### STRUCT_XJPROMSGBUSSERVER - 消息总线配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iLogDay;                         // 日志保存天数
    bool bRecordMsg;                     // 是否记录报文
    int iChannelUp;                      // 上行通道号
    int iChannelDown;                    // 下行通道号
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
    vector<string>* pvMyStn;             // 本进程管理的厂站列表
} STRUCT_XJPROMSGBUSSERVER;
```

#### 功能说明
- 与D5K总线交互的业务库接口
- 支持上下行通道配置
- 支持多进程模式下的厂站管理

### 12. ZxProSttpSCServerAPI.h - STTP短连接服务接口

#### STRUCT_XJPROSTTPSCSERVER - STTP短连接配置
```cpp
typedef struct {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iListenPort;                     // 侦听端口
    bool bRecordMsg;                     // 是否记录报文
    char szLocalIP[19];                  // 本地IP
    int iLogDay;                         // 日志保存天数
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
} STRUCT_XJPROSTTPSCSERVER;
```

#### 功能说明
- STTP短连接服务端协议实现
- 支持TCP短连接模式
- 适用于间歇性数据传输场景

### 13. ZxOscilloAnalystAPI.h - 示波分析接口

#### tpXJOsilloAnalyst - 示波分析配置
```cpp
typedef struct _STRUCT_XJOSILLOANALYST {
    char szLog_path[FILE_NAME_MAX_LEN];  // 日志路径
    int iLog_level;                      // 日志级别
    int iLogDay;                         // 日志记录天数
    int iServerRole;                     // 服务器角色
    int iModelType;                      // 外部模块类型
    bool bReserved;                      // 保留
    int iReserved;                       // 保留
    char reservedStr[256];               // 保留
} tpXJOsilloAnalyst;
```

#### 功能说明
- 用于录波文件的分析处理
- 支持多种录波文件格式
- 可与外部分析模块对接

### 14. ZxSrvOnlineManagerWrapper - 在线管理器封装

#### 类定义
```cpp
class CXJSrvOnlineManager {
private:
    XJHANDLE m_hLibSrvOnlineManager;                        // 库句柄
    PFUNSTARTSRVONLINEMANAGER m_pFunStartSrvOnlineManager; // 启动函数
    PFUNSTOPSRVONLINEMANAGER m_pFunStopSrvOnlineManager;   // 停止函数
    PFUNREGISTERSRVSWITCHCALLBACK m_pFunRegisterSrvSwitchCallback; // 注册回调
    PFUNSETSTATIONLOADSTATUS m_pFunSetStationLoadStatus;   // 设置厂站状态
};
```

#### 核心功能
```cpp
// 初始化库
virtual bool InitLibrary(char* pErrorMsg);

// 启动服务器在线管理器
int StartSrvOnLineManager(const stXJSrvOnlineManager& pManager, char* pErrorMsg);

// 停止服务器在线管理器
int StopSrvOnLineManager(char* pErrorMsg);

// 注册服务器切换回调
int RegisterSrvSwitchCallback(PFUNONSRVSWITCH pOnSrvSwitch, void* pParam, char* pErrorMsg);

// 设置子站加载状态
int SetStationLoadStatus(stXJSubstationLoadStatus& PStatus);
```

#### 动态库定义
```cpp
#ifdef OS_LINUX
#define LIB_XJSRVONLINEMANAGER_NAME "libZclMltSrvOnlineMngr.so"
#endif

#ifdef OS_WINDOWS
#define LIB_XJSRVONLINEMANAGER_NAME "ZclMltSrvOnlineMngr.dll"
#endif
```

## 工具函数

### 数据转换函数
```cpp
// 整数转字符串
char* Itoa(int value, char* str, int radix);

// 字符串反转
char* Strrev(char* szT);

// 字节数组转double
double ArrayToDouble(char* szByte, int iLength);

// double转字节数组
void DoubleToArray(double fDouble, char* szByte, int iLength, 
                  unsigned int nDecimal = 3);

// 字节数组转整数
UINT ArrayToInt(char* byte, int nCount);

// 整数转字节数组
void IntToArray(UINT l, char* ch, int length);

// 时间格式转换
void ConvertTimeTo103(char* src, int nLength, char* dis, int nLen);
time_t ConvertTimeStringToTime_T(CXJString strTime, int nLen);
void ConvertTime_tTo103(time_t* pTimeT, char* dis);
void ConvertTimeToSttp(char* src, int nLength, char* dis, int nLen);
```

## 设计模式

### 1. 观察者模式
- 用于消息分发和事件通知
- 实现解耦的消息传递机制
- 支持多对多的通信关系

### 2. 工厂模式
- 动态库加载和实例创建
- 统一的接口创建方式
- 支持运行时配置

### 3. 包装器模式
- 封装复杂的动态库接口
- 提供简化的访问方式
- 处理版本兼容性

## 使用示例

### 1. 启动服务器管理器
```cpp
// 加载动态库
XJHANDLE hLib = OS_LoadLibrary(LIB_SERVERMGR_NAME);

// 获取启动函数
PFUNSTARTSERVERMGR pStartFunc = 
    (PFUNSTARTSERVERMGR)OS_GetProcAddress(hLib, "StartServerMgr");

// 配置参数
STRUCT_XJSERVERMANAGER config;
strcpy(config.szLog_path, "/var/log/server");
config.iLog_level = 3;
config.iServerRole = 0;  // 主站
config.bRecordMsg = true;
config.iLogDay = 30;

// 启动服务
int result = pStartFunc(config);
```

### 2. 使用观察者模式
```cpp
// 创建发布者
ZxPublisher* pPublisher = new ZxPublisher();

// 创建观察者
class MyObserver : public ZxObserver {
public:
    virtual void Update(const STTPMSG& msg) {
        // 处理消息
        ProcessMessage(msg);
    }
};

MyObserver* pObserver = new MyObserver();

// 注册观察者
pPublisher->Attach(pObserver);

// 发布消息
STTPMSG msg;
// 填充消息...
pPublisher->Notify(msg);
```

### 3. 厂站状态监控
```cpp
// 注册状态变化回调
void OnStationStatusChange(void* pObj, STATION_LINK_STATUS& status) {
    printf("厂站状态变化: 通道=%d, 状态=%d, 原因=%d\n",
           status.stationStatus.nChNo,
           status.stationStatus.nStatus,
           status.stationStatus.nReason);
    
    // 处理各通道状态
    for (auto& chn : status.chnStatulList) {
        HandleChannelStatus(chn);
    }
}

// 注册回调
RegisterStationLinkChangeCallback(OnStationStatusChange, this);
```

## 关键常量汇总

### 队列大小
```cpp
const int ONE_FRONT_MAX_DEV_NUM = 200;     // 单个前置最大设备数
const int XJ_MAX_QUEUE_SIZE = 256;         // 命令队列最大尺寸
const int MAX_RESULT_QUEUE_SIZE = 20000;   // 结果队列最大尺寸
```

### 日志级别
```cpp
0 - 不记录日志
1 - 错误日志
2 - 警告日志
3 - 跟踪日志
```

### 服务器角色
```cpp
0 - 主站通讯服务器
1 - 子站通讯服务器
```

## 设计特点

### 1. 模块化设计
- 清晰的接口定义
- 独立的功能模块
- 低耦合高内聚

### 2. 跨平台支持
- Windows/Linux兼容
- 统一的接口封装
- 平台相关代码隔离

### 3. 动态加载
- 支持运行时加载
- 灵活的配置管理
- 版本兼容处理

### 4. 性能优化
- 合理的队列大小
- 高效的消息分发
- 批量处理支持

## 注意事项

1. **动态库管理**
   - 正确设置库路径
   - 处理加载失败情况
   - 及时释放资源

2. **线程安全**
   - 使用CsLocker进行同步
   - 避免死锁
   - 注意资源竞争

3. **内存管理**
   - 正确释放动态分配内存
   - 避免内存泄漏
   - 使用智能指针

4. **错误处理**
   - 检查所有返回值
   - 完整的日志记录
   - 优雅的错误恢复

5. **性能考虑**
   - 合理设置队列大小
   - 避免频繁的内存分配
   - 使用对象池技术
