# ZclMltSrvHbtMngr - 心跳管理模块详细分析

## 概述

ZclMltSrvHbtMngr(Multi-Server Heartbeat Manager)是Dragon项目的心跳管理模块，负责多服务器之间的心跳维护机制。该模块确保服务器集群中各节点的状态监控和故障检测。

## 模块文件结构

```
ZclMltSrvHbtMngr/
├── HeartbeatHander.h/.cpp           # 心跳处理器 - 主控制器
├── Heartbeat_Broadcaster.h/.cpp     # 心跳广播器 - 发送心跳包
├── Heartbeat_Listerner.h/.cpp       # 心跳监听器 - 监听心跳包  
├── Heartbeat_Receiver.h/.cpp        # 心跳接收器 - 底层UDP接收
├── HeartbeatHandlerDefine.h         # 心跳处理器常量定义
├── ZxMultiSrvHeartbeat.h/.cpp       # 动态库主入口
└── 编译配置文件(Makefile, .vcxproj等)
```

## 核心组件架构

### 1. CHeartbeatHandler - 心跳主控制器

**功能**：作为心跳系统的总控制器，管理多个广播器和监听器

**核心职责**：
- 初始化和管理心跳广播器
- 初始化和管理心跳监听器
- 协调各组件间的工作
- 处理心跳超时和异常情况

**工作流程**：
```
初始化 -> 创建广播器和监听器 -> 启动服务 -> 
周期性检查状态 -> 异常处理 -> 资源清理
```

### 2. CHeartbeat_Broadcaster - 心跳广播器

**功能**：负责向指定地址发送心跳包

**关键特性**：
- 支持UDP广播和单播
- 可配置发送间隔
- 支持多目标地址发送
- 发送状态监控

**发送流程**：
```cpp
// 伪代码示例
class CHeartbeat_Broadcaster {
private:
    SOCKET m_sendSocket;
    vector<SOCKADDR_IN> m_targetAddrs;
    HANDLE m_sendThread;
    
public:
    // 启动广播
    int Start(const vector<string>& targetIPs, int port) {
        // 1. 创建UDP套接字
        m_sendSocket = socket(AF_INET, SOCK_DGRAM, 0);
        
        // 2. 配置目标地址列表
        for(auto& ip : targetIPs) {
            SOCKADDR_IN addr;
            addr.sin_family = AF_INET;
            addr.sin_addr.s_addr = inet_addr(ip.c_str());
            addr.sin_port = htons(port);
            m_targetAddrs.push_back(addr);
        }
        
        // 3. 创建发送线程
        m_sendThread = CreateThread(NULL, 0, SendThreadProc, this, 0, NULL);
        return 0;
    }
    
    // 发送线程处理函数
    static DWORD WINAPI SendThreadProc(LPVOID param) {
        CHeartbeat_Broadcaster* pThis = (CHeartbeat_Broadcaster*)param;
        while(pThis->m_running) {
            // 构造心跳包
            HEARTBEAT_PACKET packet;
            packet.timestamp = GetCurrentTime();
            packet.serverId = pThis->m_serverId;
            packet.status = pThis->m_serverStatus;
            
            // 向所有目标发送
            for(auto& addr : pThis->m_targetAddrs) {
                sendto(pThis->m_sendSocket, (char*)&packet, 
                       sizeof(packet), 0, (SOCKADDR*)&addr, sizeof(addr));
            }
            
            // 等待下次发送
            Sleep(pThis->m_sendInterval);
        }
        return 0;
    }
};
```

### 3. CHeartbeat_Listerner - 心跳监听器

**功能**：负责监听心跳包，内部使用CHeartbeat_Receiver接收数据

**核心功能**：
- 监听指定端口的心跳包
- 解析心跳数据
- 维护服务器状态表
- 检测心跳超时

**监听流程**：
```
绑定监听端口 -> 启动接收线程 -> 
解析心跳包 -> 更新服务器状态 -> 
检查超时 -> 触发状态变化事件
```

### 4. CHeartbeat_Receiver - 心跳接收器

**功能**：负责底层UDP数据接收

**技术实现**：
- 使用select模型或IOCP模型
- 支持多线程并发接收
- 数据包校验和过滤
- 接收缓冲区管理

**接收处理流程**：
```cpp
// 伪代码示例
class CHeartbeat_Receiver {
private:
    SOCKET m_listenSocket;
    HANDLE m_recvThread;
    
public:
    int StartListen(int port) {
        // 1. 创建UDP套接字
        m_listenSocket = socket(AF_INET, SOCK_DGRAM, 0);
        
        // 2. 绑定监听地址
        SOCKADDR_IN bindAddr;
        bindAddr.sin_family = AF_INET;
        bindAddr.sin_addr.s_addr = INADDR_ANY;
        bindAddr.sin_port = htons(port);
        bind(m_listenSocket, (SOCKADDR*)&bindAddr, sizeof(bindAddr));
        
        // 3. 启动接收线程
        m_recvThread = CreateThread(NULL, 0, RecvThreadProc, this, 0, NULL);
        return 0;
    }
    
    static DWORD WINAPI RecvThreadProc(LPVOID param) {
        CHeartbeat_Receiver* pThis = (CHeartbeat_Receiver*)param;
        char buffer[1024];
        SOCKADDR_IN fromAddr;
        int fromLen = sizeof(fromAddr);
        
        while(pThis->m_running) {
            // 接收数据包
            int recvLen = recvfrom(pThis->m_listenSocket, buffer, sizeof(buffer),
                                   0, (SOCKADDR*)&fromAddr, &fromLen);
            
            if(recvLen > 0) {
                // 解析心跳包
                HEARTBEAT_PACKET* packet = (HEARTBEAT_PACKET*)buffer;
                if(pThis->ValidatePacket(packet)) {
                    // 通知监听器处理
                    pThis->OnHeartbeatReceived(packet, &fromAddr);
                }
            }
        }
        return 0;
    }
};
```

## 心跳协议设计

### 心跳包结构
```cpp
typedef struct _HEARTBEAT_PACKET {
    UINT32 magic;           // 魔数，用于包校验
    UINT32 version;         // 协议版本
    char serverId[32];      // 服务器ID
    UINT32 timestamp;       // 时间戳
    UINT32 status;          // 服务器状态
    UINT32 stationCount;    // 管辖厂站数量
    STATION_STATUS stations[MAX_STATIONS]; // 厂站状态列表
    UINT32 checksum;        // 校验和
} HEARTBEAT_PACKET;

typedef struct _STATION_STATUS {
    char stationId[16];     // 厂站ID
    UINT32 commStatus;      // 通信状态：0-断开，1-正常
    UINT32 lastUpdate;      // 最后更新时间
} STATION_STATUS;
```

### 端口分配策略

根据文档说明，为避免多类型前置使用同一心跳端口冲突：

```cpp
// 端口计算公式：基础端口 + 2 * 前置类型
int CalculateHeartbeatPort(int basePort, int frontendType) {
    return basePort + 2 * frontendType;
}

// 端口分配示例：
// 基础端口：17255
// 常规保信前置(type=0)：接收端口17255，发送端口17256
// 61850前置(type=1)：接收端口17257，发送端口17258  
// 104Vlan(type=2)：接收端口17259，发送端口17260
// 私有规约透传前置(type=3)：接收端口17261，发送端口17262
```

## 状态管理机制

### 1. 服务器状态定义
```cpp
enum SERVER_STATUS {
    SERVER_UNKNOWN = 0,     // 未知状态
    SERVER_ACTIVE = 1,      // 活动状态
    SERVER_STANDBY = 2,     // 待机状态
    SERVER_OFFLINE = 3      // 离线状态
};
```

### 2. 状态转换逻辑
```
离线 -> 待机：收到第一个心跳包
待机 -> 活动：服务器开始承担业务
活动 -> 待机：主动切换或负载均衡
任何状态 -> 离线：心跳超时
```

### 3. 心跳超时检测
```cpp
class CHeartbeatMonitor {
private:
    map<string, ServerInfo> m_serverMap;
    HANDLE m_checkThread;
    
    struct ServerInfo {
        string serverId;
        UINT32 lastHeartbeat;
        SERVER_STATUS status;
        vector<STATION_STATUS> stations;
    };
    
public:
    // 心跳检查线程
    static DWORD WINAPI CheckThreadProc(LPVOID param) {
        CHeartbeatMonitor* pThis = (CHeartbeatMonitor*)param;
        while(pThis->m_running) {
            UINT32 currentTime = GetCurrentTime();
            
            for(auto& pair : pThis->m_serverMap) {
                ServerInfo& info = pair.second;
                
                // 检查心跳超时(默认30秒)
                if(currentTime - info.lastHeartbeat > HEARTBEAT_TIMEOUT) {
                    if(info.status != SERVER_OFFLINE) {
                        info.status = SERVER_OFFLINE;
                        // 触发服务器离线事件
                        pThis->OnServerOffline(info.serverId);
                    }
                }
            }
            
            Sleep(CHECK_INTERVAL); // 5秒检查一次
        }
        return 0;
    }
};
```

## 配置管理

### 1. 心跳配置参数
```ini
[HEARTBEAT_CONFIG]
send_interval=10        ; 心跳发送间隔(秒)
timeout_threshold=30    ; 心跳超时阈值(秒)
retry_count=3          ; 重试次数
base_port=17255        ; 基础端口号

[SERVER_LIST]
server1=192.168.1.100
server2=192.168.1.101
server3=192.168.1.102
```

### 2. 日志配置
```cpp
enum LOG_LEVEL {
    LOG_ERROR = 1,
    LOG_WARN = 2, 
    LOG_INFO = 3,
    LOG_DEBUG = 4
};

class CHeartbeatLogger {
public:
    void WriteLog(LOG_LEVEL level, const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        
        // 根据级别写入不同日志文件
        switch(level) {
            case LOG_ERROR:
                WriteToFile("heartbeat_error.log", buffer);
                break;
            case LOG_INFO:
                WriteToFile("heartbeat_info.log", buffer);
                break;
        }
    }
};
```

## 异常处理机制

### 1. 网络异常处理
- 套接字创建失败
- 端口绑定失败  
- 网络发送失败
- 接收缓冲区溢出

### 2. 线程异常处理
- 线程创建失败
- 线程意外终止
- 线程资源泄漏

### 3. 内存异常处理
- 内存分配失败
- 缓冲区越界
- 野指针访问

## 性能优化

### 1. 网络性能优化
```cpp
// 设置套接字选项
int bufferSize = 64 * 1024;
setsockopt(socket, SOL_SOCKET, SO_RCVBUF, (char*)&bufferSize, sizeof(bufferSize));
setsockopt(socket, SOL_SOCKET, SO_SNDBUF, (char*)&bufferSize, sizeof(bufferSize));

// 非阻塞模式
u_long mode = 1;
ioctlsocket(socket, FIONBIO, &mode);
```

### 2. 内存池管理
```cpp
class CPacketPool {
private:
    queue<HEARTBEAT_PACKET*> m_freePackets;
    mutex m_mutex;
    
public:
    HEARTBEAT_PACKET* AllocPacket() {
        lock_guard<mutex> lock(m_mutex);
        if(!m_freePackets.empty()) {
            HEARTBEAT_PACKET* packet = m_freePackets.front();
            m_freePackets.pop();
            return packet;
        }
        return new HEARTBEAT_PACKET;
    }
    
    void FreePacket(HEARTBEAT_PACKET* packet) {
        lock_guard<mutex> lock(m_mutex);
        memset(packet, 0, sizeof(HEARTBEAT_PACKET));
        m_freePackets.push(packet);
    }
};
```

## 使用示例

### 初始化心跳管理器
```cpp
// 1. 创建心跳处理器实例
CHeartbeatHandler* pHandler = new CHeartbeatHandler();

// 2. 设置配置参数
HEARTBEAT_CONFIG config;
config.sendInterval = 10;
config.timeoutThreshold = 30;
config.basePort = 17255;
config.frontendType = FRONTEND_TYPE_104VLAN;

// 3. 设置服务器列表
vector<string> serverList = {"192.168.1.100", "192.168.1.101"};

// 4. 启动心跳服务
pHandler->Start(config, serverList);

// 5. 注册状态变化回调
pHandler->RegisterStatusCallback(OnServerStatusChanged);
```

这个心跳管理模块是Dragon系统高可用性的核心组件，确保了多服务器环境下的状态监控和故障检测能力。
