# ZclMltSrvHbtMngr - 心跳管理模块文件详细分析

## 模块概述
- **模块名称**: ZclMltSrvHbtMngr (多服务器心跳管理器)
- **主要功能**: 实现服务器间的心跳通信，监控服务器状态，支持主备切换
- **作者**: qingch
- **创建时间**: 2011/11

## 文件结构和关系

```
ZclMltSrvHbtMngr/
├── HeartbeatHander.h/.cpp          # 心跳处理主控制器
├── Heartbeat_Broadcaster.h/.cpp    # 心跳广播器（发送）
├── Heartbeat_Listerner.h/.cpp      # 心跳监听器（接收）
├── Heartbeat_Receiver.h/.cpp       # 心跳接收器（处理）
├── HeartbeatHandlerDefine.h        # 常量定义
├── ZxMultiSrvHeartbeat.h/.cpp      # 模块导出接口
└── ZxMultiSrvHeartbeat_update_history.cpp  # 更新历史
```

## 文件详细分析

### 1. HeartbeatHandlerDefine.h - 常量定义

```cpp
const int MSG_HEAD_LENGTH = 181;      // 消息头长度
const int HEARTBEAT_MAX_LEN = 20480;  // 心跳消息最大长度
```

**作用**: 定义心跳模块使用的全局常量

### 2. HeartbeatHander.h - 心跳处理主控制器

#### 类定义
```cpp
class CHeartbeatHandler : public CHeartbeatInterface
{
private:
    HBBroadcasterTable m_mapBroadcaster;  // 广播者映射表
    HBListenerTable m_mapListener;        // 监听者映射表
    CLogFile* m_pLogFile;                 // 日志记录
    CLogFile* m_pMessageLog;              // 报文日志
    bool m_bLogMessage;                   // 是否记录日志
};
```

#### 核心函数详解

##### RegisterBroadcastAddress - 注册广播地址
```cpp
virtual int RegisterBroadcastAddress(string pAddress, int pPort);
```
- **功能**: 注册心跳广播的目标地址
- **执行流程**:
  1. 验证地址格式
  2. 创建CHeartbeat_Broadcaster实例
  3. 加入m_mapBroadcaster映射表
  4. 初始化网络连接
- **使用场景**: 配置心跳发送目标

##### BroatcastHeartbeat - 广播心跳
```cpp
virtual int BroatcastHeartbeat(StHeartbeatInfo& pInfo);
```
- **功能**: 向所有注册地址发送心跳
- **执行流程**:
  1. 遍历m_mapBroadcaster中所有广播器
  2. 调用每个广播器的SendHeartbeat
  3. 记录发送日志
  4. 处理发送失败情况
- **返回值**: 0成功，-1失败

##### StartHeartbeatListening - 启动心跳监听
```cpp
virtual int StartHeartbeatListening(vector<string>& pAddresses, int pPort, int pTimeout);
```
- **功能**: 在指定端口监听心跳
- **参数**:
  - `pAddresses`: 监听地址列表
  - `pPort`: 监听端口
  - `pTimeout`: 超时时间（秒）
- **执行流程**:
  1. 为每个地址创建CHeartbeat_Listerner
  2. 绑定UDP端口
  3. 启动监听线程
  4. 设置超时参数

##### EndHeartbeatListening - 停止心跳监听
```cpp
virtual int EndHeartbeatListening();
```
- **功能**: 停止所有心跳监听
- **执行流程**:
  1. 停止所有监听线程
  2. 关闭网络连接
  3. 清理m_mapListener
  4. 释放资源

##### RegisterHeartbeatListener - 注册监听回调
```cpp
virtual int RegisterHeartbeatListener(PFUNRECVHEARTBEAT pOnRecv, void* pParam);
```
- **功能**: 注册心跳接收的回调函数
- **参数**:
  - `pOnRecv`: 回调函数指针
  - `pParam`: 用户参数
- **执行流程**:
  1. 保存回调函数
  2. 传递给所有监听器
  3. 设置回调上下文

### 3. Heartbeat_Broadcaster.h - 心跳广播器

#### 类定义
```cpp
class CHeartbeat_Broadcaster {
private:
    NETADDR m_DestAddr;           // 目标地址
    CNet m_Net;                   // 网络对象
    CLogFile* m_pMessageLog;      // 报文日志
    bool m_bLogMessage;           // 日志标记
};
```

#### 核心函数详解

##### Open - 打开广播连接
```cpp
virtual bool Open(NETADDR& pDestAddr, string& pError);
virtual bool Open(string& pError);
```
- **功能**: 建立UDP广播连接
- **执行流程**:
  1. 创建UDP套接字
  2. 设置广播选项
  3. 绑定本地端口
  4. 记录连接状态

##### SendHeartbeat - 发送心跳
```cpp
virtual bool SendHeartbeat(const StHeartbeatInfo& pInfo, string& pError);
```
- **功能**: 发送心跳数据包
- **执行流程**:
  1. 调用ConvertFrameStructToStream转换数据
  2. 通过UDP发送数据
  3. 记录发送日志
  4. 处理发送错误

##### ConvertFrameStructToStream - 数据转换
```cpp
void ConvertFrameStructToStream(string &OutMsg, const StHeartbeatInfo &pFrameData);
```
- **功能**: 将心跳结构体转换为字节流
- **执行流程**:
  1. 序列化心跳头信息
  2. 序列化服务器状态
  3. 序列化厂站列表
  4. 计算校验和

### 4. Heartbeat_Listerner.h - 心跳监听器

#### 类定义
```cpp
class CHeartbeat_Listerner {
private:
    NETADDR m_ListenAddr;              // 监听地址
    CNet m_Net;                        // 网络对象
    bool m_bRunning;                   // 运行状态
    thread* m_pListenThread;           // 监听线程
    PFUNRECVHEARTBEAT m_pCallback;     // 回调函数
    void* m_pCallbackParam;            // 回调参数
};
```

#### 核心函数

##### StartListening - 启动监听
- **功能**: 启动UDP监听服务
- **执行流程**:
  1. 绑定UDP端口
  2. 创建监听线程
  3. 进入接收循环
  4. 处理接收的数据

##### ListenThreadProc - 监听线程
- **功能**: 循环接收心跳数据
- **执行流程**:
  ```cpp
  while (m_bRunning) {
      // 1. 接收UDP数据
      int len = m_Net.RecvFrom(buffer, sizeof(buffer), addr);
      
      // 2. 解析心跳数据
      StHeartbeatInfo info;
      if (ParseHeartbeat(buffer, len, info)) {
          // 3. 调用回调函数
          if (m_pCallback) {
              m_pCallback(m_pCallbackParam, info);
          }
      }
      
      // 4. 检查超时
      CheckTimeout();
  }
  ```

### 5. Heartbeat_Receiver.h - 心跳接收器

#### 主要功能
- 处理接收到的心跳数据
- 维护远程服务器状态表
- 检测心跳超时
- 触发状态变化事件

#### 核心数据结构
```cpp
struct RemoteServerInfo {
    string serverId;              // 服务器ID
    StHeartbeatInfo lastInfo;     // 最后心跳信息
    time_t lastHeartbeatTime;     // 最后心跳时间
    int missedCount;              // 丢失计数
    bool isAlive;                 // 存活状态
};
```

### 6. ZxMultiSrvHeartbeat.h - 模块导出接口

#### 导出函数
```cpp
// 创建心跳处理器
extern "C" CHeartbeatInterface* CreateHeartbeatHandler(
    bool pIfLogMessage, 
    CLogFile* pFlowLog,
    CLogFile* pMessageLog
);

// 销毁心跳处理器
extern "C" bool DestroyHeartbeatHandler(CHeartbeatInterface* pHandler);
```

## 执行流程分析

### 1. 初始化流程
```cpp
// 1. 加载动态库
void* handle = dlopen("libZclMltSrvHbtMngr.so", RTLD_LAZY);

// 2. 获取创建函数
PFUNCREATHEARTBEATHANDLER createFunc = 
    (PFUNCREATHEARTBEATHANDLER)dlsym(handle, "CreateHeartbeatHandler");

// 3. 创建实例
CHeartbeatInterface* pHandler = createFunc(true, &logFile, &msgLog);

// 4. 配置发送地址
pHandler->RegisterBroadcastAddress("***************", 17255);

// 5. 启动监听
vector<string> listenAddrs;
listenAddrs.push_back("0.0.0.0");
pHandler->StartHeartbeatListening(listenAddrs, 17255, 30);

// 6. 注册回调
pHandler->RegisterHeartbeatListener(OnHeartbeatReceived, this);
```

### 2. 心跳发送流程
```cpp
void SendHeartbeatPeriodically() {
    while (running) {
        // 1. 准备心跳信息
        StHeartbeatInfo info;
        FillHeartbeatInfo(info);
        
        // 2. 广播心跳
        int result = pHandler->BroatcastHeartbeat(info);
        
        if (result != 0) {
            LogError("心跳发送失败");
            // 尝试重连
            ReconnectBroadcast();
        }
        
        // 3. 等待下次发送
        Sleep(10000);  // 10秒
    }
}
```

### 3. 心跳接收处理流程
```cpp
int OnHeartbeatReceived(void* pParam, StHeartbeatInfo& info) {
    ServerManager* pMgr = (ServerManager*)pParam;
    
    // 1. 更新服务器状态表
    pMgr->UpdateServerStatus(info.strServerID, info);
    
    // 2. 检查服务器角色变化
    if (info.iOnlineStatus != lastStatus) {
        // 触发主备切换逻辑
        pMgr->OnServerRoleChanged(info.strServerID, info.iOnlineStatus);
    }
    
    // 3. 更新厂站管辖信息
    pMgr->UpdateStationOwnership(info.listStationID);
    
    // 4. 记录心跳日志
    LogHeartbeat(info);
    
    return 0;
}
```

### 4. 超时检测流程
```cpp
void CheckHeartbeatTimeout() {
    time_t now = time(NULL);
    
    for (auto& server : m_serverList) {
        if (now - server.lastHeartbeatTime > m_timeout) {
            // 心跳超时
            server.missedCount++;
            
            if (server.missedCount >= 3) {
                // 连续3次超时，判定为离线
                server.isAlive = false;
                OnServerOffline(server.serverId);
            }
        }
    }
}
```

## 关键数据结构

### StHeartbeatInfo - 心跳信息结构
```cpp
typedef struct _Struct_Heartbeat_Info {
    WORD wSerialNo;                        // 序列号
    int iType;                             // 心跳类型
    int iGroup;                            // 服务器组号
    int iRunStatus;                        // 运行状态
    int iOnlineStatus;                     // 在线状态
    int iLoadSoccer;                       // 负载评分
    time_t tTimed;                         // 时间戳
    string strServerID;                    // 服务器ID
    string strHBAddr;                      // 心跳网IP
    string strNetAAddr;                    // A网IP
    string strNetBAddr;                    // B网IP
    vector<StHBSubstationStatus> listStationID;  // 厂站状态列表
} StHeartbeatInfo;
```

### 心跳类型
```cpp
const int HEARTBEAT_TYPE_INFO = 0;       // 工况信息
const int HEARTBEAT_TYPE_OPERATION = 1;  // 手动切换
```

### 在线状态
```cpp
-1: 未知
0:  Standby (备用)
1:  Active (主用)
```

### 运行状态
```cpp
0: 不可用
1: 可用
```

## 设计特点

### 1. 模块化设计
- 发送和接收分离
- 清晰的接口定义
- 易于扩展和维护

### 2. 高可靠性
- 支持多地址广播
- 超时检测机制
- 自动重连功能

### 3. 性能优化
- 使用UDP协议减少开销
- 异步接收处理
- 高效的数据序列化

### 4. 灵活配置
- 可配置的超时时间
- 支持多监听地址
- 日志级别控制

## 使用建议

1. **网络配置**
   - 确保UDP端口开放
   - 配置正确的广播地址
   - 考虑网络分段影响

2. **超时设置**
   - 根据网络延迟调整
   - 一般设置为发送间隔的3倍
   - 考虑丢包情况

3. **日志管理**
   - 合理设置日志级别
   - 定期清理日志文件
   - 关键事件必须记录

4. **异常处理**
   - 处理网络异常
   - 实现重连机制
   - 避免资源泄漏
