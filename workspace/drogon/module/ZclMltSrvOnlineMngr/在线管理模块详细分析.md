# ZclMltSrvOnlineMngr - 在线管理模块详细分析

## 概述

ZclMltSrvOnlineMngr(Multi-Server Online Manager)是Dragon项目的服务器在线管理模块，负责多服务器环境下的在线状态管理和自动切换功能。该模块实现了服务器主备切换、厂站管辖权转移等关键功能。

## 模块文件结构

```
ZclMltSrvOnlineMngr/
├── SrvOnlineManager.h/.cpp          # 在线管理器主控制器
├── SrvSelfStatusMonitor.h/.cpp      # 本机状态监控器
├── SrvHeartbeatMonitor.h/.cpp       # 心跳状态监控器
├── SrvOtherStatusMonitor.h/.cpp     # 其他服务器状态监控器
├── ServerStatus.h/.cpp              # 服务器状态管理
├── ISrvSwitchHandler.h/.cpp         # 服务器切换处理接口
├── SingleSrvSwitchHandler.h/.cpp    # 单服务器切换处理器
├── AASrvSwitchHandler.h/.cpp        # AA服务器切换处理器
├── ASSrvSwitchHandler.h/.cpp        # AS服务器切换处理器
├── SrvManagerDefine.h               # 服务器管理器常量定义
├── ZxMultiSrvOnlineMng.h/.cpp       # 动态库主入口
└── 部署多套通信服务器并行方法.docx   # 部署说明文档
```

## 核心架构设计

### 1. CSrvOnlineManager - 在线管理器总控制器

**功能**：作为总控制器，协调各个子组件的工作

**核心职责**：
- 初始化各种监控器
- 管理服务器切换逻辑
- 协调厂站管辖权转移
- 处理异常情况

**初始化流程**：
```cpp
class CSrvOnlineManager {
private:
    CSrvSelfStatusMonitor*    m_pSelfMonitor;     // 本机状态监控器
    CSrvHeartbeatMonitor*     m_pHeartbeatMonitor; // 心跳监控器
    CSrvOtherStatusMonitor*   m_pOtherMonitor;    // 其他服务器监控器
    ISrvSwitchHandler*        m_pSwitchHandler;   // 切换处理器
    
public:
    int Initialize() {
        // 1. 创建本机状态监控器
        m_pSelfMonitor = new CSrvSelfStatusMonitor();
        m_pSelfMonitor->Initialize();
        
        // 2. 创建心跳监控器
        m_pHeartbeatMonitor = new CSrvHeartbeatMonitor();
        m_pHeartbeatMonitor->Initialize();
        
        // 3. 创建其他服务器监控器
        m_pOtherMonitor = new CSrvOtherStatusMonitor();
        m_pOtherMonitor->Initialize();
        
        // 4. 根据配置创建相应的切换处理器
        CreateSwitchHandler();
        
        // 5. 注册各种事件回调
        RegisterEventHandlers();
        
        return 0;
    }
};
```

### 2. CSrvSelfStatusMonitor - 本机状态监控器

**功能**：监控本地服务器状态并发送心跳

**关键监控指标**：
- CPU使用率
- 内存使用率
- 磁盘空间
- 网络连接状态
- 业务进程状态
- 厂站通信状态

**监控流程**：
```cpp
class CSrvSelfStatusMonitor {
private:
    HANDLE m_monitorThread;
    SERVER_STATUS m_currentStatus;
    vector<STATION_STATUS> m_stationList;
    
public:
    // 监控线程主函数
    static DWORD WINAPI MonitorThreadProc(LPVOID param) {
        CSrvSelfStatusMonitor* pThis = (CSrvSelfStatusMonitor*)param;
        
        while(pThis->m_running) {
            // 1. 检查系统资源状态
            pThis->CheckSystemResource();
            
            // 2. 检查业务进程状态
            pThis->CheckBusinessProcess();
            
            // 3. 检查厂站通信状态
            pThis->CheckStationCommunication();
            
            // 4. 更新服务器综合状态
            pThis->UpdateServerStatus();
            
            // 5. 发送心跳包给其他服务器
            pThis->SendHeartbeatToOthers();
            
            Sleep(MONITOR_INTERVAL); // 默认5秒
        }
        return 0;
    }
    
    // 检查厂站通信状态
    void CheckStationCommunication() {
        for(auto& station : m_stationList) {
            // 查询数据库获取厂站最后通信时间
            DWORD lastCommTime = GetStationLastCommTime(station.stationId);
            DWORD currentTime = GetCurrentTime();
            
            // 判断通信是否超时（默认5分钟）
            if(currentTime - lastCommTime > COMMUNICATION_TIMEOUT) {
                if(station.status == STATION_COMM_NORMAL) {
                    station.status = STATION_COMM_TIMEOUT;
                    station.timeoutStart = currentTime;
                    
                    // 记录厂站断开事件
                    WriteLog("厂站 %s 通信超时", station.stationId);
                }
                
                // 检查是否到达切换时间（默认5分钟后释放管辖权）
                if(currentTime - station.timeoutStart > SWITCH_TIMEOUT) {
                    // 通知切换处理器释放厂站管辖权
                    m_pSwitchHandler->ReleaseStationControl(station.stationId);
                }
            }
            else {
                station.status = STATION_COMM_NORMAL;
                station.timeoutStart = 0;
            }
        }
    }
};
```

### 3. CSrvHeartbeatMonitor - 心跳状态监控器

**功能**：监听其他服务器心跳并更新状态

**核心任务**：
- 接收其他服务器心跳包
- 解析心跳包信息
- 更新服务器状态表
- 检测服务器离线
- 判断厂站接管时机

**心跳处理流程**：
```cpp
class CSrvHeartbeatMonitor {
private:
    map<string, SERVER_INFO> m_serverMap;
    SOCKET m_listenSocket;
    HANDLE m_recvThread;
    
    struct SERVER_INFO {
        string serverId;
        DWORD lastHeartbeat;
        SERVER_STATUS status;
        vector<STATION_STATUS> managedStations;
        DWORD offlineTime;
    };
    
public:
    // 心跳接收处理
    void OnHeartbeatReceived(const HEARTBEAT_PACKET& packet) {
        string serverId = packet.serverId;
        DWORD currentTime = GetCurrentTime();
        
        // 更新服务器信息
        SERVER_INFO& serverInfo = m_serverMap[serverId];
        serverInfo.serverId = serverId;
        serverInfo.lastHeartbeat = currentTime;
        serverInfo.status = (SERVER_STATUS)packet.status;
        
        // 更新该服务器管辖的厂站状态
        serverInfo.managedStations.clear();
        for(int i = 0; i < packet.stationCount; i++) {
            STATION_STATUS station;
            strcpy(station.stationId, packet.stations[i].stationId);
            station.commStatus = packet.stations[i].commStatus;
            station.lastUpdate = packet.stations[i].lastUpdate;
            serverInfo.managedStations.push_back(station);
            
            // 检查是否需要接管断开的厂站
            CheckStationTakeover(serverInfo, station);
        }
    }
    
    // 检查厂站接管条件
    void CheckStationTakeover(const SERVER_INFO& serverInfo, const STATION_STATUS& station) {
        // 如果厂站通信断开且超过接管时间阈值
        if(station.commStatus == STATION_COMM_DISCONNECTED) {
            DWORD currentTime = GetCurrentTime();
            DWORD disconnectTime = currentTime - station.lastUpdate;
            
            // 超过5分钟未恢复，考虑接管
            if(disconnectTime > TAKEOVER_THRESHOLD) {
                // 检查本服务器是否有能力接管该厂站
                if(CanTakeoverStation(station.stationId)) {
                    // 通知切换处理器接管厂站
                    m_pSwitchHandler->TakeoverStation(station.stationId);
                }
            }
        }
    }
};
```

### 4. CServerSwitchHandler - 服务器切换处理器

**功能**：根据服务器状态执行切换逻辑

**切换策略**：
- **主动切换**：管理员手动切换
- **故障切换**：服务器故障自动切换
- **负载切换**：负载均衡切换
- **维护切换**：维护期间切换

**切换执行流程**：
```cpp
class CServerSwitchHandler {
public:
    // 执行服务器切换
    int ExecuteServerSwitch(const SWITCH_REQUEST& request) {
        int result = 0;
        
        switch(request.switchType) {
            case SWITCH_TYPE_FAILOVER:
                result = ExecuteFailoverSwitch(request);
                break;
                
            case SWITCH_TYPE_MANUAL:
                result = ExecuteManualSwitch(request);
                break;
                
            case SWITCH_TYPE_LOADBALANCE:
                result = ExecuteLoadBalanceSwitch(request);
                break;
        }
        
        return result;
    }
    
    // 执行故障切换
    int ExecuteFailoverSwitch(const SWITCH_REQUEST& request) {
        // 1. 验证切换条件
        if(!ValidateFailoverCondition(request)) {
            return -1;
        }
        
        // 2. 准备接管资源
        if(!PrepareResourceTakeover(request.stationList)) {
            return -2;
        }
        
        // 3. 启动相关厂站进程
        for(auto& stationId : request.stationList) {
            StartStationProcess(stationId);
        }
        
        // 4. 更新数据库管辖权信息
        UpdateStationOwnership(request.stationList, m_localServerId);
        
        // 5. 通知其他服务器切换完成
        NotifyOtherServers(request);
        
        return 0;
    }
    
    // 启动厂站进程
    void StartStationProcess(const string& stationId) {
        // 根据厂站类型启动不同的进程
        STATION_CONFIG config = GetStationConfig(stationId);
        
        switch(config.stationType) {
            case STATION_TYPE_61850:
                Start61850Process(stationId, config);
                break;
                
            case STATION_TYPE_104:
                Start104Process(stationId, config);
                break;
                
            case STATION_TYPE_103:
                Start103Process(stationId, config);
                break;
        }
    }
};
```

## 厂站管辖权管理

### 1. 厂站类型识别

根据文档说明，系统需要自动识别各前置管辖的厂站列表：

```cpp
enum FRONTEND_TYPE {
    FRONTEND_NORMAL = 0,        // 常规保信前置
    FRONTEND_61850 = 1,         // 61850前置  
    FRONTEND_104VLAN = 2,       // 104Vlan
    FRONTEND_PRIVATE = 3        // 私有规约透传前置
};

class CStationManager {
public:
    // 获取前置管辖的厂站列表
    vector<string> GetManagedStations(FRONTEND_TYPE frontendType) {
        vector<string> stationList;
        
        switch(frontendType) {
            case FRONTEND_61850:
                // 查询使用61850规约的厂站
                stationList = QueryStationsByProtocol("61850");
                break;
                
            case FRONTEND_104VLAN:
                // 查询湖南透传子站
                stationList = QueryStationsByFunctionType(7); // stn_fun_type=7
                break;
                
            case FRONTEND_PRIVATE:
                // 查询私有规约透传或混合透传厂站
                stationList = QueryStationsByChannelMode({3, 5}); // reserve4=3或5
                break;
        }
        
        // 过滤本服务器管辖的厂站
        return FilterLocalManagedStations(stationList);
    }
    
private:
    // 查询使用指定规约的厂站
    vector<string> QueryStationsByProtocol(const string& protocol) {
        vector<string> result;
        
        // SQL查询示例
        string sql = "SELECT DISTINCT s.station_id FROM tb_station s "
                    "JOIN tb_channel c ON s.channel_id = c.channel_id "
                    "JOIN tb_protocol p ON c.protocol_id = p.protocol_id "
                    "WHERE p.protocol_name LIKE '%" + protocol + "%' "
                    "AND s.server_id = '" + m_localServerId + "'";
        
        // 执行查询并返回结果
        return ExecuteStationQuery(sql);
    }
};
```

### 2. 切换时间控制

系统支持配置厂站断开后的释放管辖权时间：

```cpp
class CSwitchTimeManager {
private:
    map<string, DWORD> m_stationReleaseTime; // 厂站释放时间配置
    
public:
    // 从数据库加载切换时间配置
    void LoadSwitchTimeConfig() {
        // 查询 tb_commu_server_config 表
        string sql = "SELECT station_id, release_time FROM tb_commu_server_config "
                    "WHERE server_id = '" + m_serverId + "'";
        
        // 执行查询并填充配置
        auto resultSet = m_pDatabase->Query(sql);
        for(auto& row : resultSet) {
            string stationId = row["station_id"];
            DWORD releaseTime = atoi(row["release_time"].c_str()) * 1000; // 转换为毫秒
            m_stationReleaseTime[stationId] = releaseTime;
        }
    }
    
    // 检查厂站是否到达释放时间
    bool IsStationReleaseTime(const string& stationId, DWORD disconnectDuration) {
        auto it = m_stationReleaseTime.find(stationId);
        if(it != m_stationReleaseTime.end()) {
            return disconnectDuration >= it->second;
        }
        
        // 默认5分钟
        return disconnectDuration >= (5 * 60 * 1000);
    }
};
```

## 配置管理

### 1. 服务器配置表结构
```sql
-- tb_commu_server_config 多机热切服务器配置表
CREATE TABLE tb_commu_server_config (
    server_id VARCHAR(32) PRIMARY KEY,     -- 服务器ID
    server_name VARCHAR(64),               -- 服务器名称
    heartbeat_port INT,                    -- 心跳端口
    release_time INT,                      -- 厂站断开释放时间(分钟)
    priority INT,                          -- 服务器优先级
    status INT DEFAULT 1                   -- 服务器状态
);
```

### 2. 切换配置参数
```ini
[SWITCH_CONFIG]
auto_switch_enabled=1           ; 是否启用自动切换
failover_timeout=300           ; 故障切换超时时间(秒)
station_release_time=300       ; 厂站释放管辖权时间(秒)
heartbeat_timeout=30           ; 心跳超时时间(秒)
switch_retry_count=3           ; 切换重试次数

[SERVER_PRIORITY]
server1=100                    ; 服务器1优先级
server2=90                     ; 服务器2优先级
server3=80                     ; 服务器3优先级
```

## 状态机设计

### 1. 服务器状态转换
```cpp
enum SERVER_STATE {
    STATE_OFFLINE = 0,      // 离线
    STATE_STARTING = 1,     // 启动中
    STATE_STANDBY = 2,      // 待机
    STATE_ACTIVE = 3,       // 活动
    STATE_SWITCHING = 4,    // 切换中
    STATE_MAINTENANCE = 5   // 维护中
};

class CServerStateMachine {
private:
    SERVER_STATE m_currentState;
    
public:
    // 状态转换处理
    bool TransitionTo(SERVER_STATE newState, const string& reason) {
        SERVER_STATE oldState = m_currentState;
        
        // 验证状态转换的合法性
        if(!IsValidTransition(oldState, newState)) {
            return false;
        }
        
        // 执行状态转换前的准备工作
        if(!PrepareStateTransition(oldState, newState)) {
            return false;
        }
        
        // 更新状态
        m_currentState = newState;
        
        // 执行状态转换后的清理工作
        CompleteStateTransition(oldState, newState);
        
        // 记录状态变化
        WriteLog("服务器状态从 %s 转换为 %s，原因：%s", 
                StateToString(oldState).c_str(),
                StateToString(newState).c_str(),
                reason.c_str());
        
        return true;
    }
};
```

## 异常处理和容错机制

### 1. 网络分区处理
```cpp
class CNetworkPartitionHandler {
public:
    // 检测网络分区
    bool DetectNetworkPartition() {
        int reachableServers = 0;
        int totalServers = m_serverList.size();
        
        // 尝试连接所有其他服务器
        for(auto& serverId : m_serverList) {
            if(serverId != m_localServerId) {
                if(PingServer(serverId)) {
                    reachableServers++;
                }
            }
        }
        
        // 如果能连接的服务器少于一半，可能发生网络分区
        return (reachableServers < totalServers / 2);
    }
    
    // 处理网络分区
    void HandleNetworkPartition() {
        // 1. 停止厂站进程，避免数据冲突
        StopAllStationProcesses();
        
        // 2. 设置服务器为网络分区状态
        SetServerState(STATE_NETWORK_PARTITION);
        
        // 3. 定期尝试重新连接
        StartReconnectionAttempts();
    }
};
```

### 2. 脑裂预防机制
```cpp
class CSplitBrainPrevention {
public:
    // 检查是否存在脑裂风险
    bool CheckSplitBrainRisk() {
        // 统计认为自己是主服务器的服务器数量
        int activeServers = 0;
        for(auto& pair : m_serverStatusMap) {
            if(pair.second.status == SERVER_ACTIVE) {
                activeServers++;
            }
        }
        
        // 如果有多个服务器都认为自己是主服务器，存在脑裂风险
        return activeServers > 1;
    }
    
    // 解决脑裂冲突
    void ResolveSplitBrain() {
        // 1. 比较服务器优先级
        string highestPriorityServer = FindHighestPriorityServer();
        
        // 2. 如果本服务器不是最高优先级，则转为待机
        if(highestPriorityServer != m_localServerId) {
            TransitionToStandby();
        }
        
        // 3. 如果优先级相同，比较服务器ID字典序
        else if(IsMultipleHighestPriority()) {
            if(m_localServerId != GetLowestServerIdWithHighestPriority()) {
                TransitionToStandby();
            }
        }
    }
};
```

## 监控和报警

### 1. 关键指标监控
```cpp
class COnlineManagerMonitor {
private:
    struct MONITOR_METRICS {
        DWORD heartbeatLossCount;      // 心跳丢失次数
        DWORD switchExecutionCount;    // 切换执行次数
        DWORD stationTakeoverCount;    // 厂站接管次数
        DWORD lastSwitchTime;          // 最后切换时间
        float serverAvailability;      // 服务器可用性
    };
    
public:
    // 生成监控报告
    void GenerateMonitorReport() {
        MONITOR_METRICS metrics = CollectMetrics();
        
        // 检查异常指标
        if(metrics.heartbeatLossCount > HEARTBEAT_LOSS_THRESHOLD) {
            TriggerAlert("心跳丢失过多", ALERT_WARNING);
        }
        
        if(metrics.switchExecutionCount > SWITCH_COUNT_THRESHOLD) {
            TriggerAlert("切换过于频繁", ALERT_WARNING);
        }
        
        if(metrics.serverAvailability < AVAILABILITY_THRESHOLD) {
            TriggerAlert("服务器可用性过低", ALERT_CRITICAL);
        }
    }
};
```

这个在线管理模块是Dragon系统高可用性架构的核心，确保了多服务器环境下的自动故障切换和负载均衡能力。
