# ZclMltSrvOnlineMngr - 服务器在线管理模块文件详细分析

## 模块概述
- **模块名称**: ZclMltSrvOnlineMngr (多服务器在线管理器)
- **主要功能**: 管理服务器在线状态，实现主备切换，负载均衡，厂站管辖权管理
- **作者**: qingch
- **创建时间**: 2011/11

## 文件结构和关系

```
ZclMltSrvOnlineMngr/
├── 核心管理文件
│   ├── SrvOnlineManager.h/.cpp           # 在线管理器主控制器
│   ├── SrvManagerDefine.h                # 常量和结构定义
│   └── ServerStatus.h/.cpp               # 服务器状态管理
│
├── 监控器文件
│   ├── SrvSelfStatusMonitor.h/.cpp       # 本地状态监控器
│   ├── SrvOtherStatusMonitor.h/.cpp      # 其他服务器状态监控
│   └── SrvHeartbeatMonitor.h/.cpp        # 心跳监控器
│
├── 切换处理器文件
│   ├── ISrvSwitchHandler.h/.cpp          # 切换处理器接口
│   ├── SingleSrvSwitchHandler.h/.cpp     # 单机切换处理器
│   ├── ASSrvSwitchHandler.h/.cpp         # 主备切换处理器
│   └── AASrvSwitchHandler.h/.cpp         # 双主切换处理器
│
└── 模块导出文件
    ├── ZxMultiSrvOnlineMng.h/.cpp        # 模块导出接口
    └── ZxMultiSrvOnlineMng_update_history.cpp  # 更新历史
```

## 文件详细分析

### 1. SrvManagerDefine.h - 常量和结构定义

#### 运行模式常量
```cpp
const int SRV_RUN_MODEL_SINGLE = 0;            // 单机模式
const int SRV_RUN_MODEL_ACTIVEANDSTANDBY = 1;  // 主备模式
const int SRV_RUN_MODEL_ACTIVEANDACTIVE = 2;   // 双主模式
```

#### 运行状态常量
```cpp
const int SRV_RUN_NOAVAILABLE = 0;  // 不可用
const int SRV_RUN_AVAILABLE = 1;    // 可用
```

#### 心跳状态常量
```cpp
const int SRV_HEARTBEAT_UNKNOWN = -1;   // 未知
const int SRV_HEARTBEAT_NORMAL = 0;     // 正常
const int SRV_HEARTBEAT_TIMEOUT = 1;    // 超时
```

#### ST_Server_Config - 服务器配置结构
```cpp
typedef struct _Server_Config {
    string strServerID;           // 服务器ID
    string strNetAddrA;          // A网地址
    string strNetBcastA;         // A网广播地址
    string strNetAddrB;          // B网地址
    string strNetBcastB;         // B网广播地址
    string strHeartbeatAddr;     // 心跳网地址
    string strHeartbeatBcast;    // 心跳网广播地址
    string strGatewayAddr;       // 数据网关地址
    int iSrvGroup;               // 服务器组号
    int iRunModel;               // 运行模式
    int iIfUseBakNet;            // 是否使用备用网络
    int iHeartbeatFlag;          // 心跳网络选择标志
    int iIfMonitorGateway;       // 是否监视网关
    int iStandardLoad;           // 负载基准分
    int iIfAcquireLoad;          // 是否采集负载
    int iPeriodHeartbeat;        // 心跳发送周期
    int iTimeoutHeartbeat;       // 心跳接收超时
    int iHeartbeatPort;          // 心跳广播端口
    stXJLoadAcquirer LAParam;    // 负载采集参数
    float fScopeLoad;            // 负载浮动范围
    string strReverse2;          // 备用字段（是否接管子站）
}
```

#### ST_Server_Status - 服务器状态结构
```cpp
typedef struct _Server_Status {
    CXJLock lockGuard;                    // 线程锁
    map<string, int> listStationStatus;   // 厂站状态列表
    string strServerID;                   // 服务器ID
    time_t tTimed;                        // 时间戳
    int iGroup;                          // 服务器组号
    int iRunStatus;                      // 运行状态
    int iOnlineStatus;                   // 在线状态（-1未知 0备用 1主用）
    int iLoadSoccer;                     // 负载评分
    int iHBStatus;                       // 心跳状态
    WORD wSerial;                        // 序列号
}
```

### 2. SrvOnlineManager.h - 在线管理器主控制器

#### 类定义
```cpp
class CSrvOnlineManager {
private:
    ST_Server_Config m_LocalConfig;               // 本地服务器配置
    ST_Server_Status m_LocalStatus;               // 本地服务器状态
    MAP_Server_Status m_mapServerStatus;          // 其他服务器状态映射表
    CMessageLog m_LogFile;                        // 日志文件
    CMessageLog m_MsgLog;                         // 消息日志
    CXJDBFacade* m_pDBFacade;                    // 数据库访问接口
    CHeartbeatWrapper* m_pHBWrapper;              // 心跳包装器
    CHeartbeatInterface* m_pHBInterface;          // 心跳接口
    CSrvSelfStatusMonitor* m_pSrvSelfStatusMonitor;   // 本地状态监控器
    CSrvHeartbeatMonitor* m_pSrvHBMonitor;        // 心跳监控器
    CServerSwitchHandler* m_pSrvSwitchHandler;    // 切换处理器
    map<PFUNONSRVSWITCH, void*> m_listOnSwitchCallback;  // 切换回调列表
    map<string,string> m_StationMaptoServer;      // 厂站-服务器映射
    bool m_b860Front;                             // 是否860前置
    string m_str860StnId;                         // 860厂站ID
};
```

#### 核心函数详解

##### StartSrvOnLineManager - 启动在线管理器
```cpp
int StartSrvOnLineManager(const stXJSrvOnlineManager& pManager);
```
- **功能**: 启动服务器在线管理功能
- **执行流程**:
  1. 初始化日志系统
  2. 初始化数据库访问
  3. 加载服务器配置
  4. 初始化心跳接口
  5. 启动状态监控器
  6. 启动心跳监控器
  7. 启动切换处理器
- **返回值**: 0成功，-1失败

##### StopSrvOnLineManager - 停止在线管理器
```cpp
int StopSrvOnLineManager();
```
- **功能**: 停止所有在线管理功能
- **执行流程**:
  1. 停止切换处理器
  2. 停止心跳监控
  3. 停止状态监控
  4. 释放心跳接口
  5. 断开数据库连接
  6. 清理资源

##### RegisterSrvSwitchCallback - 注册切换回调
```cpp
int RegisterSrvSwitchCallback(PFUNONSRVSWITCH pOnSrvSwitch, void* pParam);
```
- **功能**: 注册服务器切换的回调函数
- **参数**:
  - `pOnSrvSwitch`: 回调函数指针
  - `pParam`: 用户参数
- **使用场景**: 外部模块需要知道服务器切换事件

##### SetStationLoadStatus - 设置厂站加载状态
```cpp
int SetStationLoadStatus(stXJSubstationLoadStatus& PStatus);
```
- **功能**: 设置厂站的加载状态
- **参数**:
  - `PStatus`: 厂站加载状态信息
- **作用**: 更新厂站通信状态，影响切换决策

#### 初始化函数

##### InitServersConfig - 初始化服务器配置
```cpp
bool InitServersConfig();
```
- **功能**: 从数据库加载服务器配置
- **执行流程**:
  1. 查询tb_commu_server_config表
  2. 解析本地服务器配置
  3. 加载其他服务器配置
  4. 初始化服务器状态表

##### InitHBInterface - 初始化心跳接口
```cpp
bool InitHBInterface();
```
- **功能**: 初始化心跳通信接口
- **执行流程**:
  1. 创建心跳处理器实例
  2. 注册广播地址
  3. 启动心跳监听
  4. 注册回调函数

### 3. ISrvSwitchHandler.h - 切换处理器接口

#### 接口定义
```cpp
class CServerSwitchHandler {
protected:
    ST_Server_Status* m_pSrvStatus;        // 本地服务器状态
    MAP_Server_Status* m_pMapServerStatus; // 服务器状态映射表
    CLogFile& m_rLogFile;                  // 日志文件
    CXJDBFacade* m_pDBFacade;             // 数据库接口
    PFUNONSWITCH m_pOnSwitch;              // 切换回调
    void* m_pOnSwitchBindObject;           // 回调绑定对象
    vector<stXJSubstation> m_listSubstation; // 厂站列表
};
```

#### 核心虚函数
```cpp
virtual bool Start(ST_Server_Config& pLocalConfig) = 0;  // 启动切换处理
virtual void End() = 0;                                   // 停止切换处理
virtual void OnSwitch();                                  // 执行切换逻辑
```

#### 实现类
1. **SingleSrvSwitchHandler** - 单机模式切换处理器
2. **ASSrvSwitchHandler** - 主备模式切换处理器
3. **AASrvSwitchHandler** - 双主模式切换处理器

### 4. SrvSelfStatusMonitor.h - 本地状态监控器

#### 主要功能
- 监控本地服务器运行状态
- 检测网络连通性
- 监控网关状态
- 采集负载信息
- 更新服务器状态

#### 核心函数
```cpp
class CSrvSelfStatusMonitor {
public:
    bool Start(ST_Server_Config& config, ST_Server_Status& status);
    void End();
    
private:
    void MonitorThreadProc();     // 监控线程主函数
    bool CheckNetworkStatus();    // 检查网络状态
    bool CheckGatewayStatus();    // 检查网关状态
    int AcquireLoadInfo();        // 采集负载信息
    void UpdateServerStatus();    // 更新服务器状态
};
```

### 5. SrvHeartbeatMonitor.h - 心跳监控器

#### 主要功能
- 周期性发送心跳
- 监控其他服务器心跳
- 检测心跳超时
- 触发状态更新

#### 核心实现
```cpp
class CSrvHeartbeatMonitor {
private:
    void HeartbeatThreadProc() {
        while (!m_bExit) {
            // 1. 准备心跳信息
            StHeartbeatInfo info;
            PrepareHeartbeatInfo(info);
            
            // 2. 广播心跳
            m_pHBInterface->BroatcastHeartbeat(info);
            
            // 3. 检查其他服务器心跳超时
            CheckHeartbeatTimeout();
            
            // 4. 等待下次发送
            Sleep(m_iPeriodHeartbeat * 1000);
        }
    }
    
    void CheckHeartbeatTimeout() {
        time_t now = time(NULL);
        
        for (auto& server : m_mapServerStatus) {
            if (now - server.second->tTimed > m_iTimeoutHeartbeat) {
                // 心跳超时
                server.second->iHBStatus = SRV_HEARTBEAT_TIMEOUT;
                TriggerStatusChange(server.first);
            }
        }
    }
};
```

## 主备切换流程

### 1. 主备模式切换逻辑（ASSrvSwitchHandler）

```cpp
void ASSrvSwitchHandler::OnSwitch() {
    // 1. 检查本地服务器状态
    if (m_pSrvStatus->iRunStatus != SRV_RUN_AVAILABLE) {
        // 本地不可用，放弃所有厂站
        ReleaseAllStations();
        return;
    }
    
    // 2. 查找配对服务器
    ST_Server_Status* pPeerServer = FindPeerServer();
    
    if (!pPeerServer || pPeerServer->iHBStatus == SRV_HEARTBEAT_TIMEOUT) {
        // 对端离线或超时，接管所有厂站
        TakeOverAllStations();
        SetOnlineStatus(SRV_ONLINE_ACTIVE);
    } else if (pPeerServer->iOnlineStatus == SRV_ONLINE_ACTIVE) {
        // 对端是主机，本机为备机
        SetOnlineStatus(SRV_ONLINE_STANDBY);
    } else {
        // 负载均衡决策
        if (m_pSrvStatus->iLoadSoccer < pPeerServer->iLoadSoccer) {
            // 本机负载更低，成为主机
            TakeOverAllStations();
            SetOnlineStatus(SRV_ONLINE_ACTIVE);
        }
    }
    
    // 3. 触发切换回调
    NotifySwitchCallbacks();
}
```

### 2. 厂站断开自动切换流程

```cpp
void HandleStationDisconnect(const string& stationId) {
    // 1. 记录断开时间
    m_stationDisconnectTime[stationId] = time(NULL);
    
    // 2. 启动定时器
    StartStationTimer(stationId);
}

void OnStationTimer(const string& stationId) {
    time_t now = time(NULL);
    time_t disconnectTime = m_stationDisconnectTime[stationId];
    
    // 3. 检查是否达到切换时间（如5分钟）
    if (now - disconnectTime >= m_stationSwitchTimeout) {
        // 4. 通知对端服务器接管
        NotifyPeerToTakeover(stationId);
        
        // 5. 本地放弃管辖权
        ReleaseStation(stationId);
    }
}
```

### 3. 手动切换流程

```cpp
int OnManualSwitch(StHeartbeatInfo& info) {
    if (info.iType == HEARTBEAT_TYPE_OPERATION) {
        // 手动切换命令
        if (info.iOnlineStatus == SRV_ONLINE_ACTIVE) {
            // 要求成为主机
            TakeOverAllStations();
            SetOnlineStatus(SRV_ONLINE_ACTIVE);
        } else {
            // 要求成为备机
            ReleaseAllStations();
            SetOnlineStatus(SRV_ONLINE_STANDBY);
        }
        
        // 通知外部模块
        NotifySwitchCallbacks();
    }
    
    return 0;
}
```

## 使用示例

### 1. 基本使用流程
```cpp
// 1. 配置参数
stXJSrvOnlineManager config;
strcpy(config.chServerID, "SERVER001");
strcpy(config.szLog_path, "/var/log/online");
config.iLog_level = 3;
config.iLogDay = 30;

// 2. 启动在线管理器
int result = StartSrvOnLineManager(config);

// 3. 注册切换回调
RegisterSrvSwitchCallback(OnServerSwitch, this);

// 4. 设置厂站状态
stXJSubstationLoadStatus loadStatus;
loadStatus.strSubstationID = "STATION001";
loadStatus.iStatus = 1;  // 正常
SetStationLoadStatus(loadStatus);

// 回调函数实现
int OnServerSwitch(void* pParam, int status, vector<stXJSubstation>& stationList) {
    printf("服务器切换: 状态=%d, 厂站数=%d\n", status, stationList.size());
    
    // 处理厂站管辖权变化
    for (auto& station : stationList) {
        if (status == SRV_ONLINE_ACTIVE) {
            // 接管厂站
            StartStationCommunication(station.strSubstationID);
        } else {
            // 释放厂站
            StopStationCommunication(station.strSubstationID);
        }
    }
    
    return 0;
}
```

### 2. 配置数据库表

```sql
-- tb_commu_server_config 服务器配置表
CREATE TABLE tb_commu_server_config (
    server_id VARCHAR(40),        -- 服务器ID
    net_addr_a VARCHAR(20),       -- A网地址
    net_bcast_a VARCHAR(20),      -- A网广播地址
    net_addr_b VARCHAR(20),       -- B网地址  
    net_bcast_b VARCHAR(20),      -- B网广播地址
    heartbeat_addr VARCHAR(20),   -- 心跳网地址
    heartbeat_bcast VARCHAR(20),  -- 心跳网广播地址
    srv_group INT,                -- 服务器组号
    run_model INT,                -- 运行模式
    period_heartbeat INT,         -- 心跳周期(秒)
    timeout_heartbeat INT,        -- 心跳超时(秒)
    heartbeat_port INT,           -- 心跳端口
    station_timeout INT           -- 厂站断开超时(秒)
);

-- 示例配置
INSERT INTO tb_commu_server_config VALUES (
    'SERVER001',           -- 主服务器
    '192.168.1.10',       -- A网
    '192.168.1.255',      
    '192.168.2.10',       -- B网
    '192.168.2.255',
    '192.168.3.10',       -- 心跳网
    '192.168.3.255',
    1,                    -- 组1
    1,                    -- 主备模式
    10,                   -- 10秒心跳
    30,                   -- 30秒超时
    17255,                -- 端口
    300                   -- 5分钟厂站超时
);

INSERT INTO tb_commu_server_config VALUES (
    'SERVER002',           -- 备服务器
    '192.168.1.11',       
    '192.168.1.255',      
    '192.168.2.11',       
    '192.168.2.255',
    '192.168.3.11',       
    '192.168.3.255',
    1,                    -- 同组
    1,                    -- 主备模式
    10,                   
    30,                   
    17255,                
    300                   
);
```

## 设计特点

### 1. 灵活的运行模式
- 支持单机、主备、双主三种模式
- 运行模式可配置
- 支持在线切换

### 2. 完善的监控机制
- 本地状态监控
- 网络状态监控
- 心跳状态监控
- 负载监控

### 3. 智能切换策略
- 基于负载的切换
- 厂站断开自动切换
- 手动切换支持

### 4. 高可靠性设计
- 双网冗余
- 心跳超时检测
- 状态同步机制

## 注意事项

1. **网络配置**
   - 确保心跳网络畅通
   - 配置正确的广播地址
   - 防火墙开放UDP端口

2. **数据库配置**
   - 服务器必须配置在同一组
   - 运行模式必须匹配
   - 心跳参数要合理

3. **性能优化**
   - 合理设置心跳周期
   - 避免频繁切换
   - 监控系统负载

4. **故障处理**
   - 处理网络故障
   - 处理数据库异常
   - 日志记录完整
