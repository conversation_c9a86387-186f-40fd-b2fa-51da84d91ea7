# Dragon项目文档索引

## 概述
本文档为Dragon项目的完整文档索引，包含了所有模块的详细分析文档路径和简要说明。

## 项目总体文档

### 架构与流程
- **[Dragon项目总体架构与调用流程分析.md](./Dragon项目总体架构与调用流程分析.md)**
  - 系统整体架构图
  - 核心模块关系
  - 主要调用流程
  - 关键设计模式
  - 部署架构方案

## libapimngr - API管理库文档

### 接口定义
- **[libapimngr/接口文件详细分析.md](./libapimngr/接口文件详细分析.md)**
  - IZxBusSwap - 总线交换接口
  - IZxD5kPrimState - D5K主状态接口
  - IZxD5kModelSync - D5K模型同步接口

### API定义
- **[libapimngr/API定义文件详细分析.md](./libapimngr/API定义文件详细分析.md)**
  - ZxLibApi_Def_Heartbeat - 心跳定义
  - ZxLibApi_SrvOnlineMngr - 在线管理器API
  - ZxLibApi_LoadAcquirer - 负载采集器API
  - ZxLibApi_Def_MsgMgr - 消息管理器定义
  - ZxLibApi_SttpProxy - STTP代理API
  - ZxLibApi_SubStationInit - 子站初始化API

### 管理器实现
- **[libapimngr/管理器实现文件详细分析.md](./libapimngr/管理器实现文件详细分析.md)**
  - ZxLibMngr_DBAcess - 数据库访问管理器
  - ZxLibMngr_SttpMsgAnalyze - STTP消息分析器
  - ZxLibMngr_Heartbeat - 心跳管理器
  - ZxLibMngr_StatHisData - 历史数据统计管理器

### 加载器
- **[libapimngr/加载器文件详细分析.md](./libapimngr/加载器文件详细分析.md)**
  - ZxLoadBusSwapLib - 总线交换库加载器
  - ZxLoadD5kModelSyncLib - D5K模型同步库加载器
  - ZxLoadD5kPrimStateLib - D5K主状态库加载器

## module - 功能模块文档

### ZclMltSrvHbtMngr - 多服务器心跳管理器
- **[module/ZclMltSrvHbtMngr/多服务器心跳管理器详细分析.md](./module/ZclMltSrvHbtMngr/多服务器心跳管理器详细分析.md)**
  - HeartbeatBroadcaster - 心跳广播器
  - HeartbeatListener - 心跳监听器
  - HeartbeatManager - 心跳管理器

### ZclMltSrvOnlineMngr - 多服务器在线管理器
- **[module/ZclMltSrvOnlineMngr/多服务器在线管理器详细分析.md](./module/ZclMltSrvOnlineMngr/多服务器在线管理器详细分析.md)**
  - SrvOnlineManager - 服务器在线管理
  - ServerStatusMonitor - 服务器状态监控
  - SubstationOwnershipManager - 子站归属管理

## ZcsServer - 通信服务器文档

### common - 公共模块
- **[module/ZcsServer/common/通信服务器公共模块详细分析.md](./module/ZcsServer/common/通信服务器公共模块详细分析.md)**
  - CommuDef - 通信定义
  - ZxProtocolAPI - 协议接口
  - ZxSubscribeCenterAPI - 订阅中心接口
  - ZxDataHandlerAPI - 数据处理接口
  - ZxServerManagerAPI - 服务器管理接口
  - ZxObserver/ZxPublisher - 观察者模式实现
  - SttpMsgProductor - STTP消息生产者
  - XJMsgCaster - 消息广播器

### server - 服务器核心组件
- **[module/ZcsServer/server/通信服务器核心组件详细分析.md](./module/ZcsServer/server/通信服务器核心组件详细分析.md)**
  - 核心组件概述
  - 模块间交互关系

#### ZcsMain - 主服务进程
- **[module/ZcsServer/server/ZcsMain/主服务进程详细分析.md](./module/ZcsServer/server/ZcsMain/主服务进程详细分析.md)**
  - ServerEngine - 服务引擎
  - Main.cpp - 程序入口
  - Windows服务支持

#### ZcsCliMngr - 客户端管理器
- **[module/ZcsServer/server/ZcsCliMngr/客户端管理器详细分析.md](./module/ZcsServer/server/ZcsCliMngr/客户端管理器详细分析.md)**
  - ClientManager - 客户端管理
  - ZxClientSession - 客户端会话
  - 协议库动态加载

#### ZcsDataHdl - 数据处理中心
- **[module/ZcsServer/server/ZcsDataHdl/数据处理中心详细分析.md](./module/ZcsServer/server/ZcsDataHdl/数据处理中心详细分析.md)**
  - DataHandler - 数据处理器
  - 消息队列管理
  - 数据存储策略

#### ZcsSsbMngr - 订阅管理中心
- **[module/ZcsServer/server/ZcsSsbMngr/订阅管理中心详细分析.md](./module/ZcsServer/server/ZcsSsbMngr/订阅管理中心详细分析.md)**
  - SubscribeCenter - 订阅中心
  - 发布者管理
  - 观察者管理
  - 消息路由

#### ZcsSrvMngr - 服务器管理器
- **[module/ZcsServer/server/ZcsSrvMngr/服务器管理器详细分析.md](./module/ZcsServer/server/ZcsSrvMngr/服务器管理器详细分析.md)**
  - ServerManager - 服务器管理
  - 会话管理
  - 命令处理
  - STTP总线接口

#### ZcsFrontMgr/ZcsFrontSrv - 前置管理器和服务
- **[module/ZcsServer/server/ZcsFrontMgr和ZcsFrontSrv/前置管理器和前置服务详细分析.md](./module/ZcsServer/server/ZcsFrontMgr和ZcsFrontSrv/前置管理器和前置服务详细分析.md)**
  - 前置管理器 - 多进程管理
  - 前置服务 - 单进程服务
  - 进程监控与恢复

### pro - 协议实现模块
- **[module/ZcsServer/pro/协议实现模块详细分析.md](./module/ZcsServer/pro/协议实现模块详细分析.md)**
  - 协议分类概述
  - 协议转换机制
  - 协议扩展机制

#### ZcsPro103SrvFlow - 103服务端流程
- **[module/ZcsServer/pro/ZcsPro103SrvFlow/103服务端流程详细分析.md](./module/ZcsServer/pro/ZcsPro103SrvFlow/103服务端流程详细分析.md)**
  - ZxPro103ServerFlow - 服务端流程入口
  - ZxPro103ServerWay - 服务端处理方式
  - Zx103ServerSttpHandler - STTP处理器
  - 会话管理与ASDU处理

#### ZcsPro61850Cli - 61850客户端
- **[module/ZcsServer/pro/ZcsPro61850Cli/61850客户端详细分析.md](./module/ZcsServer/pro/ZcsPro61850Cli/61850客户端详细分析.md)**
  - MMS客户端实现
  - SCL文件处理
  - 数据模型管理
  - 报告服务实现

## 文档使用说明

### 阅读顺序建议

#### 初学者路径
1. 先阅读 **Dragon项目总体架构与调用流程分析**，了解整体架构
2. 阅读 **通信服务器核心组件详细分析**，理解核心模块
3. 根据兴趣选择具体模块深入学习

#### 开发者路径
1. 根据开发任务，直接查找相关模块文档
2. 关注接口定义和API文档
3. 参考具体实现文档进行开发

#### 运维人员路径
1. 重点关注 **主服务进程详细分析**
2. 了解 **多服务器心跳管理器** 和 **在线管理器**
3. 查看部署架构和配置示例

### 文档特点

1. **详细的代码分析**: 每个文档都包含关键代码片段和详细说明
2. **执行流程图**: 使用流程图清晰展示程序执行过程
3. **配置示例**: 提供实际可用的配置文件示例
4. **设计模式说明**: 解释使用的设计模式及其原因
5. **性能优化建议**: 提供性能调优的具体建议
6. **使用注意事项**: 列出常见问题和注意事项

### 文档维护

- **更新频率**: 随代码更新同步维护
- **版本对应**: 文档版本与代码版本保持一致
- **问题反馈**: 发现文档问题请及时反馈

## 快速索引

### 按功能查找

#### 网络通信
- [协议实现模块详细分析](./module/ZcsServer/pro/协议实现模块详细分析.md)
- [103服务端流程详细分析](./module/ZcsServer/pro/ZcsPro103SrvFlow/103服务端流程详细分析.md)
- [61850客户端详细分析](./module/ZcsServer/pro/ZcsPro61850Cli/61850客户端详细分析.md)

#### 数据处理
- [数据处理中心详细分析](./module/ZcsServer/server/ZcsDataHdl/数据处理中心详细分析.md)
- [订阅管理中心详细分析](./module/ZcsServer/server/ZcsSsbMngr/订阅管理中心详细分析.md)

#### 高可用
- [多服务器心跳管理器详细分析](./module/ZclMltSrvHbtMngr/多服务器心跳管理器详细分析.md)
- [多服务器在线管理器详细分析](./module/ZclMltSrvOnlineMngr/多服务器在线管理器详细分析.md)

#### 管理功能
- [客户端管理器详细分析](./module/ZcsServer/server/ZcsCliMngr/客户端管理器详细分析.md)
- [服务器管理器详细分析](./module/ZcsServer/server/ZcsSrvMngr/服务器管理器详细分析.md)

### 按模块查找

#### 基础库
- libapimngr目录下所有文档

#### 服务模块
- module/ZclMltSrvHbtMngr目录下文档
- module/ZclMltSrvOnlineMngr目录下文档

#### 核心服务
- module/ZcsServer/server目录下所有文档

#### 协议实现
- module/ZcsServer/pro目录下所有文档

## 总结

Dragon项目文档体系完整覆盖了：
- **30+** 个核心模块
- **100+** 个关键类和接口
- **500+** 个重要函数
- **完整的执行流程分析**
- **详细的配置示例**
- **全面的设计说明**

这些文档为项目的开发、维护、部署和优化提供了全面的技术支持。
