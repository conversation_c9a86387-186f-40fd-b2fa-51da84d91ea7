# libapimngr - API管理库完整文件列表

## 目录结构
```
libapimngr/
├── 接口定义文件 (Interface)
│   ├── I_ZclDbAccess.h                     # 数据库访问接口
│   ├── IDataHandle.h                       # 数据处理接口
│   ├── IFileNotify.h                       # 文件通知接口
│   ├── ISttpHandle.h                       # STTP处理接口
│   ├── IZasStatCk.h                        # 统计检查接口
│   ├── IZasStatDiagn.h                     # 统计诊断接口
│   ├── IZasStatFlt.h                       # 故障统计接口
│   ├── IZasStatInsp.h                      # 巡视统计接口
│   ├── IZasXmlTransToHdr.h                 # XML转换接口
│   ├── IZclDMail.h                         # 邮件发送接口
│   ├── IZdsDeleteDbInfo.h                  # 数据库删除接口
│   ├── IZdsUfvlsDaHdl.h                    # UFVLS数据处理接口
│   ├── IZdsUfvlsDaHdlEx.h                  # UFVLS数据处理扩展接口
│   ├── IZwsWebDataSysCli.h                 # Web数据系统客户端接口
│   ├── IZxBusSwap.h                        # 总线交换接口
│   ├── IZxD5kModelSync.h                   # D5K模型同步接口
│   └── IZxD5kPrimState.h                   # D5K主状态接口
│
├── API定义文件 (API Definition)
│   ├── ZxLibApi_CvtStnXmlToDb.h/.cpp       # 厂站XML转数据库API
│   ├── ZxLibApi_Def_CimEExp.h              # CIM-E导出定义
│   ├── ZxLibApi_Def_Heartbeat.h            # 心跳定义
│   ├── ZxLibApi_Def_MsgMgr.h               # 消息管理器定义
│   ├── ZxLibApi_Def_MsgMonitor.h           # 消息监控定义
│   ├── ZxLibApi_Def_RelayAnalyze.h         # 继电器分析定义
│   ├── ZxLibApi_Def_WebProvider.h          # Web服务提供者定义
│   ├── ZxLibApi_Heartbeat.h                # 心跳API
│   ├── ZxLibApi_LoadAcquirer.h             # 负载获取API
│   ├── ZxLibApi_OscilloAnalyst.h           # 示波分析API
│   ├── ZxLibApi_SrvAccepter.h              # 服务器接受器API
│   ├── ZxLibApi_SrvOnlineMngr.h            # 服务器在线管理API
│   ├── ZxLibApi_SttpProxy.h                # STTP代理API
│   ├── ZxLibApi_SubStationInit.h           # 子站初始化API
│   └── ZxLibApi_SysParamAcquire.h          # 系统参数获取API
│
├── 管理器实现文件 (Manager Implementation)
│   ├── ZasLibMngr_DBModelExp.h/.cpp        # 数据库模型导出管理器
│   ├── ZasLibMngr_DBModelExpForRTDB.h/.cpp # 实时库模型导出管理器
│   ├── ZasLibmngr_HexDiagAnalyze.h/.cpp    # 六角图分析管理器
│   ├── ZasLibmngr_StatCk.h/.cpp            # 统计检查管理器
│   ├── ZasLibmngr_StatDiagn.h/.cpp         # 统计诊断管理器
│   ├── ZasLibmngr_StatFlt.h/.cpp           # 故障统计管理器
│   ├── ZasLibmngr_StatInsp.h/.cpp          # 巡视统计管理器
│   ├── ZclLibmngr_DMail.h/.cpp             # 邮件管理器
│   ├── ZclLibmngr_Gbk2Utf8.h/.cpp          # GBK转UTF8管理器
│   ├── ZdsLibMngr_daHdl.h/.cpp             # 数据处理管理器
│   ├── ZdsLibMngr_DeleteInfo.h/.cpp        # 删除信息管理器
│   ├── ZdsLibMngr_FileNotify.h/.cpp        # 文件通知管理器
│   ├── ZdsLibMngr_SttpHdl.h/.cpp           # STTP处理管理器
│   ├── ZwsLibMngr_WebDataSysCli.h/.cpp     # Web数据系统客户端管理器
│   ├── ZxLibMngr_DBAcess.h/.cpp            # 数据库访问管理器
│   ├── ZxLibMngr_DBAccessEx.h/.cpp         # 数据库访问扩展管理器
│   ├── ZxLibMngr_DbAgent.h/.cpp            # 数据库代理管理器
│   ├── ZxLibMngr_FaultpktAnalyze.h/.cpp    # 故障包分析管理器
│   ├── ZxLibMngr_Gbk2Utf8.h/.cpp           # GBK转UTF8管理器
│   ├── ZxLibMngr_GetDataType.h/.cpp        # 数据类型获取管理器
│   ├── ZxLibMngr_HarzardAnalyze.h/.cpp     # 隐患分析管理器
│   ├── ZxLibMngr_Heartbeat.h/.cpp          # 心跳管理器
│   ├── ZxLibMngr_JvmAdapterMngr.h/.cpp     # JVM适配器管理器
│   ├── ZxLibMngr_JvmRTDBAdapterMngr.h/.cpp # JVM实时库适配器管理器
│   ├── ZxLibMngr_MsgMonitor.h/.cpp         # 消息监控管理器
│   ├── ZxLibMngr_NwRlFltQuery.h/.cpp       # 南网真实故障查询管理器
│   ├── ZxLibMngr_OscAnalyze.h/.cpp         # 示波分析管理器
│   ├── ZxLibMngr_SectionData.h/.cpp        # 断面数据管理器
│   ├── ZxLibMngr_StatHis.h/.cpp            # 历史统计管理器
│   ├── ZxLibMngr_StatHisData.h/.cpp        # 历史统计数据管理器
│   ├── ZxLibMngr_SttpMsgAnalyze.h/.cpp     # STTP消息分析管理器
│   ├── ZxLibMngr_SvgEdit.h/.cpp            # SVG编辑管理器
│   ├── ZxLibMngr_WaveCfgBindDevice.h/.cpp  # 波形配置绑定设备管理器
│   ├── ZxLibMngr_XmlToHdr.h/.cpp           # XML转头文件管理器
│   ├── ZxLibMngr_ZdsUfvlsDaHdl.h/.cpp      # UFVLS数据处理管理器
│   ├── ZxLibMngr_ZdsUfvlsDaHdlEx.h/.cpp    # UFVLS数据处理扩展管理器
│   └── ZxLibmngr_zipc.h/.cpp               # ZIPC管理器
│
├── 加载器文件 (Loader)
│   ├── ZxLoadBusSwapLib.h/.cpp             # 总线交换库加载器
│   ├── ZxLoadD5kModelSyncLib.h/.cpp        # D5K模型同步库加载器
│   ├── ZxLoadD5kPrimStateLib.h/.cpp        # D5K主状态库加载器
│   └── load_plm12rule_anz_lib.h/.cpp       # PLM12规则分析库加载器
│
├── 工具类文件 (Utility)
│   ├── ZxSttpMsgMaker.h/.cpp               # STTP消息制作器
│   ├── ZxUPtId.h/.cpp                      # PT ID工具
│   ├── ZxD5kMsgBusAgent.h/.cpp             # D5K消息总线代理
│   ├── rttb_data_save_struct.h             # 实时库数据保存结构
│   ├── zx_ied_info_dic_define.h            # IED信息字典定义
│   ├── zipc_I.h                            # ZIPC接口定义
│   ├── ZxlibMngr_CheckGrade.h/.cpp         # 检查等级管理器
│   └── newgeneration/                      # 新一代平台
│       ├── rttb_data_save_struct.h
│       └── ZxLibMngr_DBAcess.h/.cpp
│
└── 其他文件
    └── Libapimngr_update_history.cpp       # 更新历史记录
```

## 文件分类统计
- 接口定义文件(I*.h): 17个
- API定义文件(ZxLibApi_*.h): 14个  
- 管理器实现文件(*Mngr*.h/.cpp): 70+个
- 加载器文件(ZxLoad*.h/.cpp): 8个
- 工具类文件: 10+个
- 总计: 约130个文件

## 命名规范说明
- **I前缀**: 纯接口定义
- **Zas前缀**: 在线监测与故障诊断系统(Z-Automation System)
- **Zcl前缀**: 通信系统(Z-Communication Layer)
- **Zds前缀**: 数据服务(Z-Data Service)
- **Zws前缀**: Web服务(Z-Web Service)
- **Zx前缀**: 通用系统(Z-eXtended)

每个文件都包含了特定功能的接口定义或实现，构成了整个Dragon系统的API基础设施。
