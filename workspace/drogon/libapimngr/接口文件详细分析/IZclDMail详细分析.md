# IZclDMail.h - 邮件发送接收接口详细分析

## 文件概述
- **文件名**: IZclDMail.h
- **作者**: 李金梅
- **版本**: 1.0.1
- **功能**: 提供邮件发送接收功能的接口定义
- **创建日期**: 2015/05/05
- **多线程安全**: 是
- **异常安全**: 是

## 常量定义

### EH文件名
```cpp
const string ZCL_EH_FILE_NAME = "SECDEV_MONITOR";
```
- **说明**: 默认的EH（邮件头）文件标识名称
- **用途**: 用于标识安全设备监控相关的邮件

## 数据结构定义

### DMAIL_LOGIN_PARAM - 邮件服务登录参数
```cpp
typedef struct _DMAIL_LOGIN_PARAM {
    string usrname;          // 用户名
    string passwd;           // 密码
    string servip;           // 服务器IP地址
    unsigned short port;     // 端口号
    string strlogpath;       // 日志根路径
    int nloglevel;          // 日志等级
    long nlogsize;          // 日志大小
    int nlogday;            // 日志保存天数
}
```

**默认值**：
- `usrname`: "国调1.自动化"
- `passwd`: "zdh,zdh"
- `servip`: "**************"
- `port`: 5677
- `strlogpath`: "../Zx_Log/DMail"
- `nloglevel`: 3
- `nlogsize`: 10MB (10*1024*1024)
- `nlogday`: 30天

### FILE_INFO - 文件信息
```cpp
typedef struct _FILE_INFO {
    int ntype;               // 文件类型：1=录波文件，其他=其他文件
    std::string strfilename; // 文件名
}
```

**默认值**：
- `ntype`: 1 (录波文件)
- `strfilename`: 空字符串

### ZCLEHINFOR - EH头文件信息
```cpp
typedef struct _ZCLEHINFOR {
    std::string ehfilename;               // EH文件名
    std::string mark;                     // 标识
    std::string sendAddr;                 // 发送地址
    std::vector<std::string> recvAddrs;   // 接收地址列表
    std::string type;                     // 传输类型
    std::string content;                  // 内容
    std::vector<FILE_INFO> attachs;       // 附件列表
    std::string status;                   // 邮件状态(EHR文件使用)
    int nflag;                           // 0:系统自动生成文件名 1:用户指定文件名
}
```

**默认值**：
- `type`: "0"
- `nflag`: 0
- `mark`: ZCL_EH_FILE_NAME ("SECDEV_MONITOR")

## 句柄定义

### ZclDmailHandle - 邮件连接句柄
```cpp
#ifdef OS_WINDOWS
#define ZclDmailHandle SOCKET
#else
typedef int ZclDmailHandle;
#endif
```
- **说明**: 跨平台的套接字句柄定义
- Windows使用SOCKET类型
- Linux使用int类型

## 核心接口函数详解

### 1. zcl_dmail_login - 登录邮件服务器
```cpp
virtual ZclDmailHandle zcl_dmail_login(const DMAIL_LOGIN_PARAM &login_param, 
                                       std::string& errdesc) = 0;
```
- **功能**: 登录DMail服务器，进行用户名和密码验证
- **参数**:
  - `login_param`: 登录参数（用户名、密码、服务器信息等）
  - `errdesc`: 错误描述信息输出
- **返回**: 
  - >0: 成功，返回连接句柄
  - <=0: 失败
- **执行流程**:
  1. 创建套接字连接
  2. 连接到指定的服务器和端口
  3. 发送认证信息
  4. 等待服务器响应
  5. 返回连接句柄或错误

### 2. zcl_dmail_logout - 退出登录
```cpp
virtual void zcl_dmail_logout(ZclDmailHandle& sockfd) = 0;
```
- **功能**: 退出登录，释放连接资源
- **参数**:
  - `sockfd`: 邮件连接句柄
- **执行流程**:
  1. 发送退出命令
  2. 关闭套接字
  3. 清理相关资源

### 3. zcl_dmail_send - 发送邮件
```cpp
virtual int zcl_dmail_send(ZclDmailHandle& sockfd, 
                          ZCLEHINFOR& ehinfo, 
                          const std::string& path, 
                          std::string& errdesc, 
                          bool bRemoveEhfile = true) = 0;
```
- **功能**: 发送邮件及附件
- **参数**:
  - `sockfd`: 连接句柄
  - `ehinfo`: EH头文件信息
  - `path`: 本地创建的EH文件存储路径
  - `errdesc`: 错误描述输出
  - `bRemoveEhfile`: 是否删除创建的EH文件（默认true）
- **返回**: 0成功，其他失败
- **执行流程**:
  1. 创建EH头文件
  2. 填充邮件信息
  3. 打包附件文件
  4. 发送到邮件服务器
  5. 等待发送确认
  6. 清理临时文件（如果需要）

### 4. zcl_dmail_recv - 接收邮件
```cpp
virtual int zcl_dmail_recv(ZclDmailHandle& sockfd, 
                          const std::string& path, 
                          std::string& errdesc, 
                          bool bRemoveEhfile = true) = 0;
```
- **功能**: 接收邮件及附件
- **参数**:
  - `sockfd`: 连接句柄
  - `path`: 接收附件的存储路径
  - `errdesc`: 错误描述输出
  - `bRemoveEhfile`: 是否删除EH文件（默认true）
- **返回**: 0成功，其他失败
- **执行流程**:
  1. 查询新邮件
  2. 下载EH头文件
  3. 解析邮件信息
  4. 下载附件文件
  5. 存储到指定路径
  6. 清理临时文件（如果需要）

## 实例创建和销毁

### create_inst - 创建实例
```cpp
IZclDMail* create_inst();
```
- **功能**: 创建邮件客户端实例
- **返回**: IZclDMail接口指针

### destroy_inst - 销毁实例
```cpp
int destroy_inst(IZclDMail *pInst);
```
- **功能**: 销毁邮件客户端实例
- **参数**: 
  - `pInst`: 要销毁的实例指针
- **返回**: 0成功，1失败

## 使用示例

### 基本邮件发送流程
```cpp
// 1. 创建实例
IZclDMail* pDMail = create_inst();

// 2. 配置登录参数
DMAIL_LOGIN_PARAM loginParam;
loginParam.usrname = "user1";
loginParam.passwd = "password123";
loginParam.servip = "*************";
loginParam.port = 5677;
loginParam.strlogpath = "/var/log/dmail";

// 3. 登录服务器
string errorDesc;
ZclDmailHandle handle = pDMail->zcl_dmail_login(loginParam, errorDesc);
if (handle <= 0) {
    printf("登录失败: %s\n", errorDesc.c_str());
    return;
}

// 4. 准备邮件信息
ZCLEHINFOR ehInfo;
ehInfo.mark = "FAULT_RECORD";
ehInfo.sendAddr = "Station001";
ehInfo.recvAddrs.push_back("ControlCenter");
ehInfo.recvAddrs.push_back("BackupCenter");
ehInfo.type = "1";  // 重要邮件
ehInfo.content = "故障录波文件传输";

// 5. 添加附件
FILE_INFO fileInfo;
fileInfo.ntype = 1;  // 录波文件
fileInfo.strfilename = "fault_20240101_120000.dat";
ehInfo.attachs.push_back(fileInfo);

// 6. 发送邮件
string sendPath = "/data/outgoing";
if (pDMail->zcl_dmail_send(handle, ehInfo, sendPath, errorDesc) == 0) {
    printf("邮件发送成功\n");
} else {
    printf("发送失败: %s\n", errorDesc.c_str());
}

// 7. 退出登录
pDMail->zcl_dmail_logout(handle);

// 8. 销毁实例
destroy_inst(pDMail);
```

### 邮件接收示例
```cpp
class MailReceiver {
private:
    IZclDMail* m_pDMail;
    ZclDmailHandle m_handle;
    bool m_running;
    
public:
    bool Initialize() {
        m_pDMail = create_inst();
        
        DMAIL_LOGIN_PARAM param;
        // 设置参数...
        
        string error;
        m_handle = m_pDMail->zcl_dmail_login(param, error);
        return (m_handle > 0);
    }
    
    void StartReceiving() {
        m_running = true;
        
        while (m_running) {
            string recvPath = "/data/incoming";
            string error;
            
            // 接收邮件
            if (m_pDMail->zcl_dmail_recv(m_handle, recvPath, error) == 0) {
                // 处理接收到的文件
                ProcessReceivedFiles(recvPath);
            }
            
            // 等待一段时间再检查
            Sleep(10000);  // 10秒
        }
    }
    
    void ProcessReceivedFiles(const string& path) {
        // 扫描接收目录
        // 解析EH文件
        // 处理附件
        printf("处理接收到的文件: %s\n", path.c_str());
    }
    
    void Cleanup() {
        m_running = false;
        if (m_handle > 0) {
            m_pDMail->zcl_dmail_logout(m_handle);
        }
        destroy_inst(m_pDMail);
    }
};
```

### 批量文件传输示例
```cpp
class BulkFileTransfer {
public:
    void TransferMultipleFiles(vector<string>& fileList) {
        IZclDMail* pDMail = create_inst();
        
        // 登录
        DMAIL_LOGIN_PARAM param;
        ConfigureLoginParam(param);
        
        string error;
        ZclDmailHandle handle = pDMail->zcl_dmail_login(param, error);
        if (handle <= 0) return;
        
        // 分批发送文件
        const int BATCH_SIZE = 10;  // 每批10个文件
        ZCLEHINFOR ehInfo;
        
        for (size_t i = 0; i < fileList.size(); i++) {
            FILE_INFO fileInfo;
            fileInfo.ntype = 1;
            fileInfo.strfilename = fileList[i];
            ehInfo.attachs.push_back(fileInfo);
            
            // 达到批次大小或最后一个文件
            if (ehInfo.attachs.size() >= BATCH_SIZE || 
                i == fileList.size() - 1) {
                
                // 设置邮件信息
                ehInfo.mark = "BATCH_TRANSFER";
                ehInfo.sendAddr = "LocalStation";
                ehInfo.recvAddrs.push_back("RemoteCenter");
                ehInfo.content = FormatBatchInfo(i, ehInfo.attachs.size());
                
                // 发送批次
                if (pDMail->zcl_dmail_send(handle, ehInfo, 
                                          "/data/batch", error) != 0) {
                    LogError("批次发送失败: %s", error.c_str());
                }
                
                // 清空附件列表，准备下一批
                ehInfo.attachs.clear();
            }
        }
        
        pDMail->zcl_dmail_logout(handle);
        destroy_inst(pDMail);
    }
};
```

### 邮件状态监控示例
```cpp
class MailMonitor {
private:
    struct MailRecord {
        string ehfilename;
        string status;
        time_t sendTime;
        vector<string> receivers;
    };
    
    map<string, MailRecord> m_mailRecords;
    
public:
    void SendWithTracking(IZclDMail* pDMail, ZclDmailHandle handle,
                         ZCLEHINFOR& ehInfo) {
        // 生成唯一的邮件ID
        ehInfo.ehfilename = GenerateMailId();
        ehInfo.nflag = 1;  // 用户指定文件名
        
        // 记录发送信息
        MailRecord record;
        record.ehfilename = ehInfo.ehfilename;
        record.status = "SENDING";
        record.sendTime = time(NULL);
        record.receivers = ehInfo.recvAddrs;
        m_mailRecords[ehInfo.ehfilename] = record;
        
        // 发送邮件
        string error;
        if (pDMail->zcl_dmail_send(handle, ehInfo, 
                                   "/data/tracked", error) == 0) {
            m_mailRecords[ehInfo.ehfilename].status = "SENT";
        } else {
            m_mailRecords[ehInfo.ehfilename].status = "FAILED";
        }
    }
    
    void CheckMailStatus() {
        for (auto& pair : m_mailRecords) {
            MailRecord& record = pair.second;
            
            // 检查超时
            if (record.status == "SENT") {
                time_t now = time(NULL);
                if (now - record.sendTime > 3600) {  // 1小时超时
                    record.status = "TIMEOUT";
                    LogWarning("邮件超时: %s", record.ehfilename.c_str());
                }
            }
        }
    }
};
```

## EH文件格式说明

### EH文件结构
```
[HEADER]
MARK=SECDEV_MONITOR
SEND_ADDR=Station001
RECV_ADDR=Center001,Center002
TYPE=1
CONTENT=故障录波数据

[FILES]
FILE1=fault_001.dat,1
FILE2=fault_002.dat,1

[STATUS]
CREATE_TIME=2024-01-01 12:00:00
SEND_TIME=2024-01-01 12:00:05
STATUS=SENT
```

## 设计特点

### 1. 简单易用
- 清晰的接口定义
- 直观的参数结构
- 完整的错误信息

### 2. 灵活性
- 支持多接收方
- 可选的文件清理
- 自定义EH文件名

### 3. 可靠性
- 连接状态管理
- 错误恢复机制
- 文件完整性保证

### 4. 扩展性
- 支持多种文件类型
- 预留字段设计
- 状态跟踪机制

## 注意事项

1. **连接管理**
   - 登录成功后保持连接
   - 长时间空闲可能断开
   - 需要重连机制

2. **文件处理**
   - 确保文件路径有效
   - 注意文件大小限制
   - 处理文件权限问题

3. **错误处理**
   - 检查所有返回值
   - 保存错误描述信息
   - 实现重试机制

4. **性能优化**
   - 批量发送文件
   - 避免频繁登录
   - 合理设置超时时间

5. **安全考虑**
   - 加密敏感信息
   - 验证文件来源
   - 限制文件类型

6. **资源清理**
   - 正确调用logout
   - 清理临时文件
   - 释放内存资源
