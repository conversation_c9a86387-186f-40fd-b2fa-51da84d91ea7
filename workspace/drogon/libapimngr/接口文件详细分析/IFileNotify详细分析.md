# IFileNotify.h - 文件通知接口详细分析

## 文件概述
- **文件名**: IFileNotify.h
- **作者**: 李金梅  
- **版本**: 1.0.0
- **功能**: 文件监视和通知接口，支持多种文件系统事件
- **创建日期**: 2015/04/10
- **多线程安全**: 是
- **异常安全**: 是

## 文件事件定义

### 基础事件类型
```cpp
#define ZDS_IN_ACCESS        0x00000001  // 文件被访问
#define ZDS_IN_MODIFY        0x00000002  // 文件被修改
#define ZDS_IN_ATTRIB        0x00000004  // 元数据改变
#define ZDS_IN_CLOSE_WRITE   0x00000008  // 可写文件被关闭
#define ZDS_IN_CLOSE_NOWRITE 0x00000010  // 不可写文件关闭
#define ZDS_IN_OPEN          0x00000020  // 文件被打开
#define ZDS_IN_MOVED_FROM    0x00000040  // 文件被移出
#define ZDS_IN_MOVED_TO      0x00000080  // 文件被移入
#define ZDS_IN_CREATE        0x00000100  // 子文件被创建
#define ZDS_IN_DELETE        0x00000200  // 子文件被删除
#define ZDS_IN_DELETE_SELF   0x00000400  // 自身被删除
#define ZDS_IN_MOVE_SELF     0x00000800  // 自身被移动
```

### 系统事件类型
```cpp
#define ZDS_IN_UNMOUNT       0x00002000  // 文件系统被卸载
#define ZDS_IN_Q_OVERFLOW    0x00004000  // 事件队列溢出
#define ZDS_IN_IGNORED       0x00008000  // 文件被忽略
```

### 辅助事件定义
```cpp
#define ZDS_IN_CLOSE         (ZDS_IN_CLOSE_WRITE | ZDS_IN_CLOSE_NOWRITE)  // 关闭事件
#define ZDS_IN_MOVE          (ZDS_IN_MOVED_FROM | ZDS_IN_MOVED_TO)        // 移动事件
```

### 特殊标志
```cpp
#define ZDS_IN_ONLYDIR       0x01000000  // 仅监视目录
#define ZDS_IN_DONT_FOLLOW   0x02000000  // 不跟随符号链接
#define ZDS_IN_MASK_ADD      0x20000000  // 添加到已存在的监视掩码
#define ZDS_IN_ISDIR         0x40000000  // 事件发生在目录上
#define ZDS_IN_ONESHOT       0x80000000  // 只发送一次事件
```

### 预定义事件集合
```cpp
// 所有事件
#define ZDS_IN_ALL_EVENTS    (ZDS_IN_ACCESS | ZDS_IN_MODIFY | ZDS_IN_ATTRIB | 
                             ZDS_IN_CLOSE_WRITE | ZDS_IN_CLOSE_NOWRITE | 
                             ZDS_IN_OPEN | ZDS_IN_MOVED_FROM | ZDS_IN_MOVED_TO | 
                             ZDS_IN_DELETE | ZDS_IN_CREATE | ZDS_IN_DELETE_SELF | 
                             ZDS_IN_MOVE_SELF)

// 泽鑫系统需要的四类事件
#define ZDS_IN_ZX_ALL_EVENTS (ZDS_IN_CLOSE_WRITE | ZDS_IN_MOVED_TO | 
                             ZDS_IN_DELETE | ZDS_IN_CREATE)
```

## 数据结构定义

### FILE_MONITOR_SET - 文件监视配置
```cpp
typedef struct _file_monitor_set {
    vector<string> vMonitor_Path;  // 需要监视的目录列表
    bool bMonitorChild;           // 是否监视子目录
    int nMonitorEvent;            // 监视事件类型
    string strLogPath;            // 日志文件根路径
    int nLogLevel;                // 日志等级
    int nLogDays;                 // 日志保存天数
    int nTimeOut;                 // 队列中文件最大保留时间(分钟)
}
```

**默认值说明**：
- `bMonitorChild`: false (不监视子目录)
- `nMonitorEvent`: ZDS_IN_CREATE (仅监视创建事件)
- `nLogLevel`: 3 (INFO级别)
- `nLogDays`: 30天
- `nTimeOut`: 3600分钟 (60小时)

## 回调函数定义

### PFUNONFNOTIRY - 文件通知回调
```cpp
typedef int (*PFUNONFNOTIRY)(void* pParam, const string &strFilefullname, void* pReserved);
```
- **功能**: 文件事件发生时的回调函数
- **参数**:
  - `pParam`: 用户自定义参数
  - `strFilefullname`: 触发事件的文件完整路径
  - `pReserved`: 保留参数，用于扩展
- **返回**: 0成功，其他失败

## 核心接口函数详解

### 1. Start - 启动文件监视
```cpp
virtual int Start(const FILE_MONITOR_SET &fileMonitor) = 0;
```
- **功能**: 启动文件监视功能
- **参数**:
  - `fileMonitor`: 文件监视参数配置
- **返回**: 0成功，1失败
- **执行流程**:
  1. 验证监视路径有效性
  2. 创建inotify实例（Linux）或目录监视器（Windows）
  3. 添加监视路径和事件掩码
  4. 启动事件监听线程
  5. 初始化事件队列
- **使用示例**:
```cpp
FILE_MONITOR_SET monitorSet;
monitorSet.vMonitor_Path.push_back("/data/incoming");
monitorSet.vMonitor_Path.push_back("/data/processing");
monitorSet.bMonitorChild = true;
monitorSet.nMonitorEvent = ZDS_IN_CREATE | ZDS_IN_CLOSE_WRITE;
monitorSet.strLogPath = "/var/log/filenotify";
monitorSet.nLogLevel = 3;
monitorSet.nTimeOut = 120; // 2小时

if (pFileNotify->Start(monitorSet) == 0) {
    printf("文件监视启动成功\n");
}
```

### 2. RegCallBak - 注册回调函数
```cpp
virtual int RegCallBak(void * pRegObj, PFUNONFNOTIRY pCallBack) = 0;
```
- **功能**: 注册文件通知回调函数
- **参数**:
  - `pRegObj`: 注册对象指针
  - `pCallBack`: 回调函数指针
- **返回**: 0成功，1失败
- **说明**:
  - 可以注册多个回调函数
  - 事件发生时按注册顺序调用
  - 回调函数应快速返回，避免阻塞
- **使用示例**:
```cpp
// 回调函数实现
int OnFileNotify(void* pParam, const string& filename, void* pReserved) {
    MyClass* pThis = (MyClass*)pParam;
    printf("文件事件: %s\n", filename.c_str());
    
    // 处理文件
    pThis->ProcessFile(filename);
    
    return 0;
}

// 注册回调
pFileNotify->RegCallBak(this, OnFileNotify);
```

### 3. Stop - 停止文件监视
```cpp
virtual int Stop() = 0;
```
- **功能**: 停止文件监视功能
- **返回**: 0成功，1失败
- **执行流程**:
  1. 停止事件监听线程
  2. 清理未处理的事件队列
  3. 移除所有监视路径
  4. 释放系统资源
  5. 清理回调函数列表

## 实例创建和销毁

### create_inst_filenotify - 创建实例
```cpp
IFileNotify* create_inst_filenotify();
```
- **功能**: 创建文件通知接口实例
- **返回**: 接口实例指针

### destroy_inst_filenotify - 销毁实例
```cpp
int destroy_inst_filenotify(IFileNotify *pInst);
```
- **功能**: 销毁文件通知接口实例
- **参数**:
  - `pInst`: 要销毁的实例指针
- **返回**: 0成功，1失败

## 使用场景和示例

### 1. 监视文件上传目录
```cpp
class FileUploadMonitor {
private:
    IFileNotify* m_pFileNotify;
    
public:
    void StartMonitor() {
        // 创建实例
        m_pFileNotify = create_inst_filenotify();
        
        // 配置监视参数
        FILE_MONITOR_SET config;
        config.vMonitor_Path.push_back("/data/upload");
        config.bMonitorChild = true;
        config.nMonitorEvent = ZDS_IN_CLOSE_WRITE; // 文件写入完成
        config.strLogPath = "/var/log/upload";
        config.nTimeOut = 60; // 1小时超时
        
        // 启动监视
        m_pFileNotify->Start(config);
        
        // 注册回调
        m_pFileNotify->RegCallBak(this, OnFileUploaded);
    }
    
    static int OnFileUploaded(void* pParam, const string& filename, void* pReserved) {
        FileUploadMonitor* pThis = (FileUploadMonitor*)pParam;
        
        // 检查文件类型
        if (filename.find(".dat") != string::npos) {
            // 处理数据文件
            pThis->ProcessDataFile(filename);
        }
        else if (filename.find(".xml") != string::npos) {
            // 处理配置文件
            pThis->ProcessConfigFile(filename);
        }
        
        return 0;
    }
    
    void StopMonitor() {
        if (m_pFileNotify) {
            m_pFileNotify->Stop();
            destroy_inst_filenotify(m_pFileNotify);
            m_pFileNotify = NULL;
        }
    }
};
```

### 2. 监视多种事件类型
```cpp
void MonitorMultipleEvents() {
    IFileNotify* pNotify = create_inst_filenotify();
    
    FILE_MONITOR_SET config;
    config.vMonitor_Path.push_back("/data/work");
    
    // 监视多种事件
    config.nMonitorEvent = ZDS_IN_CREATE |      // 文件创建
                          ZDS_IN_DELETE |       // 文件删除
                          ZDS_IN_MODIFY |       // 文件修改
                          ZDS_IN_MOVED_TO |     // 文件移入
                          ZDS_IN_CLOSE_WRITE;   // 文件写入完成
    
    pNotify->Start(config);
    pNotify->RegCallBak(NULL, [](void* p, const string& file, void* r) -> int {
        printf("文件事件发生: %s\n", file.c_str());
        return 0;
    });
}
```

### 3. 实现文件处理队列
```cpp
class FileProcessor {
private:
    IFileNotify* m_pNotify;
    queue<string> m_fileQueue;
    mutex m_queueMutex;
    condition_variable m_queueCV;
    thread m_processThread;
    bool m_running;
    
public:
    void Start() {
        m_running = true;
        m_pNotify = create_inst_filenotify();
        
        // 配置监视
        FILE_MONITOR_SET config;
        config.vMonitor_Path.push_back("/data/incoming");
        config.nMonitorEvent = ZDS_IN_CLOSE_WRITE | ZDS_IN_MOVED_TO;
        config.nTimeOut = 120; // 2小时
        
        // 启动处理线程
        m_processThread = thread(&FileProcessor::ProcessThread, this);
        
        // 启动监视
        m_pNotify->Start(config);
        m_pNotify->RegCallBak(this, OnFileReady);
    }
    
    static int OnFileReady(void* pParam, const string& filename, void* pReserved) {
        FileProcessor* pThis = (FileProcessor*)pParam;
        
        // 添加到处理队列
        {
            lock_guard<mutex> lock(pThis->m_queueMutex);
            pThis->m_fileQueue.push(filename);
        }
        pThis->m_queueCV.notify_one();
        
        return 0;
    }
    
    void ProcessThread() {
        while (m_running) {
            unique_lock<mutex> lock(m_queueMutex);
            m_queueCV.wait(lock, [this] { 
                return !m_fileQueue.empty() || !m_running; 
            });
            
            while (!m_fileQueue.empty()) {
                string filename = m_fileQueue.front();
                m_fileQueue.pop();
                lock.unlock();
                
                // 处理文件
                ProcessFile(filename);
                
                lock.lock();
            }
        }
    }
    
    void ProcessFile(const string& filename) {
        printf("处理文件: %s\n", filename.c_str());
        // 实际的文件处理逻辑
    }
    
    void Stop() {
        m_running = false;
        m_queueCV.notify_all();
        
        if (m_processThread.joinable()) {
            m_processThread.join();
        }
        
        if (m_pNotify) {
            m_pNotify->Stop();
            destroy_inst_filenotify(m_pNotify);
        }
    }
};
```

## 设计特点

### 1. 跨平台支持
- Linux: 使用inotify机制
- Windows: 使用ReadDirectoryChangesW
- 统一的接口抽象

### 2. 事件过滤
- 支持精确的事件类型过滤
- 减少不必要的回调触发
- 提高系统性能

### 3. 超时机制
- 防止队列无限增长
- 自动清理过期事件
- 保护系统资源

### 4. 灵活配置
- 支持多路径监视
- 可选的递归监视
- 自定义事件组合

## 性能优化建议

1. **事件过滤**
   - 只监视需要的事件类型
   - 避免使用ZDS_IN_ALL_EVENTS
   - 使用ZDS_IN_ZX_ALL_EVENTS作为基础集

2. **路径优化**
   - 避免监视过大的目录树
   - 合理使用bMonitorChild
   - 分离不同类型文件到不同目录

3. **回调优化**
   - 回调函数快速返回
   - 重量级处理异步进行
   - 避免在回调中进行IO操作

4. **队列管理**
   - 合理设置nTimeOut
   - 定期检查队列状态
   - 及时处理通知事件

## 注意事项

1. **权限问题**
   - 确保对监视目录有读权限
   - 某些系统目录可能无法监视

2. **资源限制**
   - Linux系统有inotify watches限制
   - 可通过/proc/sys/fs/inotify/max_user_watches调整

3. **事件风暴**
   - 大量文件操作可能导致事件风暴
   - 需要合理的事件聚合策略

4. **符号链接**
   - 默认不跟随符号链接
   - 使用ZDS_IN_DONT_FOLLOW控制行为

5. **线程安全**
   - 回调函数在独立线程中执行
   - 需要考虑多线程同步问题
