# I_ZclDbAccess.h - 数据库访问接口详细分析

## 文件概述
- **文件名**: I_ZclDbAccess.h
- **作者**: 奚东洋
- **版本**: 1.0.1
- **功能**: 管理数据库访问的统一接口，支持多种数据库类型
- **设计模式**: 接口模式，构造函数为protected防止直接实例化

## 数据结构定义

### ZX_DB_ACCESS_PARAM - 数据库访问参数
```cpp
typedef struct _ZX_DB_ACCESS_PARAM {
    int nConDataBaseNum;           // 与数据库的连接数
    string strDbServerIpAddr;      // 数据库的IP地址
    string strServerName;          // 数据库服务名
    string strUserName;            // 数据库访问用户名
    string strUserPassword;        // 数据库访问密码
    string strUserMode;            // 数据库访问模式名(oracle时无效)
    int ndbType;                   // 数据库类型: 1:mysql 2:oracle 3:sqlserver 4:达梦 5:金仓 6:达梦7
    int nConDataBaseTimeout;       // 连接数据库的超时时间
}
```

## 核心接口函数详解

### 1. 基础配置函数

#### SetLogPath - 设置日志路径
```cpp
virtual bool SetLogPath(char* pSetLogPath, char* pErrorMsg) = 0;
```
- **功能**: 设置数据库操作日志的存储路径
- **参数**:
  - `pSetLogPath`: 需要设置的日志路径
  - `pErrorMsg`: 错误返回信息
- **返回**: true成功，false失败
- **使用场景**: 初始化时配置日志系统

#### SetConnNum - 设置连接数
```cpp
virtual bool SetConnNum(int nConNum) = 0;
```
- **功能**: 设置数据库连接池的连接数量
- **参数**: 
  - `nConNum`: 连接数（1~30，大于30时默认取为30）
- **返回**: true成功，false失败
- **使用场景**: 优化数据库连接性能

#### SetRtdbConnFlag - 设置实时库连接标识
```cpp
virtual bool SetRtdbConnFlag(int nRtdbConnFlag) = 0;
```
- **功能**: 控制是否连接实时数据库
- **参数**:
  - `nRtdbConnFlag`: 0不连接，1连接
- **返回**: true成功，false失败
- **使用场景**: 配置实时数据库访问

### 2. 连接管理函数

#### Connect - 连接数据库
```cpp
virtual bool Connect(int iModelNO, char* pError, ZX_DB_ACCESS_PARAM* pDbAccessParam = NULL) = 0;
```
- **功能**: 建立数据库连接
- **参数**:
  - `iModelNO`: 使用者登录模块ID
  - `pError`: 返回错误码
  - `pDbAccessParam`: 外部数据库连接参数（NULL时使用配置文件）
- **返回**: true成功，false失败
- **执行流程**:
  1. 验证模块ID合法性
  2. 读取或使用传入的数据库参数
  3. 根据数据库类型建立连接
  4. 初始化连接池
  5. 返回连接状态

#### CloseConnect - 关闭连接
```cpp
virtual bool CloseConnect(char* pErrorMsg) = 0;
```
- **功能**: 关闭数据库连接并释放资源
- **参数**:
  - `pErrorMsg`: 错误信息
- **返回**: true成功，false失败
- **执行流程**:
  1. 关闭所有活动连接
  2. 释放连接池资源
  3. 清理内部状态

### 3. 基本CRUD操作函数

#### Select - 查询数据
```cpp
virtual bool Select(int pTableNo, SQL_DATA& pSqlData, char* pError, CMemSet* pMemSet) = 0;
```
- **功能**: 执行数据库查询操作
- **参数**:
  - `pTableNo`: 数据库表编号
  - `pSqlData`: SQL查询参数结构
  - `pError`: 错误信息输出
  - `pMemSet`: 查询结果数据集
- **返回**: true成功，false失败
- **使用示例**:
```cpp
SQL_DATA sqlData;
sqlData.AddSelectField("station_id");
sqlData.AddSelectField("station_name");
sqlData.AddCondition("status", "=", "1");
CMemSet resultSet;
if (pDbAccess->Select(TABLE_STATION, sqlData, errorMsg, &resultSet)) {
    // 处理查询结果
}
```

#### Update - 更新数据
```cpp
virtual bool Update(int pTableNo, SQL_DATA& pSqlData, char* pError) = 0;
```
- **功能**: 执行数据库更新操作
- **参数**:
  - `pTableNo`: 数据库表编号
  - `pSqlData`: SQL更新参数结构
  - `pError`: 错误信息输出
- **返回**: true成功，false失败

#### Insert - 插入数据
```cpp
virtual bool Insert(int pTableNo, SQL_DATA& pSqlData, char* pError) = 0;
```
- **功能**: 执行数据库插入操作
- **参数**:
  - `pTableNo`: 数据库表编号
  - `pSqlData`: SQL插入参数结构
  - `pError`: 错误信息输出
- **返回**: true成功，false失败

#### Delete - 删除数据
```cpp
virtual bool Delete(int pTableNo, SQL_DATA& pSqlData, char* pError) = 0;
```
- **功能**: 执行数据库删除操作
- **参数**:
  - `pTableNo`: 数据库表编号
  - `pSqlData`: SQL删除参数结构
  - `pError`: 错误信息输出
- **返回**: true成功，false失败

### 4. 高级操作函数

#### ExecuteSql - 执行自定义SQL
```cpp
virtual bool ExecuteSql(MutiSQL_DATA& pMutiSQLDATA, char* pError, CMemSet* pMemSet) = 0;
```
- **功能**: 执行复杂SQL语句或访问未知表号的数据
- **参数**:
  - `pMutiSQLDATA`: 包含完整SQL语句的结构体
  - `pError`: 错误信息
  - `pMemSet`: 查询结果（如果是查询操作）
- **返回**: true成功，false失败
- **使用场景**: 
  - 执行复杂的JOIN查询
  - 访问其他系统的数据库表
  - 执行存储过程

#### SelectTableDivisionHisData - 查询分表历史数据
```cpp
virtual bool SelectTableDivisionHisData(int nTableID, SQL_DATA& query_sql, 
                                       const string &strBeginTm, const string &strEndTm, 
                                       char* pErrorMsg, CMemSet * p_rst) = 0;
```
- **功能**: 查询按年分表存储的历史数据
- **参数**:
  - `nTableID`: 表名编号
  - `query_sql`: SQL语句结构（不包含时间条件）
  - `strBeginTm`: 起始时间(yyyy-mm-dd hh:mm:ss)
  - `strEndTm`: 终止时间(yyyy-mm-dd hh:mm:ss)
  - `pErrorMsg`: 错误信息
  - `p_rst`: 查询结果集
- **返回**: true成功，false失败
- **执行流程**:
  1. 解析时间范围
  2. 确定需要查询的分表
  3. 构造跨表查询SQL
  4. 合并多表查询结果

### 5. 批量操作函数

#### BlobSelectData - 批量查询
```cpp
virtual bool BlobSelectData(int, BLOB_SQL_DATA&, char*, CMemSet*) = 0;
```
- **功能**: 执行大数据量查询，无SQL长度限制（总长度<8K）
- **使用场景**: 查询条件复杂或返回数据量大的场景

#### BlobUpdateData - 批量更新
```cpp
virtual bool BlobUpdateData(int, BLOB_SQL_DATA&, char*) = 0;
```
- **功能**: 执行大批量数据更新

#### BlobInsertData - 批量插入
```cpp
virtual bool BlobInsertData(int, BLOB_SQL_DATA&, char*) = 0;
```
- **功能**: 执行大批量数据插入

#### BlobExecuteSql - 执行大SQL
```cpp
virtual bool BlobExecuteSql(BLOB_MutiSQL_DATA&, char*, CMemSet*) = 0;
```
- **功能**: 执行长度较大的SQL语句（<8K）

### 6. 实时库操作函数

#### Insert_rttb - 实时库插入
```cpp
virtual bool Insert_rttb(int ntableno, char *pData, int nDataLen) = 0;
```
- **功能**: 向实时库插入数据
- **参数**:
  - `ntableno`: 实时库表编号
  - `pData`: 数据流指针，如(char*)&结构体
  - `nDataLen`: 数据长度
- **返回**: true成功，false失败
- **使用示例**:
```cpp
ZX_RTTB_MANUFACT_STATION_STAT_INSERT statData;
// 填充数据...
pDbAccess->Insert_rttb(RTTB_MANUFACT_STAT, (char*)&statData, sizeof(statData));
```

#### Update_rttb - 实时库更新
```cpp
virtual bool Update_rttb(int ntableno, const string &strCondition, 
                        const string &strfields, char *pData, int nDataLen) = 0;
```
- **功能**: 更新实时库数据
- **参数**:
  - `ntableno`: 实时库表编号
  - `strCondition`: 更新条件，如"manufacture='泽鑫'"
  - `strfields`: 更新字段列表，逗号分隔
  - `pData`: 数据流
  - `nDataLen`: 数据长度
- **返回**: true成功，false失败

#### Update_rttb_net_statistic - 更新网络统计信息
```cpp
virtual bool Update_rttb_net_statistic(const string &strName_Area, 
                                      const ZX_RTTB_DEV_NET_BASE_UPDATE& InfoUpdate) = 0;
```
- **功能**: 更新实时库和EMS库的设备网络通信状态统计
- **参数**:
  - `strName_Area`: 区域名称
  - `InfoUpdate`: 更新的统计信息结构
- **返回**: true成功，false失败

#### Insert_rttb_net_statistic - 插入网络统计记录
```cpp
virtual bool Insert_rttb_net_statistic(const ZX_RTTB_DEV_NET_BASE_INSERT& InfoInsert) = 0;
```
- **功能**: 新增设备网络基础信息记录

#### Update_rttb_station_statistic - 更新厂站统计信息
```cpp
virtual bool Update_rttb_station_statistic(const string &strStation_id, 
                                          const ZX_RTTB_STATION_BASE_UPDATE& InfoUpdate) = 0;
```
- **功能**: 更新厂站通信状态统计信息

#### Insert_rttb_station_statistic - 插入厂站统计记录
```cpp
virtual bool Insert_rttb_station_statistic(const ZX_RTTB_STATION_BASE_INSERT& InfoInsert) = 0;
```
- **功能**: 新增厂站基础信息记录

#### Select_rttb_yc_yx_byId - 根据ID查询遥测遥信
```cpp
virtual bool Select_rttb_yc_yx_byId(unsigned long lId, char* pBuf_ptr) = 0;
```
- **功能**: 根据遥测或遥信ID查询实时值
- **参数**:
  - `lId`: 遥测或遥信ID
  - `pBuf_ptr`: 查询结果缓冲区
- **返回**: true成功，false失败
- **使用场景**: 实时监控系统获取测点当前值

## 实例创建和销毁

### create_inst - 创建实例
```cpp
IZclDbAccess* create_inst();
```
- **功能**: 创建数据库访问接口实例
- **返回**: 指向具体实现类的接口指针
- **说明**: 工厂方法，隐藏具体实现类

### destroy_inst - 销毁实例
```cpp
int destroy_inst(IZclDbAccess *pInst);
```
- **功能**: 销毁数据库访问接口实例
- **参数**: 
  - `pInst`: 要销毁的实例指针
- **返回**: 0成功，1失败

## 使用示例

### 完整的数据库操作流程
```cpp
// 1. 创建实例
IZclDbAccess* pDbAccess = create_inst();

// 2. 设置配置
pDbAccess->SetLogPath("/var/log/db", errorMsg);
pDbAccess->SetConnNum(10);
pDbAccess->SetRtdbConnFlag(1);

// 3. 连接数据库
ZX_DB_ACCESS_PARAM dbParam;
dbParam.strDbServerIpAddr = "*************";
dbParam.strServerName = "SCADA";
dbParam.strUserName = "dragon";
dbParam.strUserPassword = "dragon123";
dbParam.ndbType = 4; // 达梦数据库

if (pDbAccess->Connect(MODULE_ID_DATAHDL, errorMsg, &dbParam)) {
    // 4. 执行数据库操作
    SQL_DATA queryData;
    queryData.AddSelectField("*");
    queryData.AddCondition("create_time", ">", "2024-01-01");
    
    CMemSet resultSet;
    if (pDbAccess->Select(TABLE_EVENT, queryData, errorMsg, &resultSet)) {
        // 处理查询结果
        for (int i = 0; i < resultSet.GetRowCount(); i++) {
            // 获取每行数据
        }
    }
    
    // 5. 关闭连接
    pDbAccess->CloseConnect(errorMsg);
}

// 6. 销毁实例
destroy_inst(pDbAccess);
```

## 设计特点

1. **接口隔离**: 纯虚函数接口，实现与接口分离
2. **多数据库支持**: 支持MySQL、Oracle、SQL Server、达梦等
3. **连接池管理**: 内置连接池提高性能
4. **批量操作**: 支持大数据量的批量处理
5. **实时库集成**: 特别支持电力系统实时数据库
6. **分表查询**: 支持历史数据分表存储架构

## 注意事项

1. 连接数设置不要超过30个
2. 实时库操作需要先设置SetRtdbConnFlag(1)
3. 分表查询时时间格式必须严格遵守yyyy-mm-dd hh:mm:ss
4. 大SQL操作总长度不能超过8K
5. 接口实例不能直接new创建，必须使用create_inst()
