# IZxBusSwap.h - 总线交换接口详细分析

## 文件概述
- **文件名**: IZxBusSwap.h
- **作者**: jjl
- **创建日期**: 2015/04/13
- **功能**: 定义总线数据交换接口，支持D5K和STTP两种总线类型
- **核心作用**: 提供统一的消息总线访问接口，屏蔽底层总线差异

## 重要常量定义

### 总线类型
```cpp
typedef enum _BUS_TYPE {
    UNKNOWN_BUSSWAP_TYPE = 0,    // 未知类型
    D5K_BUSSWAP_TYPE = 1,        // D5K总线
    STTP_BUSSWAP_TYPE = 2,       // STTP总线
} BUS_TYPE;
```

### 链路状态
```cpp
const int BUS_LINK_STATUS_NORMAL = 1;      // 链路正常
const int BUS_LINK_STATUS_DISCONNECT = 0;  // 链路断开
const int BUS_LINK_STATUS_UNKNOWN = -1;     // 链路未知
```

### D5000态名定义
```cpp
const char ZX_D5K_CONTEXT_REALTIME[] = "realtime";  // 实时态
const char ZX_D5K_CONTEXT_STUDY[]    = "study";     // 研究态
const char ZX_D5K_CONTEXT_PLAN[]     = "plan";      // 规划态
const char ZX_D5K_CONTEXT_TEST[]     = "test";      // 测试态
const char ZX_D5K_CONTEXT_PDR[]      = "pdr";       // 反演态
const char ZX_D5K_CONTEXT_DTS[]      = "dts";       // 培训态
```

### D5000应用名
```cpp
const char ZX_D5K_APPNAME_SCADARELAY[] = "scada_relay";  // 保信应用
const char ZX_D5K_APPNAME_SCADA[]      = "scada";        // SCADA应用
const char ZX_D5K_APPNAME_PUBLIC[]     = "public";       // 公共应用
const char ZX_D5K_APPNAME_DATASRV[]    = "data_srv";     // 数据服务
const char ZX_D5K_APPNAME_BASESRV[]    = "base_srv";     // 基本服务
const char ZX_D5K_APPNAME_AGC[]        = "agc";          // 自动发电控制
const char ZX_D5K_APPNAME_FES[]        = "fes";          // 前置采集
```

### 消息通道定义
```cpp
const short ZX_D5K_CHANNEL_HMI = 250;                       // 人机通道
const short ZX_D5K_SCADA_RELAY_CHANNEL_DOWN_NEW = 231;     // 新系统下行通道
const short ZX_D5k_SCADA_RELAY_CHANNEL_DOWN_OLD = 230;     // 老系统下行通道
```

### 消息主题定义
```cpp
const short ZX_MSG_TOPIC_COMMAND = 623;  // 命令消息
const short ZX_MSG_TOPIC_REPONSE = 624;  // 响应消息
const short ZX_MSG_TOPIC_EVENT   = 625;  // 事件通知
```

## 数据结构定义

### APP_NODE_INFO - 应用节点信息
```cpp
typedef struct _APP_NODE_INFO {
    int nProcType;                      // 应用进程模块编号
    string strProcName;                 // 应用进程名称（不超过24字节）
    string strContextName;              // 态名(D5000有效，默认"realtime")
    string strAppName;                  // 应用名(D5000有效，默认"scada_relay")
    vector<short> vRcvMsgChannelList;   // 订阅的消息通道列表
    short nSndMsgChannel;               // 发送消息的通道
    vector<short> vEventTopicList;      // 订阅消息事件主题列表
    bool bIsCareSttpInfo;               // STTP消息ID关注类型
    vector<int> vSttpMsgList;           // STTP消息ID列表
    E_ZX_MSG_RS_MODE eRSMode;           // 消息收发模式
    int nRSMsgType;                     // 收发消息类型
    string strAppModelVer;              // 应用模型版本号
    string strBusModelVer;              // 总线模型版本号
    int nReserve;                       // 广域消息总线连接标识
    string strReserve;                  // 广域消息总线调度名称
}
```

### BUS_ADDITION_INFO - 附加信息
```cpp
typedef struct _BUS_ADDITION_INFO {
    int nSafeArea;      // 信息来源安全区
    int nReserve1;      // 整形备用1
    int nReserve2;      // 整形备用2
    string strReserve1; // 字符串备用1
    string strReserve2; // 字符串备用2
}
```

## 回调函数定义

### PFUN_ONRECV_STTPDATA - STTP数据接收回调
```cpp
typedef int (*PFUN_ONRECV_STTPDATA)(
    void* pRegObj,                  // 注册对象
    STTP_FULL_DATA& sttp_data,      // STTP数据
    string& strAppNodeName,         // 应用节点名
    BUS_ADDITION_INFO& addition_info // 附加信息
);
```

### PFUN_ONRECV_D5KDATA - D5K数据接收回调
```cpp
#ifdef __D5000_PLATFORM__
typedef int (*PFUN_ONRECV_D5KDATA)(void* pRegObj, Message& d5k_msg);
#endif
```

### PFUNONCONNECTCHANGE - 连接状态变化回调
```cpp
typedef int (*PFUNONCONNECTCHANGE)(void* pRegObj, int nStatus);
```

## IZxBusSwap接口类定义

### 核心接口函数

#### 1. InitAppNode - 初始化应用节点
```cpp
virtual int InitAppNode(APP_NODE_INFO& node_inf) = 0;
```
- **功能**: 初始化应用节点信息
- **参数**: 
  - `node_inf`: 应用节点配置信息
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 验证节点信息有效性
  2. 根据总线类型初始化
  3. 注册到消息总线
  4. 订阅指定的消息通道和主题

#### 2. GetBusType - 获取总线类型
```cpp
virtual BUS_TYPE GetBusType() = 0;
```
- **功能**: 获取当前使用的总线类型
- **返回**: D5K_BUSSWAP_TYPE或STTP_BUSSWAP_TYPE

#### 3. GetCurRunSafeArea - 获取当前安全区
```cpp
virtual int GetCurRunSafeArea() = 0;
```
- **功能**: 获取当前运行的安全区编号
- **返回**: 安全区编号

#### 4. RegConntStatusChgCallBak - 注册连接状态变化回调
```cpp
virtual int RegConntStatusChgCallBak(void* pRegObj, PFUNONCONNECTCHANGE pCallBack) = 0;
```
- **功能**: 注册连接状态变化的回调函数
- **参数**:
  - `pRegObj`: 注册对象
  - `pCallBack`: 回调函数指针
- **返回**: 0成功，-1失败

#### 5. RegRecvSttpMsgCallBak - 注册STTP消息接收回调
```cpp
virtual int RegRecvSttpMsgCallBak(void* pRegObj, PFUN_ONRECV_STTPDATA pCallBack) = 0;
```
- **功能**: 注册STTP消息接收的回调函数
- **参数**:
  - `pRegObj`: 注册对象
  - `pCallBack`: 回调函数指针
- **返回**: 0成功，-1失败

#### 6. StartBusSwap - 启动总线服务
```cpp
virtual int StartBusSwap() = 0;
```
- **功能**: 启动总线数据交换服务
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 创建消息接收线程
  2. 建立总线连接
  3. 开始消息监听

#### 7. StopBusSwap - 停止总线服务
```cpp
virtual int StopBusSwap() = 0;
```
- **功能**: 停止总线数据交换服务
- **返回**: 0成功，-1失败

#### 8. SendSttpMsgData - 发送STTP消息
```cpp
virtual int SendSttpMsgData(
    const STTP_FULL_DATA& sttp_data,
    short nMsgTopicType,
    string strAppNodeName = "",
    short nMsgChannel = -1,
    void* pReserve = NULL
) = 0;
```
- **功能**: 发送STTP格式的消息数据
- **参数**:
  - `sttp_data`: STTP消息数据
  - `nMsgTopicType`: 消息主题类型
  - `strAppNodeName`: 应用节点名（用于匹配）
  - `nMsgChannel`: 消息通道（-1使用默认）
  - `pReserve`: 保留参数
- **返回**: 0成功，-1失败

#### 9. SetTimeOutOption - 设置超时选项
```cpp
virtual int SetTimeOutOption(int nOptionName, int nTimeOut, int nReserved) = 0;
```
- **功能**: 设置各种超时参数
- **参数**:
  - `nOptionName`: 选项名称
  - `nTimeOut`: 超时时间（秒）
  - `nReserved`: 保留参数
- **返回**: 0成功，-1失败

#### 10. QueryMultiSrvStatus - 查询多服务器状态
```cpp
virtual int QueryMultiSrvStatus(XJServerStatusSet& vSrvStatusSet) = 0;
```
- **功能**: 查询多个服务器的状态信息
- **参数**:
  - `vSrvStatusSet`: 服务器状态集合
- **返回**: 0成功，-1失败

### D5000平台特有接口

#### SendD5kMsgData - 发送D5K消息
```cpp
#ifdef __D5000_PLATFORM__
virtual int SendD5kMsgData(const Message& d5k_msg, void* pReserve = NULL) = 0;
#endif
```

#### RegRecvD5kMsgCallBak - 注册D5K消息回调
```cpp
#ifdef __D5000_PLATFORM__
virtual int RegRecvD5kMsgCallBak(void* pRegObj, PFUN_ONRECV_D5KDATA pCallBack) = 0;
#endif
```

## 使用示例

### 基本使用流程
```cpp
// 1. 创建总线实例
IZxBusSwap* pBusSwap = /* 通过动态库加载获取 */;

// 2. 配置应用节点信息
APP_NODE_INFO nodeInfo;
nodeInfo.nProcType = MODULE_DATAHDL;
nodeInfo.strProcName = "DataHandler";
nodeInfo.strContextName = ZX_D5K_CONTEXT_REALTIME;
nodeInfo.strAppName = ZX_D5K_APPNAME_SCADARELAY;
nodeInfo.vRcvMsgChannelList.push_back(ZX_D5K_SCADA_RELAY_CHANNEL_DOWN_NEW);
nodeInfo.nSndMsgChannel = ZX_D5K_CHANNEL_HMI;
nodeInfo.vEventTopicList.push_back(ZX_MSG_TOPIC_COMMAND);
nodeInfo.vEventTopicList.push_back(ZX_MSG_TOPIC_EVENT);

// 3. 初始化节点
if (pBusSwap->InitAppNode(nodeInfo) != 0) {
    printf("初始化失败\n");
    return;
}

// 4. 注册回调函数
pBusSwap->RegRecvSttpMsgCallBak(this, OnSttpDataReceived);
pBusSwap->RegConntStatusChgCallBak(this, OnConnectionChanged);

// 5. 启动服务
pBusSwap->StartBusSwap();

// 6. 发送消息
STTP_FULL_DATA sttpData;
// 填充数据...
pBusSwap->SendSttpMsgData(sttpData, ZX_MSG_TOPIC_EVENT);

// 7. 停止服务
pBusSwap->StopBusSwap();
```

### 回调函数实现
```cpp
// STTP消息接收回调
int OnSttpDataReceived(void* pObj, STTP_FULL_DATA& sttpData, 
                      string& appNode, BUS_ADDITION_INFO& addInfo) {
    MyClass* pThis = (MyClass*)pObj;
    
    printf("收到STTP消息: MsgId=%d, AppNode=%s, SafeArea=%d\n",
           sttpData.head.msgid, appNode.c_str(), addInfo.nSafeArea);
    
    // 处理消息
    pThis->ProcessSttpMessage(sttpData);
    
    return 0;
}

// 连接状态变化回调
int OnConnectionChanged(void* pObj, int nStatus) {
    const char* statusStr = (nStatus == BUS_LINK_STATUS_NORMAL) ? "正常" : 
                           (nStatus == BUS_LINK_STATUS_DISCONNECT) ? "断开" : "未知";
    
    printf("总线连接状态: %s\n", statusStr);
    
    return 0;
}
```

### 双总线支持示例
```cpp
class DualBusHandler {
private:
    IZxBusSwap* m_pBusSwap;
    
public:
    void Initialize() {
        // 根据配置文件确定总线类型
        BUS_TYPE busType = m_pBusSwap->GetBusType();
        
        if (busType == D5K_BUSSWAP_TYPE) {
            InitForD5K();
        } else if (busType == STTP_BUSSWAP_TYPE) {
            InitForSttp();
        }
    }
    
    void InitForD5K() {
        // D5K特定初始化
        #ifdef __D5000_PLATFORM__
        m_pBusSwap->RegRecvD5kMsgCallBak(this, OnD5kMessage);
        #endif
    }
    
    void InitForSttp() {
        // STTP特定初始化
        m_pBusSwap->RegRecvSttpMsgCallBak(this, OnSttpMessage);
    }
    
    void SendMessage(const STTP_FULL_DATA& data) {
        if (m_pBusSwap->GetBusType() == STTP_BUSSWAP_TYPE) {
            // 直接发送STTP
            m_pBusSwap->SendSttpMsgData(data, ZX_MSG_TOPIC_EVENT);
        } else {
            // D5K需要转换
            #ifdef __D5000_PLATFORM__
            Message d5kMsg;
            ConvertSttpToD5k(data, d5kMsg);
            m_pBusSwap->SendD5kMsgData(d5kMsg);
            #endif
        }
    }
};
```

### 多安全区支持
```cpp
class MultiSafeAreaHandler {
public:
    void ProcessMessageBySafeArea(STTP_FULL_DATA& data, BUS_ADDITION_INFO& info) {
        int currentArea = m_pBusSwap->GetCurRunSafeArea();
        int msgArea = info.nSafeArea;
        
        if (msgArea == currentArea) {
            // 处理本安全区消息
            ProcessLocalMessage(data);
        } else {
            // 转发到其他安全区
            ForwardToOtherArea(data, msgArea);
        }
    }
};
```

## 设计特点

### 1. 总线抽象
- 统一接口支持多种总线
- 屏蔽底层实现差异
- 方便系统迁移和升级

### 2. 灵活配置
- 动态配置消息通道
- 可选的消息主题订阅
- 支持多应用节点

### 3. 异步通信
- 基于回调的消息接收
- 非阻塞的消息发送
- 支持同步和异步模式

### 4. 可靠性设计
- 连接状态监控
- 自动重连机制
- 超时控制

## 注意事项

1. **平台差异**
   - D5000平台需要定义`__D5000_PLATFORM__`宏
   - 注意头文件的条件编译
   - Message结构体仅在D5000平台可用

2. **线程安全**
   - 回调函数在独立线程中执行
   - 需要考虑数据同步问题
   - 避免在回调中进行耗时操作

3. **资源管理**
   - 正确的初始化和清理顺序
   - 注意动态库的加载和卸载
   - 避免内存泄漏

4. **性能优化**
   - 合理设置消息通道数量
   - 避免频繁的消息发送
   - 批量处理消息

5. **错误处理**
   - 检查所有返回值
   - 处理连接断开情况
   - 记录关键错误日志
