# IZxD5kModelSync.h - D5K模型同步接口详细分析

## 文件概述
- **文件名**: IZxD5kModelSync.h
- **作者**: sl
- **创建日期**: 2019/05/19
- **功能**: 提供一次设备状态获取和模型同步的接口
- **用途**: 在D5000平台和Dragon系统之间同步电力设备模型和状态

## 常量定义

### 一次设备类型
```cpp
#define PRIM_DEV_TYPE_LINE      1    // 线路
#define PRIM_DEV_TYPE_BUS       2    // 母线
#define PRIM_DEV_TYPE_TRANS     3    // 变压器
#define PRIM_DEV_TYPE_BREAKER   4    // 开关
#define PRIM_DEV_TYPE_BAY       5    // 间隔
#define PRIM_DEV_TYPE_SWITCH    6    // 刀闸
```

## IZxD5kModelSync接口类定义

### 核心接口函数

#### 1. Init - 初始化
```cpp
virtual int Init(int nWorkPart, CXJDBFacade* pDBFacade) = 0;
```
- **功能**: 初始化模型同步模块
- **参数**:
  - `nWorkPart`: 安全工作区编号
  - `pDBFacade`: 数据库访问接口指针
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 设置工作区参数
  2. 保存数据库接口
  3. 初始化D5K连接
  4. 建立模型映射关系
- **注意**: 实例化对象后必须首次调用

#### 2. SyncBayInfo - 同步间隔信息
```cpp
virtual int SyncBayInfo(const char* cStnId, 
                       ZID lMainPrimDev, 
                       int nPrimType, 
                       bool bSyncSwitchInfo = false) = 0;
```
- **功能**: 同步EMS库的间隔信息到Dragon库
- **参数**:
  - `cStnId`: Dragon数据库的厂站ID（必须指定）
  - `lMainPrimDev`: 一次设备GID（long long类型）
  - `nPrimType`: 一次设备类型（使用上述常量）
  - `bSyncSwitchInfo`: 是否同时更新刀闸信息（默认false）
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 验证厂站ID有效性
  2. 从EMS库查询间隔信息
  3. 根据一次设备查找关联间隔
  4. 同步间隔数据到Dragon库
  5. 可选同步刀闸信息
- **使用示例**:
```cpp
// 同步某条线路的间隔信息
ZID lineGid = 1234567890;
pModelSync->SyncBayInfo("STATION001", lineGid, PRIM_DEV_TYPE_LINE, true);
```

#### 3. SyncSwitchByBayId - 根据间隔ID同步刀闸
```cpp
virtual int SyncSwitchByBayId(const char* cStnId, ZID lBay) = 0;
```
- **功能**: 根据间隔ID获取刀闸信息并入库
- **参数**:
  - `cStnId`: 厂站ID
  - `lBay`: 间隔ID
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 查询间隔下的所有刀闸
  2. 获取刀闸详细信息
  3. 同步刀闸数据到Dragon库
  4. 更新刀闸状态
- **使用场景**:
  - 已知间隔ID时的刀闸同步
  - 间隔变更后的刀闸更新

#### 4. CtrlThreadStateSync - 控制状态同步线程
```cpp
virtual int CtrlThreadStateSync(bool bThreadState, int nSyncTime) = 0;
```
- **功能**: 控制刀闸和开关状态的自动同步线程
- **参数**:
  - `bThreadState`: 
    - true: 启动线程运行
    - false: 停止线程运行
  - `nSyncTime`: 状态同步周期（分钟），最小间隔3分钟
- **返回**: 0成功，其他失败
- **执行流程**:
  1. 验证同步周期合法性（>=3分钟）
  2. 创建/停止同步线程
  3. 设置定时器
  4. 周期性执行状态同步
- **注意事项**:
  - 同步周期不能小于3分钟
  - 线程会自动处理异常和重连

#### 5. Free - 释放资源
```cpp
virtual int Free() = 0;
```
- **功能**: 停止模型同步服务并释放资源
- **返回**: 0成功，-1失败
- **执行流程**:
  1. 停止同步线程
  2. 断开D5K连接
  3. 清理内部缓存
  4. 释放资源

## 创建和销毁函数

### CreateModelSyncIns - 创建实例
```cpp
IZxD5kModelSync* CreateModelSyncIns(CMessageLog* pMsgFile, char* pChLogPath = NULL);
```
- **功能**: 创建模型同步对象实例
- **参数**:
  - `pMsgFile`: 报文记录对象
  - `pChLogPath`: 日志存放路径（可选）
- **返回**: 实例对象指针

### DestroyModelSyncIns - 销毁实例
```cpp
bool DestroyModelSyncIns(IZxD5kModelSync* pIns);
```
- **功能**: 销毁模型同步对象实例
- **参数**:
  - `pIns`: 要销毁的实例指针
- **返回**: true成功，false失败

## 使用示例

### 基本使用流程
```cpp
// 1. 创建日志对象
CMessageLog msgLog;
msgLog.Init("/var/log/modelsync", "modelsync", 3);

// 2. 创建数据库访问对象
CXJDBFacade* pDBFacade = new CXJDBFacade(&msgLog);
pDBFacade->LoadLib();
pDBFacade->Connect(MODULE_ID, errorMsg);

// 3. 创建模型同步实例
IZxD5kModelSync* pModelSync = CreateModelSyncIns(&msgLog, "/var/log/modelsync");

// 4. 初始化
int workArea = 1;  // 安全区1
if (pModelSync->Init(workArea, pDBFacade) != 0) {
    printf("初始化失败\n");
    return;
}

// 5. 同步间隔信息
pModelSync->SyncBayInfo("STN001", 1234567890, PRIM_DEV_TYPE_LINE, true);

// 6. 启动自动同步
pModelSync->CtrlThreadStateSync(true, 10);  // 每10分钟同步一次

// 7. 停止和清理
pModelSync->CtrlThreadStateSync(false, 0);
pModelSync->Free();
DestroyModelSyncIns(pModelSync);
```

### 批量同步示例
```cpp
class BatchModelSync {
private:
    IZxD5kModelSync* m_pModelSync;
    
public:
    void SyncStationDevices(const char* stnId) {
        // 同步线路间隔
        vector<ZID> lineGids = GetStationLines(stnId);
        for (auto gid : lineGids) {
            m_pModelSync->SyncBayInfo(stnId, gid, PRIM_DEV_TYPE_LINE, true);
        }
        
        // 同步变压器间隔
        vector<ZID> transGids = GetStationTransformers(stnId);
        for (auto gid : transGids) {
            m_pModelSync->SyncBayInfo(stnId, gid, PRIM_DEV_TYPE_TRANS, true);
        }
        
        // 同步母线间隔
        vector<ZID> busGids = GetStationBuses(stnId);
        for (auto gid : busGids) {
            m_pModelSync->SyncBayInfo(stnId, gid, PRIM_DEV_TYPE_BUS, false);
        }
    }
    
    void SyncAllStations() {
        vector<string> stations = GetAllStations();
        
        for (const auto& stnId : stations) {
            printf("同步厂站: %s\n", stnId.c_str());
            SyncStationDevices(stnId.c_str());
        }
    }
};
```

### 定时同步管理器
```cpp
class ModelSyncManager {
private:
    IZxD5kModelSync* m_pModelSync;
    bool m_manualSyncMode;
    
public:
    void Initialize() {
        // 初始化...
        
        // 根据配置决定同步模式
        if (m_manualSyncMode) {
            // 手动模式，不启动自动同步
            printf("手动同步模式\n");
        } else {
            // 自动模式，启动定时同步
            int syncInterval = GetConfigSyncInterval();
            m_pModelSync->CtrlThreadStateSync(true, syncInterval);
            printf("自动同步模式，周期: %d分钟\n", syncInterval);
        }
    }
    
    void ManualSync(const char* stnId, ZID deviceId, int deviceType) {
        if (!m_manualSyncMode) {
            printf("当前为自动同步模式\n");
            return;
        }
        
        // 执行手动同步
        int result = m_pModelSync->SyncBayInfo(stnId, deviceId, deviceType, true);
        if (result == 0) {
            printf("手动同步成功\n");
            LogSyncEvent(stnId, deviceId, deviceType, "MANUAL");
        } else {
            printf("手动同步失败\n");
        }
    }
    
    void SwitchSyncMode(bool autoMode) {
        if (autoMode && m_manualSyncMode) {
            // 从手动切换到自动
            m_pModelSync->CtrlThreadStateSync(true, 10);
            m_manualSyncMode = false;
        } else if (!autoMode && !m_manualSyncMode) {
            // 从自动切换到手动
            m_pModelSync->CtrlThreadStateSync(false, 0);
            m_manualSyncMode = true;
        }
    }
};
```

### 增量同步示例
```cpp
class IncrementalSync {
private:
    IZxD5kModelSync* m_pModelSync;
    map<ZID, time_t> m_lastSyncTime;
    
public:
    void OnDeviceChanged(const char* stnId, ZID deviceId, int deviceType) {
        // 设备变更时触发同步
        printf("设备变更通知: Station=%s, Device=%lld, Type=%d\n", 
               stnId, deviceId, deviceType);
        
        // 检查是否需要同步（防止频繁同步）
        time_t now = time(NULL);
        auto it = m_lastSyncTime.find(deviceId);
        if (it != m_lastSyncTime.end()) {
            if (now - it->second < 60) {  // 1分钟内不重复同步
                printf("跳过同步，距上次同步不足1分钟\n");
                return;
            }
        }
        
        // 执行同步
        if (m_pModelSync->SyncBayInfo(stnId, deviceId, deviceType, true) == 0) {
            m_lastSyncTime[deviceId] = now;
        }
    }
    
    void OnBayChanged(const char* stnId, ZID bayId) {
        // 间隔变更时同步刀闸
        printf("间隔变更通知: Station=%s, Bay=%lld\n", stnId, bayId);
        m_pModelSync->SyncSwitchByBayId(stnId, bayId);
    }
};
```

## 设计特点

### 1. 平台集成
- 深度集成D5000平台
- 支持EMS数据同步
- 兼容Dragon数据库

### 2. 灵活同步
- 支持手动和自动同步
- 可选的刀闸同步
- 增量同步能力

### 3. 性能优化
- 批量同步支持
- 最小同步间隔限制
- 异步处理机制

### 4. 可靠性
- 自动重连机制
- 异常处理
- 状态监控

## 注意事项

1. **初始化顺序**
   - 必须先调用Init
   - 确保数据库连接有效
   - 正确设置工作区

2. **同步策略**
   - 合理设置同步周期
   - 避免频繁同步
   - 注意系统负载

3. **数据一致性**
   - 确保厂站ID正确
   - 验证设备类型匹配
   - 处理同步冲突

4. **资源管理**
   - 正确调用Free
   - 及时销毁实例
   - 避免内存泄漏

5. **错误处理**
   - 检查返回值
   - 记录同步日志
   - 实现重试机制

6. **性能考虑**
   - 批量同步优于单个同步
   - 使用增量同步
   - 监控同步耗时
