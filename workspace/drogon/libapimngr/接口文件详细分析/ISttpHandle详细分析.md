# ISttpHandle.h - STTP报文处理接口详细分析

## 文件概述
- **文件名**: ISttpHandle.h
- **作者**: 奚东洋
- **版本**: 1.0.0
- **功能**: STTP报文处理接口，负责报文入实时库和历史库
- **创建日期**: 2020/07/11
- **设计特点**: 简化版的报文处理接口，专注于数据存储

## 数据结构定义

### STTPHANDLE_PARAM - STTP处理参数
```cpp
typedef struct _STTPHANDLE_PARAM {
    char chLogPath[255];    // 日志路径
    int nLogLevel;          // 日志级别
    int nLogSaveDay;        // 日志保存天数
    int nrtdb_save;         // 是否存实时库
}
```

**默认值说明**：
- `chLogPath`: 空字符串
- `nLogLevel`: 0 (最低级别)
- `nLogSaveDay`: 3天
- `nrtdb_save`: 0 (默认不存实时库)

## 核心接口函数详解

### 1. Init - 初始化函数
```cpp
virtual int Init(const STTPHANDLE_PARAM & da_param, CXJDBFacade* plibmngr_dbacess) = 0;
```
- **功能**: 初始化STTP处理模块
- **参数**:
  - `da_param`: STTP处理参数配置
  - `plibmngr_dbacess`: 数据库访问接口指针
- **返回**: 0成功，1失败
- **执行流程**:
  1. 初始化日志系统
  2. 设置日志级别和保存天数
  3. 保存数据库访问接口指针
  4. 初始化实时库连接（如果需要）
  5. 创建内部处理队列
- **注意事项**:
  - 数据库接口由外部管理，内部不负责创建和释放
  - 需要根据nrtdb_save参数决定是否连接实时库

### 2. Uninit - 释放函数
```cpp
virtual int Uninit() = 0;
```
- **功能**: 释放STTP处理模块资源
- **返回**: 0成功，1失败
- **执行流程**:
  1. 停止所有处理任务
  2. 清空待处理队列
  3. 断开实时库连接
  4. 清理日志系统
  5. 释放内部资源

### 3. HandleSttpData - 处理STTP报文
```cpp
virtual int HandleSttpData(STTP_FULL_DATA & sttpFullData, char* pErrorMsg, int nChangeflag) = 0;
```
- **功能**: 处理STTP报文并存储到实时库和历史库
- **参数**:
  - `sttpFullData`: STTP完整报文数据
  - `pErrorMsg`: 错误信息输出缓冲区
  - `nChangeflag`: 数据是否发生变化标志
- **返回**: 0成功，1失败
- **参数详解**:
  - `nChangeflag`: 
    - 1: 数据已变化，需要存储
    - 0: 数据未变化，可能跳过存储
- **执行流程**:
  1. 验证STTP报文有效性
  2. 解析报文内容
  3. 判断是否需要存储（基于nChangeflag）
  4. 如果nrtdb_save=1，存入实时库
  5. 存入历史库
  6. 记录处理结果

## 实例创建和销毁

### createISttpHandle - 创建实例
```cpp
ISttpHandle* createISttpHandle();
```
- **功能**: 创建STTP处理接口实例
- **返回**: 接口实例指针
- **说明**: 工厂方法，隐藏实现细节

### destroyISttpHandle - 销毁实例
```cpp
int destroyISttpHandle(ISttpHandle *pInst);
```
- **功能**: 销毁STTP处理接口实例
- **参数**:
  - `pInst`: 要销毁的实例指针
- **返回**: 0成功，1失败

## 使用示例

### 基本使用流程
```cpp
// 1. 创建数据库访问实例
CXJDBFacade* pDbFacade = new CXJDBFacade(&logFile);
pDbFacade->LoadLib();
pDbFacade->Connect(MODULE_ID, errorMsg);

// 2. 创建STTP处理实例
ISttpHandle* pSttpHandle = createISttpHandle();

// 3. 配置参数
STTPHANDLE_PARAM param;
strcpy(param.chLogPath, "/var/log/sttp");
param.nLogLevel = 3;
param.nLogSaveDay = 7;
param.nrtdb_save = 1;  // 启用实时库存储

// 4. 初始化
if (pSttpHandle->Init(param, pDbFacade) == 0) {
    // 5. 处理STTP数据
    STTP_FULL_DATA sttpData;
    char errorMsg[256];
    int changeFlag = 1;  // 数据已变化
    
    // 填充STTP数据...
    if (pSttpHandle->HandleSttpData(sttpData, errorMsg, changeFlag) != 0) {
        printf("处理失败: %s\n", errorMsg);
    }
    
    // 6. 清理
    pSttpHandle->Uninit();
}

// 7. 销毁实例
destroyISttpHandle(pSttpHandle);
delete pDbFacade;
```

### 批量处理示例
```cpp
class SttpBatchProcessor {
private:
    ISttpHandle* m_pSttpHandle;
    CXJDBFacade* m_pDbFacade;
    
public:
    bool Initialize() {
        // 初始化数据库
        m_pDbFacade = new CXJDBFacade(&m_logFile);
        m_pDbFacade->LoadLib();
        m_pDbFacade->Connect(MODULE_ID, m_errorMsg);
        
        // 创建STTP处理器
        m_pSttpHandle = createISttpHandle();
        
        // 配置参数
        STTPHANDLE_PARAM param;
        strcpy(param.chLogPath, "/var/log/sttp_batch");
        param.nLogLevel = 2;
        param.nLogSaveDay = 30;
        param.nrtdb_save = 1;
        
        return (m_pSttpHandle->Init(param, m_pDbFacade) == 0);
    }
    
    void ProcessBatch(vector<STTP_FULL_DATA>& dataList) {
        char errorMsg[256];
        int successCount = 0;
        int failCount = 0;
        
        for (auto& sttpData : dataList) {
            // 判断数据是否变化
            int changeFlag = CheckDataChange(sttpData);
            
            if (m_pSttpHandle->HandleSttpData(sttpData, errorMsg, changeFlag) == 0) {
                successCount++;
            } else {
                failCount++;
                LogError("处理失败: PT_ID=%s, Error=%s", 
                        sttpData.pt_id, errorMsg);
            }
        }
        
        LogInfo("批量处理完成: 成功=%d, 失败=%d", successCount, failCount);
    }
    
    int CheckDataChange(const STTP_FULL_DATA& newData) {
        // 获取旧值
        float oldValue = GetOldValueFromDb(newData.pt_id);
        float newValue = ExtractValue(newData);
        
        // 死区判断
        if (fabs(newValue - oldValue) < m_deadZone) {
            return 0;  // 未变化
        }
        
        return 1;  // 已变化
    }
    
    void Cleanup() {
        if (m_pSttpHandle) {
            m_pSttpHandle->Uninit();
            destroyISttpHandle(m_pSttpHandle);
        }
        
        delete m_pDbFacade;
    }
};
```

### 实时库条件存储示例
```cpp
void ConditionalStorage() {
    ISttpHandle* pHandle = createISttpHandle();
    
    // 配置：只存历史库，不存实时库
    STTPHANDLE_PARAM paramHistory;
    strcpy(paramHistory.chLogPath, "/var/log/history_only");
    paramHistory.nrtdb_save = 0;  // 禁用实时库
    
    // 配置：同时存储到实时库和历史库
    STTPHANDLE_PARAM paramBoth;
    strcpy(paramBoth.chLogPath, "/var/log/both_storage");
    paramBoth.nrtdb_save = 1;  // 启用实时库
    
    // 根据数据类型选择存储策略
    if (isRealtimeData) {
        pHandle->Init(paramBoth, pDbFacade);
    } else {
        pHandle->Init(paramHistory, pDbFacade);
    }
}
```

## 与IDataHandle的区别

### 1. 功能简化
- ISttpHandle专注于数据存储
- IDataHandle包含更多的数据处理逻辑
- ISttpHandle更轻量级

### 2. 参数差异
- ISttpHandle使用STTPHANDLE_PARAM
- IDataHandle使用DATAHANDLE_PARAM
- ISttpHandle参数更简单

### 3. 接口差异
- ISttpHandle有nChangeflag参数
- ISttpHandle有pErrorMsg输出参数
- ISttpHandle没有总线相关接口

### 4. 使用场景
- ISttpHandle: 纯数据存储场景
- IDataHandle: 复杂的数据处理场景

## 设计特点

### 1. 简洁性
- 接口简单，易于使用
- 参数精简，配置方便
- 专注核心功能

### 2. 灵活性
- 可选的实时库存储
- 变化标志控制
- 错误信息输出

### 3. 性能优化
- 根据变化标志跳过处理
- 避免重复存储
- 减少数据库访问

## 注意事项

1. **数据库管理**
   - 数据库接口由外部创建和管理
   - 确保传入的数据库接口有效
   - 在销毁ISttpHandle前不要释放数据库接口

2. **错误处理**
   - 总是检查返回值
   - 利用pErrorMsg获取详细错误信息
   - 日志记录所有错误

3. **性能考虑**
   - 合理使用nChangeflag避免不必要的存储
   - 批量处理时注意内存使用
   - 考虑使用异步处理提高吞吐量

4. **配置建议**
   - 生产环境建议nLogLevel设置为2或3
   - 日志保存天数根据磁盘空间调整
   - 实时库存储根据实际需求开启

5. **线程安全**
   - 单个实例不是线程安全的
   - 多线程环境需要外部同步
   - 或者每个线程创建独立实例
