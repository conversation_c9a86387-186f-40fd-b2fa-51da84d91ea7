# 心跳管理接口详细分析

本文档分析libapimngr中的心跳管理相关接口文件。

## 1. ZxLibApi_Heartbeat.h - 心跳接口主文件

### 文件概述
- **文件名**: ZxLibApi_Heartbeat.h
- **功能**: 定义心跳管理的主接口类
- **创建日期**: 2011/11/17

### CHeartbeatInterface类定义

```cpp
class CHeartbeatInterface {
public:
    // 发送相关接口
    virtual int RegisterBroadcastAddress(string pAddress, int pPort) = 0;
    virtual int BroatcastHeartbeat(StHeartbeatInfo& pInfo) = 0;
    
    // 接收相关接口
    virtual int StartHeartbeatListening(vector<string>& pAddresses, int pPort, int pTimeout) = 0;
    virtual int EndHeartbeatListening() = 0;
    virtual int RegisterHeartbeatListener(PFUNRECVHEARTBEAT pOnRecv, void* pParam) = 0;
};
```

### 核心接口函数详解

#### 1. RegisterBroadcastAddress - 注册广播地址
```cpp
virtual int RegisterBroadcastAddress(string pAddress, int pPort) = 0;
```
- **功能**: 注册心跳广播的目标地址
- **参数**:
  - `pAddress`: 目标IP地址或广播地址
  - `pPort`: 目标端口号
- **返回**: 0成功，其他失败
- **使用场景**:
  - 配置心跳发送的目标
  - 支持单播和广播模式
  - 可注册多个地址

#### 2. BroatcastHeartbeat - 广播心跳
```cpp
virtual int BroatcastHeartbeat(StHeartbeatInfo& pInfo) = 0;
```
- **功能**: 发送心跳包到已注册的地址
- **参数**:
  - `pInfo`: 心跳信息结构体
- **返回**: 0成功，其他失败
- **执行流程**:
  1. 构造心跳数据包
  2. 向所有注册地址发送
  3. 更新发送统计

#### 3. StartHeartbeatListening - 启动心跳监听
```cpp
virtual int StartHeartbeatListening(vector<string>& pAddresses, int pPort, int pTimeout) = 0;
```
- **功能**: 启动心跳接收监听
- **参数**:
  - `pAddresses`: 监听的本地地址列表
  - `pPort`: 监听端口
  - `pTimeout`: 心跳超时时间（秒）
- **返回**: 0成功，其他失败
- **说明**:
  - 创建UDP监听套接字
  - 启动接收线程
  - 设置超时检测

#### 4. EndHeartbeatListening - 停止心跳监听
```cpp
virtual int EndHeartbeatListening() = 0;
```
- **功能**: 停止心跳接收监听
- **返回**: 0成功，其他失败
- **执行流程**:
  1. 停止接收线程
  2. 关闭监听套接字
  3. 清理资源

#### 5. RegisterHeartbeatListener - 注册心跳监听器
```cpp
virtual int RegisterHeartbeatListener(PFUNRECVHEARTBEAT pOnRecv, void* pParam) = 0;
```
- **功能**: 注册心跳接收回调函数
- **参数**:
  - `pOnRecv`: 回调函数指针
  - `pParam`: 用户参数
- **返回**: 0成功，其他失败

## 2. ZxLibMngr_Heartbeat.h - 心跳管理器封装

### 文件概述
- **文件名**: ZxLibMngr_Heartbeat.h (HeartbeatWrapper.h)
- **功能**: 封装心跳处理动态库的加载和管理
- **创建日期**: 2011/12/08

### CHeartbeatWrapper类定义

```cpp
class CHeartbeatWrapper {
private:
    CLogFile& m_rLogFile;                            // 日志对象引用
    XJHANDLE m_hLibHBHandler;                        // 动态库句柄
    PFUNCREATHEARTBEATHANDLER m_pFunCreateHBHandler;    // 创建函数指针
    PFUNDESTROYHEARTBEATHANDLER m_pFunDestroyHBHandler; // 销毁函数指针
    
public:
    CHeartbeatWrapper(CLogFile& pLogFile);
    bool InitLibrary();
    CHeartbeatInterface* CreateHeartbeatHandler(bool pIfLogMessage, 
                                               CLogFile* pFlowLog,
                                               CLogFile* pMessageLog);
    bool DestroyHeartbeatHandler(CHeartbeatInterface* pHandler);
};
```

### 动态库定义
```cpp
#ifdef OS_LINUX
#define LIB_HEARTBEATHANDLER_NAME "libZclMltSrvHbtMngr.so"
#endif

#ifdef OS_WINDOWS
#define LIB_HEARTBEATHANDLER_NAME "ZclMltSrvHbtMngr.dll"
#endif
```

### 核心方法详解

#### InitLibrary - 初始化库
```cpp
bool InitLibrary();
```
- **功能**: 加载心跳处理动态库
- **执行流程**:
  1. 加载动态库文件
  2. 获取创建函数地址
  3. 获取销毁函数地址
  4. 验证函数指针有效性

#### CreateHeartbeatHandler - 创建心跳处理器
```cpp
CHeartbeatInterface* CreateHeartbeatHandler(bool pIfLogMessage, 
                                           CLogFile* pFlowLog,
                                           CLogFile* pMessageLog);
```
- **功能**: 创建心跳处理器实例
- **参数**:
  - `pIfLogMessage`: 是否记录消息日志
  - `pFlowLog`: 流程日志对象
  - `pMessageLog`: 消息日志对象
- **返回**: 心跳接口实例指针

## 3. ZxLibApi_Def_Heartbeat.h - 心跳定义文件

### 心跳信息结构体
```cpp
typedef struct StHeartbeatInfo {
    char serverId[32];           // 服务器ID
    char serverName[64];         // 服务器名称
    int serverStatus;            // 服务器状态
    int serverPriority;          // 服务器优先级
    time_t timestamp;            // 时间戳
    int stationCount;            // 管辖厂站数量
    char extInfo[256];           // 扩展信息
} StHeartbeatInfo;
```

### 心跳接收回调函数
```cpp
typedef int (*PFUNRECVHEARTBEAT)(void* pParam, StHeartbeatInfo& pInfo);
```

### 服务器状态定义
```cpp
enum SERVER_STATUS {
    SERVER_STATUS_OFFLINE = 0,    // 离线
    SERVER_STATUS_STANDBY = 1,    // 待机
    SERVER_STATUS_ACTIVE = 2,     // 活动
    SERVER_STATUS_FAULT = 3,      // 故障
    SERVER_STATUS_MAINTENANCE = 4 // 维护
};
```

## 使用示例

### 完整的心跳管理示例
```cpp
class HeartbeatManager {
private:
    CHeartbeatWrapper* m_pWrapper;
    CHeartbeatInterface* m_pHeartbeat;
    CLogFile m_logFile;
    CLogFile m_msgLog;
    
public:
    bool Initialize() {
        // 1. 创建包装器
        m_pWrapper = new CHeartbeatWrapper(m_logFile);
        
        // 2. 初始化动态库
        if (!m_pWrapper->InitLibrary()) {
            return false;
        }
        
        // 3. 创建心跳处理器
        m_pHeartbeat = m_pWrapper->CreateHeartbeatHandler(
            true,        // 记录消息日志
            &m_logFile,  // 流程日志
            &m_msgLog    // 消息日志
        );
        
        return (m_pHeartbeat != NULL);
    }
    
    // 配置为发送方
    void ConfigureAsSender() {
        // 注册广播地址
        m_pHeartbeat->RegisterBroadcastAddress("***************", 17255);
        m_pHeartbeat->RegisterBroadcastAddress("*************", 17255);
        
        // 启动发送线程
        StartSendingThread();
    }
    
    // 配置为接收方
    void ConfigureAsReceiver() {
        // 监听地址列表
        vector<string> listenAddrs;
        listenAddrs.push_back("0.0.0.0");  // 监听所有接口
        
        // 启动监听
        m_pHeartbeat->StartHeartbeatListening(listenAddrs, 17255, 30);
        
        // 注册回调
        m_pHeartbeat->RegisterHeartbeatListener(OnHeartbeatReceived, this);
    }
    
    // 发送心跳
    void SendHeartbeat() {
        StHeartbeatInfo info = {0};
        
        // 填充心跳信息
        strcpy(info.serverId, "SERVER001");
        strcpy(info.serverName, "主服务器");
        info.serverStatus = SERVER_STATUS_ACTIVE;
        info.serverPriority = 100;
        info.timestamp = time(NULL);
        info.stationCount = 50;
        sprintf(info.extInfo, "CPU:45%%, MEM:60%%");
        
        // 发送心跳
        m_pHeartbeat->BroatcastHeartbeat(info);
    }
    
    // 心跳接收回调
    static int OnHeartbeatReceived(void* pParam, StHeartbeatInfo& info) {
        HeartbeatManager* pThis = (HeartbeatManager*)pParam;
        
        printf("收到心跳: 服务器=%s, 状态=%d, 时间=%ld\n",
               info.serverId, info.serverStatus, info.timestamp);
        
        // 更新服务器状态表
        pThis->UpdateServerStatus(info);
        
        return 0;
    }
    
    // 发送线程
    void SendingThreadProc() {
        while (m_running) {
            SendHeartbeat();
            Sleep(10000);  // 10秒发送一次
        }
    }
    
    void Cleanup() {
        if (m_pHeartbeat) {
            m_pHeartbeat->EndHeartbeatListening();
            m_pWrapper->DestroyHeartbeatHandler(m_pHeartbeat);
        }
        delete m_pWrapper;
    }
};
```

### 双向心跳示例
```cpp
class BidirectionalHeartbeat {
private:
    CHeartbeatInterface* m_pHeartbeat;
    
public:
    void Setup() {
        // 同时配置发送和接收
        
        // 1. 配置发送
        m_pHeartbeat->RegisterBroadcastAddress("*********", 17255); // 组播地址
        
        // 2. 配置接收
        vector<string> listenAddrs;
        listenAddrs.push_back("0.0.0.0");
        m_pHeartbeat->StartHeartbeatListening(listenAddrs, 17255, 30);
        m_pHeartbeat->RegisterHeartbeatListener(OnPeerHeartbeat, this);
        
        // 3. 启动双向心跳
        StartBidirectionalHeartbeat();
    }
    
    static int OnPeerHeartbeat(void* pParam, StHeartbeatInfo& info) {
        BidirectionalHeartbeat* pThis = (BidirectionalHeartbeat*)pParam;
        
        // 处理对端心跳
        if (strcmp(info.serverId, pThis->m_localServerId) != 0) {
            pThis->ProcessPeerHeartbeat(info);
        }
        
        return 0;
    }
};
```

## 设计特点

### 1. 模块化设计
- 接口与实现分离
- 动态库加载机制
- 灵活的配置方式

### 2. 双向通信
- 支持发送和接收
- 单播和广播模式
- 组播支持

### 3. 可靠性设计
- 超时检测机制
- 状态监控
- 错误恢复

### 4. 扩展性
- 自定义心跳信息
- 灵活的回调机制
- 可扩展的状态定义

## 注意事项

1. **端口配置**
   - 默认使用17255端口
   - 确保端口未被占用
   - 防火墙需要开放UDP端口

2. **网络配置**
   - 广播需要网络支持
   - 组播需要路由器配置
   - 注意网络分段影响

3. **性能考虑**
   - 合理设置发送间隔
   - 避免心跳风暴
   - 注意CPU使用率

4. **错误处理**
   - 检查所有返回值
   - 处理网络异常
   - 日志记录关键事件

5. **资源管理**
   - 正确初始化和清理
   - 避免内存泄漏
   - 线程安全考虑
