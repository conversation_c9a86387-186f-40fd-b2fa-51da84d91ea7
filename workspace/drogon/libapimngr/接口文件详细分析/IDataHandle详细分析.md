# IDataHandle.h - 数据处理接口详细分析

## 文件概述
- **文件名**: IDataHandle.h
- **作者**: 李金梅
- **版本**: 1.0.0
- **功能**: STTP报文对比、入库的统一接口
- **创建日期**: 2015/04/10
- **多线程安全**: 是
- **异常安全**: 是，满足资源申请即初始化准则

## 数据结构定义

### DATAHANDLE_PARAM - 数据处理参数
```cpp
typedef struct _DATAHANDLE_PARAM {
    string str_logpath;           // 日志路径
    int n_loglevel;              // 日志级别
    long l_logfilesize;          // 日志文件大小限制
    int n_connect_num;           // 数据库连接数(1~30)
    int n_logday;                // 日志记录天数
    int n_historydatasave_num;   // 历史数据存储实例个数
    int n_rt_db_connet;          // 实时库连接标识 0:不连接 1:连接
}
```

**默认值说明**：
- `n_loglevel`: 3 (默认INFO级别)
- `l_logfilesize`: 10MB (10*1024*1024)
- `n_connect_num`: 1
- `n_logday`: 30天
- `n_historydatasave_num`: 0
- `n_rt_db_connet`: 0 (默认不连接)

## 回调函数定义

### PFUN_RCV_HDL_STTPDATA - 处理报文回调
```cpp
typedef int (*PFUN_RCV_HDL_STTPDATA)(void * pRegObj, STTP_FULL_DATA& sttp_data);
```
- **功能**: 处理接收到的STTP报文的回调函数
- **参数**:
  - `pRegObj`: 注册对象指针
  - `sttp_data`: STTP完整数据结构
- **返回**: 0成功，-1失败
- **创建时间**: 20161221 by xdy

## 核心接口函数详解

### 1. Init - 初始化函数
```cpp
virtual int Init(const DATAHANDLE_PARAM & da_param, CXJDBFacade* plibmngr_dbacess) = 0;
```
- **功能**: 初始化数据处理模块
- **参数**:
  - `da_param`: 数据处理参数配置
  - `plibmngr_dbacess`: 数据库访问指针（20200429新增，避免内部重复加载）
- **返回**: 0成功，1失败
- **执行流程**:
  1. 初始化日志系统
  2. 设置数据库连接参数
  3. 创建历史数据存储实例
  4. 初始化实时库连接（如果需要）
  5. 创建内部处理线程
- **版本变更**: 
  - 20200429: 增加数据库访问指针参数，优化资源管理

### 2. Uninit - 释放函数
```cpp
virtual int Uninit() = 0;
```
- **功能**: 释放数据处理模块资源
- **返回**: 0成功，1失败
- **执行流程**:
  1. 停止所有处理线程
  2. 清空消息队列
  3. 关闭数据库连接
  4. 释放历史数据存储实例
  5. 清理日志系统

### 3. HandleData - 处理STTP报文
```cpp
virtual int HandleData(STTP_FULL_DATA & sttpFullData) = 0;
```
- **功能**: 处理STTP报文，进行数据对比并入库
- **参数**:
  - `sttpFullData`: STTP完整报文数据
- **返回**: 0成功，1失败
- **执行流程**:
  1. 解析STTP报文头
  2. 根据消息类型分发处理
  3. 数据有效性校验
  4. 与当前值对比（死区判断）
  5. 更新实时库
  6. 写入历史库
  7. 触发事件通知
- **处理的报文类型**:
  - 遥测数据
  - 遥信数据
  - 电度数据
  - 事件数据
  - SOE数据

### 4. SetZxBusSwap_CmdLiveTm - 设置总线和命令超时
```cpp
virtual int SetZxBusSwap_CmdLiveTm(IZxBusSwap * pIZxBusSwap, 
                                   short nMsgTopicType,
                                   string strAppNodeName="",
                                   short nMsgChannel = -1, 
                                   int n_cmdLiveTm=10) = 0;
```
- **功能**: 设置总线客户端实例和STTP命令超时时间
- **参数**:
  - `pIZxBusSwap`: 总线客户端实例指针
  - `nMsgTopicType`: 消息主体类型
  - `strAppNodeName`: 应用节点名标识（用于D5K总线命令匹配）
  - `nMsgChannel`: 消息通道/事件集（-1使用默认）
  - `n_cmdLiveTm`: STTP命令超时时间（秒，默认10）
- **返回**: 0成功，1失败
- **使用场景**:
  - 配置总线通信参数
  - 设置命令响应超时
  - 多进程环境下的命令结果匹配

### 5. RegRcvHdlSttpMsgCallBak - 注册回调函数
```cpp
virtual int RegRcvHdlSttpMsgCallBak(void * pRegObj, PFUN_RCV_HDL_STTPDATA pCallBack) = 0;
```
- **功能**: 注册接收处理报文的回调函数
- **参数**:
  - `pRegObj`: 原注册对象指针
  - `pCallBack`: 回调函数指针
- **返回**: 0成功，-1失败
- **创建者**: 20161221 xdy
- **使用说明**:
  - 允许外部模块接收处理后的数据
  - 支持多个回调函数注册
  - 回调函数在数据处理完成后调用

## 实例创建和销毁

### create_inst - 创建实例
```cpp
IDataHandle* create_inst();
```
- **功能**: 创建数据处理接口实例
- **返回**: 指向具体派生类的IDataHandle指针
- **说明**: 工厂方法模式

### destroy_inst - 销毁实例
```cpp
int destroy_inst(IDataHandle *pInst);
```
- **功能**: 销毁数据处理接口实例
- **参数**:
  - `pInst`: 要销毁的实例指针
- **返回**: 0成功，1失败

## 使用示例

### 完整的数据处理流程
```cpp
// 1. 创建数据库访问实例
CXJDBFacade* pDbFacade = new CXJDBFacade(&logFile);
pDbFacade->LoadLib();
pDbFacade->Connect(MODULE_ID, errorMsg);

// 2. 创建数据处理实例
IDataHandle* pDataHandle = create_inst();

// 3. 配置初始化参数
DATAHANDLE_PARAM param;
param.str_logpath = "/var/log/datahandle";
param.n_loglevel = 3;
param.n_connect_num = 10;
param.n_logday = 30;
param.n_historydatasave_num = 5;
param.n_rt_db_connet = 1;

// 4. 初始化
if (pDataHandle->Init(param, pDbFacade) == 0) {
    
    // 5. 设置总线参数
    IZxBusSwap* pBusSwap = /* 获取总线实例 */;
    pDataHandle->SetZxBusSwap_CmdLiveTm(pBusSwap, 
                                        MSG_TOPIC_DATA,
                                        "DataHandler",
                                        -1,
                                        15);
    
    // 6. 注册回调函数
    pDataHandle->RegRcvHdlSttpMsgCallBak(this, OnDataProcessed);
    
    // 7. 处理数据
    STTP_FULL_DATA sttpData;
    // ... 填充STTP数据
    pDataHandle->HandleData(sttpData);
    
    // 8. 清理
    pDataHandle->Uninit();
}

// 9. 销毁实例
destroy_inst(pDataHandle);
delete pDbFacade;

// 回调函数实现
int OnDataProcessed(void* pObj, STTP_FULL_DATA& sttpData) {
    // 处理已入库的数据
    printf("数据已处理: MsgId=%d\n", sttpData.head.msgid);
    return 0;
}
```

### STTP数据处理详细流程
```cpp
// HandleData内部处理流程示例
int HandleData(STTP_FULL_DATA& sttpData) {
    // 1. 解析消息类型
    switch(sttpData.head.msgid) {
        case STTP_MSG_YC:  // 遥测数据
            return HandleYcData(sttpData);
            
        case STTP_MSG_YX:  // 遥信数据
            return HandleYxData(sttpData);
            
        case STTP_MSG_DD:  // 电度数据
            return HandleDdData(sttpData);
            
        case STTP_MSG_SOE: // SOE事件
            return HandleSoeData(sttpData);
            
        default:
            return HandleOtherData(sttpData);
    }
}

// 遥测数据处理
int HandleYcData(STTP_FULL_DATA& sttpData) {
    // 1. 提取遥测值
    float value = ExtractYcValue(sttpData);
    
    // 2. 死区判断
    float oldValue = GetOldValue(sttpData.pt_id);
    if (fabs(value - oldValue) < deadZone) {
        return 0; // 在死区内，不处理
    }
    
    // 3. 数据质量检查
    if (!CheckDataQuality(sttpData)) {
        return -1;
    }
    
    // 4. 更新实时库
    UpdateRealtimeDb(sttpData.pt_id, value);
    
    // 5. 写入历史库
    WriteHistoryDb(sttpData.pt_id, value, sttpData.timestamp);
    
    // 6. 触发回调
    TriggerCallbacks(sttpData);
    
    return 0;
}
```

## 设计特点

### 1. 模块化设计
- 数据处理与数据库访问分离
- 支持多种数据源接入
- 灵活的回调机制

### 2. 性能优化
- 支持批量数据处理
- 多线程并发处理
- 连接池复用

### 3. 可靠性保障
- 数据完整性校验
- 异常恢复机制
- 日志追踪

### 4. 扩展性
- 支持新的STTP消息类型
- 可配置的处理策略
- 插件式回调机制

## 注意事项

1. **资源管理**
   - Init必须与Uninit配对使用
   - 数据库指针由外部管理，内部不负责释放

2. **线程安全**
   - HandleData是线程安全的，可并发调用
   - 回调函数需要自行保证线程安全

3. **性能考虑**
   - 批量数据建议分批处理
   - 合理设置连接池大小
   - 注意历史数据存储实例数量

4. **错误处理**
   - 所有接口都有返回值，需检查
   - 通过日志系统记录详细错误信息

5. **版本兼容**
   - 20200429版本后Init函数参数有变化
   - 注意数据库指针参数的正确传递
