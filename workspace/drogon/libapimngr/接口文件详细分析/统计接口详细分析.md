# 统计接口详细分析

本文档分析libapimngr中的三个统计相关接口：IZasStatCk、IZasStatDiagn和IZasStatFlt。

## 1. IZasStatCk.h - 专业巡视统计接口

### 文件概述
- **文件名**: IZasStatCk.h
- **作者**: 沈亚东
- **版本**: 1.0.0
- **功能**: 专业巡视统计功能接口
- **创建日期**: 2020/08/11

### 数据结构定义

#### STATCK_SET - 专业巡视统计参数
```cpp
typedef struct _STATCK_SET {
    string strBeginTime;      // 统计起始时间
    string strEndTime;        // 统计终止时间
    string strXmlRootPath;    // 统计文件存放根路径
    int nStatType;           // 统计类型
    string strStatId;        // 统计对象ID
    int nReportFlag;         // 报告标识
    int nReserve;            // 备用字段
    string strReserve;       // 概要文件名（nReportFlag=10005时）
    string strReserve2;      // 详细文件名（nReportFlag=10005时）
}
```

#### 统计类型枚举
```cpp
// nStatType取值：
0   - 统计指定地区下的专业巡视信息
1   - 统计指定厂站的专业巡视信息
2   - 统计指定生产厂家的专业巡视信息
3   - 统计指定二次设备的专业巡视信息
99  - 统计全网的专业巡视信息
```

#### 报告标识枚举
```cpp
// nReportFlag取值：
10001 - 月报
10002 - 季报
10003 - 年报
10004 - 用户指定时间范围
10005 - 用户指定时间范围和文件名
```

### 核心接口函数

#### 1. Init - 初始化
```cpp
virtual int Init(const string &strRootLogPath, 
                int nLogDays, 
                int nLogLevel, 
                const string &strReserved) = 0;
```
- **功能**: 初始化专业巡视统计模块
- **参数**:
  - `strRootLogPath`: 日志根路径
  - `nLogDays`: 日志保存天数
  - `nLogLevel`: 日志等级
  - `strReserved`: 备用参数
- **返回**: 0成功，1失败

#### 2. Uninit - 释放
```cpp
virtual int Uninit() = 0;
```
- **功能**: 释放统计模块资源
- **返回**: 0成功，1失败

#### 3. StartStatistic - 启动统计
```cpp
virtual int StartStatistic(const STATCK_SET & statistics) = 0;
```
- **功能**: 启动专业巡视统计任务
- **参数**:
  - `statistics`: 统计参数配置
- **返回**: 0成功，1失败
- **执行流程**:
  1. 解析统计参数
  2. 连接数据库
  3. 查询巡视数据
  4. 生成统计结果
  5. 输出XML报告

#### 4. CreatXML_HuNan - 湖南特定XML生成
```cpp
virtual int CreatXML_HuNan(CMessageLog* pLogFile, 
                          CXJDBFacade* pDBFacade, 
                          const string& strCloudAreaId,
                          const string& strStnId, 
                          const int& nZipDay,
                          const string& strBeginTime, 
                          const string& strInPath,
                          string& strOutPath) = 0;
```
- **功能**: 生成湖南地区特定格式的数据XML
- **参数**:
  - `pLogFile`: 日志对象指针
  - `pDBFacade`: 数据库对象指针
  - `strCloudAreaId`: 调控云地区ID
  - `strStnId`: 厂站ID
  - `nZipDay`: 压缩天数
  - `strBeginTime`: 开始时间(YYYY-MM-DD HH:MM:SS)
  - `strInPath`: 压缩包存放路径
  - `strOutPath`: 输出的压缩包完整路径
- **返回**: 0成功，1失败

## 2. IZasStatDiagn.h - 诊断统计接口

### 文件概述
- **文件名**: IZasStatDiagn.h
- **作者**: 沈亚东
- **版本**: 1.0.0
- **功能**: 设备诊断统计功能接口
- **创建日期**: 2018/09/26

### 数据结构定义

#### STATDIAGN_SET - 诊断统计参数
```cpp
typedef struct _STATDIAGN_SET {
    string strBeginTime;      // 统计起始时间
    string strEndTime;        // 统计终止时间
    string strXmlRootPath;    // 统计文件存放根路径
    int nStatType;           // 统计类型
    string strStatId;        // 统计对象ID
    int nReportFlag;         // 报告标识
    int nReserve;            // 备用字段
    string strReserve;       // 概要文件名
    string strReserve2;      // 详细文件名
}
```

#### 统计类型枚举
```cpp
enum {
    DIAGNSTAT_TYP_AREA = 0,  // 按地区统计
    DIAGNSTAT_TYP_STN = 1,   // 按厂站统计
    DIAGNSTAT_TYP_MFGR = 2,  // 按厂家统计
    DIAGNSTAT_TYP_ALL = 99   // 全网统计
};
```

#### 报告标识枚举
```cpp
enum {
    DIAGNSTAT_RPT_M = 10001,  // 月报
    DIAGNSTAT_RPT_Q = 10002,  // 季报
    DIAGNSTAT_RPT_Y = 10003,  // 年报
    DIAGNSTAT_RPT_R = 10004   // 随机报告
};
```

### 核心接口函数

接口函数与IZasStatCk基本相同：
- `Init()` - 初始化
- `Uninit()` - 释放
- `StartStatistic()` - 启动统计

主要区别在于统计的内容是设备诊断信息而非巡视信息。

## 3. IZasStatFlt.h - 故障统计接口

### 文件概述
- **文件名**: IZasStatFlt.h
- **作者**: 李金梅
- **版本**: 1.0.0
- **功能**: 故障统计功能接口
- **创建日期**: 2015/04/10

### 数据结构定义

#### STATFLT_SET - 故障统计参数
```cpp
typedef struct _statflt_set {
    string strBeginTime;         // 统计起始时间
    string strEndTime;           // 统计终止时间
    string strXmlRootPath;       // 统计文件存放根路径
    int nStatType;              // 统计类型
    string strStatId;           // 统计对象ID
    int nReportFlag;            // 报告标识
    int nReserve;               // 备用字段
    string strReserve;          // 备用字段
    string strReserve2;         // 详细xml名称
    MTMP AreaVolStat_List;      // 按地区统计电压等级
}
```

#### 特殊类型定义
```cpp
typedef map<string,string> MTMP;  // 地区-电压等级映射
```

#### 统计类型枚举
```cpp
enum {
    FLTSTAT_TYP_AREA = 0,   // 按地区统计故障
    FLTSTAT_TYP_STN = 1,    // 按厂站统计故障
    FLTSTAT_TYP_MFGR = 2,   // 按厂家统计故障
    FLTSTAT_TYP_ALL = 99    // 全网故障统计
};
```

#### 报告标识枚举
```cpp
enum {
    FLTSTAT_RPT_M = 10001,   // 月报
    FLTSTAT_RPT_Q = 10002,   // 季报
    FLTSTAT_RPT_Y = 10003,   // 年报
    FLTSTAT_RPT_R = 10004    // 随机报告
};
```

### 核心接口函数

接口函数与前两个相同，主要区别：
- 增加了按电压等级统计的功能
- 支持地区-电压等级的映射关系

## 使用示例

### 1. 月度巡视统计
```cpp
// 创建巡视统计实例
IZasStatCk* pStatCk = createIZasStatCk();

// 初始化
pStatCk->Init("/var/log/stat", 30, 3, "");

// 配置月报参数
STATCK_SET ckParam;
ckParam.strBeginTime = "2024-01-01 00:00:00";
ckParam.strEndTime = "2024-01-31 23:59:59";
ckParam.strXmlRootPath = "/data/reports/check";
ckParam.nStatType = 99;        // 全网统计
ckParam.nReportFlag = 10001;   // 月报

// 启动统计
if (pStatCk->StartStatistic(ckParam) == 0) {
    printf("巡视月报生成成功\n");
}

// 清理
pStatCk->Uninit();
destroyIZasStatCk(pStatCk);
```

### 2. 厂站故障统计
```cpp
class StationFaultStatistic {
private:
    IZasStatFlt* m_pStatFlt;
    
public:
    void GenerateStationReport(const string& stnId) {
        m_pStatFlt = createIZasStatFlt();
        m_pStatFlt->Init("/var/log/fault", 30, 3, "");
        
        STATFLT_SET fltParam;
        fltParam.strBeginTime = GetMonthBegin();
        fltParam.strEndTime = GetMonthEnd();
        fltParam.strXmlRootPath = "/data/reports/fault";
        fltParam.nStatType = FLTSTAT_TYP_STN;  // 按厂站统计
        fltParam.strStatId = stnId;            // 指定厂站
        fltParam.nReportFlag = FLTSTAT_RPT_M;  // 月报
        
        // 执行统计
        m_pStatFlt->StartStatistic(fltParam);
        
        // 清理
        m_pStatFlt->Uninit();
        destroyIZasStatFlt(m_pStatFlt);
    }
};
```

### 3. 综合统计报告
```cpp
class ComprehensiveReport {
private:
    IZasStatCk* m_pStatCk;
    IZasStatDiagn* m_pStatDiagn;
    IZasStatFlt* m_pStatFlt;
    
public:
    void GenerateQuarterlyReport() {
        string beginTime = "2024-01-01 00:00:00";
        string endTime = "2024-03-31 23:59:59";
        string reportPath = "/data/reports/Q1_2024";
        
        // 1. 巡视统计
        GenerateCheckReport(beginTime, endTime, reportPath);
        
        // 2. 诊断统计
        GenerateDiagnReport(beginTime, endTime, reportPath);
        
        // 3. 故障统计
        GenerateFaultReport(beginTime, endTime, reportPath);
        
        // 4. 合并报告
        MergeReports(reportPath);
    }
    
private:
    void GenerateCheckReport(const string& begin, const string& end, 
                           const string& path) {
        m_pStatCk = createIZasStatCk();
        m_pStatCk->Init("/var/log", 30, 3, "");
        
        STATCK_SET param;
        param.strBeginTime = begin;
        param.strEndTime = end;
        param.strXmlRootPath = path + "/check";
        param.nStatType = 99;
        param.nReportFlag = 10002;  // 季报
        
        m_pStatCk->StartStatistic(param);
        m_pStatCk->Uninit();
        destroyIZasStatCk(m_pStatCk);
    }
    
    // 类似的诊断和故障报告生成函数...
};
```

### 4. 自定义文件名统计
```cpp
void CustomFileNameStatistic() {
    IZasStatDiagn* pStat = createIZasStatDiagn();
    pStat->Init("/var/log", 30, 3, "");
    
    STATDIAGN_SET param;
    param.strBeginTime = "2024-01-01 00:00:00";
    param.strEndTime = "2024-01-31 23:59:59";
    param.strXmlRootPath = "/data/custom";
    param.nStatType = DIAGNSTAT_TYP_ALL;
    param.nReportFlag = 10005;  // 自定义文件名
    param.strReserve = "January_2024_Summary.xml";     // 概要文件名
    param.strReserve2 = "January_2024_Details.xml";    // 详细文件名
    
    pStat->StartStatistic(param);
    pStat->Uninit();
    destroyIZasStatDiagn(pStat);
}
```

### 5. 按电压等级故障统计
```cpp
void VoltageBasedFaultStatistic() {
    IZasStatFlt* pFault = createIZasStatFlt();
    pFault->Init("/var/log", 30, 3, "");
    
    STATFLT_SET param;
    param.strBeginTime = "2024-01-01 00:00:00";
    param.strEndTime = "2024-12-31 23:59:59";
    param.strXmlRootPath = "/data/voltage_fault";
    param.nStatType = FLTSTAT_TYP_AREA;
    param.strStatId = "AREA001";
    param.nReportFlag = FLTSTAT_RPT_Y;  // 年报
    
    // 设置电压等级映射
    param.AreaVolStat_List["AREA001"] = "500kV";
    param.AreaVolStat_List["AREA002"] = "220kV";
    param.AreaVolStat_List["AREA003"] = "110kV";
    
    pFault->StartStatistic(param);
    pFault->Uninit();
    destroyIZasStatFlt(pFault);
}
```

## 输出XML格式示例

### 巡视统计XML
```xml
<?xml version="1.0" encoding="UTF-8"?>
<CheckStatistic>
    <Header>
        <ReportType>Monthly</ReportType>
        <Period>2024-01</Period>
        <GenerateTime>2024-02-01 08:00:00</GenerateTime>
    </Header>
    <Summary>
        <TotalChecks>1250</TotalChecks>
        <NormalCount>1200</NormalCount>
        <AbnormalCount>50</AbnormalCount>
    </Summary>
    <Details>
        <Station id="STN001">
            <CheckCount>50</CheckCount>
            <AbnormalCount>2</AbnormalCount>
        </Station>
        <!-- 更多厂站... -->
    </Details>
</CheckStatistic>
```

### 故障统计XML
```xml
<?xml version="1.0" encoding="UTF-8"?>
<FaultStatistic>
    <Header>
        <ReportType>Quarterly</ReportType>
        <Period>2024-Q1</Period>
    </Header>
    <Summary>
        <TotalFaults>35</TotalFaults>
        <ByVoltageLevel>
            <Level voltage="500kV">5</Level>
            <Level voltage="220kV">12</Level>
            <Level voltage="110kV">18</Level>
        </ByVoltageLevel>
    </Summary>
    <Details>
        <!-- 详细故障记录 -->
    </Details>
</FaultStatistic>
```

## 设计特点

### 1. 统一接口
- 三个统计接口具有相同的基本结构
- 便于扩展新的统计类型
- 统一的初始化和执行流程

### 2. 灵活配置
- 支持多种统计维度
- 可自定义报告周期
- 支持自定义文件名

### 3. 报告类型
- 标准化的月报、季报、年报
- 支持自定义时间范围
- XML格式便于后续处理

### 4. 扩展性
- 预留字段支持特殊需求
- 支持地区特定格式（如湖南）
- 可添加新的统计维度

## 注意事项

1. **时间格式**
   - 统一使用"YYYY-MM-DD HH:MM:SS"格式
   - 注意时区处理
   - 结束时间通常为23:59:59

2. **路径管理**
   - 确保输出路径存在且可写
   - 注意磁盘空间
   - 定期清理历史报告

3. **性能考虑**
   - 大数据量统计可能耗时较长
   - 考虑使用异步处理
   - 合理设置查询条件

4. **错误处理**
   - 检查所有返回值
   - 记录详细日志
   - 处理数据库连接异常

5. **并发控制**
   - 避免同时运行多个统计任务
   - 使用文件锁或数据库锁
   - 注意资源竞争
