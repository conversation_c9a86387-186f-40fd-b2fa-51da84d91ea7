# libapimngr - API定义文件详细分析

## 概述
libapimngr目录包含了系统各个模块的API定义文件，这些文件定义了模块间的接口规范、数据结构和函数原型。

## API定义文件列表

### 1. ZxLibApi_Def_Heartbeat.h - 心跳处理API定义

#### 常量定义
```cpp
const int HEARTBEAT_TYPE_INFO = 0;      // 工况信息心跳
const int HEARTBEAT_TYPE_OPERATION = 1; // 手动切换心跳
```

#### 数据结构

##### StHBSubstationStatus - 子站状态
```cpp
typedef struct _STRUCT_HBSUBSTATION_STATUS {
    int iStatus;              // 状态
    string strStationID;      // 厂站ID
} StHBSubstationStatus;
```

##### StHeartbeatInfo - 心跳信息
```cpp
typedef struct _Struct_Heartbeat_Info {
    WORD wSerialNo;           // 序列号
    int iType;                // 心跳类型 0:工况信息 1:手动切换
    int iGroup;               // 服务器分组号
    int iRunStatus;           // 运行状态 0-不可用 1-可用
    int iOnlineStatus;        // 在线状态 -1-未知 0-Standby 1-Active
    int iLoadSoccer;          // 负载评分
    time_t tTimed;            // 时标
    string strServerID;       // 服务器ID
    string strHBAddr;         // 心跳网IP地址
    string strNetAAddr;       // A网IP地址
    string strNetBAddr;       // B网IP地址
    vector<StHBSubstationStatus> listStationID; // 所辖厂站状态
} StHeartbeatInfo;
```

#### 回调函数
```cpp
typedef void (*PFUNRECVHEARTBEAT)(LPVOID, StHeartbeatInfo&);
```

### 2. ZxLibApi_SrvOnlineMngr.h - 服务器在线管理API

#### 常量定义
```cpp
const int SRV_ONLINE_UNKNOWN = -1;  // 服务器在线状态-未知
const int SRV_ONLINE_STANDBY = 0;   // 服务器在线状态-备用
const int SRV_ONLINE_ACTIVE = 1;    // 服务器在线状态-在线

const int SUB_LOADED_UNKNOWN = -1;  // 子站挂载状态-未知
const int SUB_LOADED_OFF = 0;       // 子站挂载状态-未挂载
const int SUB_LOADED_ON = 1;        // 子站挂载状态-已挂载
```

#### 数据结构

##### stXJSrvOnlineManager - 在线管理器配置
```cpp
typedef struct _STRUCT_XJSrvOnlineManager {
    char chServerID[41];              // 服务器ID
    char szLog_path[FILE_NAME_MAX_LEN]; // 日志路径
    int iLog_level;                   // 日志级别
    int iLogDay;                      // 日志保存天数
    bool bRecordMsg;                  // 是否记录报文
    bool bReserved;                   // 保留
    int iReserved;                    // 保留(是否61850前置)
    char reservedStr[256];            // 保留(61850厂站ID)
} stXJSrvOnlineManager;
```

##### stXJSubstation - 子站信息
```cpp
typedef struct _STRUCT_SUBSTATION {
    int iType;                        // 类型
    int iAddr;                        // 地址
    int iPort;                        // 端口
    int iPortReserve;                 // 备用端口
    int iProtocolID;                  // 协议ID
    int iIPFlag;                      // IP标志
    char chStationID[13];             // 厂站ID
    char chStationName[51];           // 厂站名称
    char chIP[19];                    // IP地址
    char chIPReserve[19];             // 备用IP
    char chTelphone[51];              // 电话号码
} stXJSubstation;
```

##### stXJSubstationLoadStatus - 子站加载状态
```cpp
typedef struct _STRUCT_SUBSTATION_LOAD_STATUS {
    char chStationID[13];             // 厂站ID
    int iStatus;                      // 状态
} stXJSubstationLoadStatus;
```

#### 函数定义
```cpp
// 启动/停止在线管理器
typedef int (*PFUNSTARTSRVONLINEMANAGER)(const stXJSrvOnlineManager&);
typedef int (*PFUNSTOPSRVONLINEMANAGER)();

// 服务器切换回调
typedef int (*PFUNONSRVSWITCH)(void* pParam, int pStatus, 
                               vector<stXJSubstation>& pStationList);

// 注册切换回调
typedef int (*PFUNREGISTERSRVSWITCHCALLBACK)(PFUNONSRVSWITCH pOnSrvSwitch, 
                                             void* pParam);

// 设置厂站加载状态
typedef int (*PFUNSETSTATIONLOADSTATUS)(stXJSubstationLoadStatus& pStatus);
```

### 3. ZxLibApi_LoadAcquirer.h - 负载采集器API

#### 库名定义
```cpp
#ifdef OS_LINUX
#define LIB_LOADACQUIRER_NAME "libZclGetSrvLoad.so"
#endif

#ifdef OS_WINDOWS
#define LIB_LOADACQUIRER_NAME "ZclGetSrvLoad.dll"
#endif
```

#### 数据结构

##### stXJLoadAcquirer - 负载采集配置
```cpp
typedef struct _STRUCT_XJLOADACQUIRER {
    int iProThreadWeight;      // 进程线程资源权重
    int iProHandlerWeight;     // 进程句柄资源权重
    int iProCPUWeight;         // 进程CPU资源权重
    int iProMemoryWeight;      // 进程内存资源权重
    int iSrvThreadWeight;      // 服务器线程资源权重
    int iSrvHandlerWeight;     // 服务器句柄资源权重
    int iSrvCPUWeight;         // 服务器CPU资源权重
    int iSrvMemoryWeight;      // 服务器内存资源权重
    bool bReserved;            // 保留
    int iReserved;             // 保留
    char reservedStr[256];     // 保留
} stXJLoadAcquirer;
```

#### 函数定义
```cpp
// 启动负载采集器
typedef int (*PStartLoadAcquirer)(const stXJLoadAcquirer&, CLogFile&);

// 停止负载采集器
typedef int (*PStopLoadAcquirer)();

// 请求负载得分
typedef int (*PAcquireLoadSoccer)();
```

### 4. ZxLibApi_Def_MsgMgr.h - 报文管理器API定义

#### 常量定义
```cpp
// 缓冲区大小
#define MSG_BUFFER_SIZE 0x2000              // 报文缓冲长度(8KB)
#define MSG_DES_BUFFER_SIZE (4*MSG_BUFFER_SIZE) // 报文描述缓冲长度(32KB)
#define NAME_SIZE 64                        // 名字缓冲长度

// 报文方向
#define MSG_DIRECT_SEND 0                   // 报文发送方向
#define MSG_DIRECT_RECV 1                   // 报文接收方向

// 保存格式
#define MSG_SAVE_FMT_STR 1                  // 字符格式
#define MSG_SAVE_FMT_ASC 2                  // 二进制格式

// 配置文件
#define MSGMGR_CONFIG_FILE_NAME "xjmsgmgr.ini"
```

#### 协议类型定义
```cpp
// STTP协议
#define XJ_STTP_PROTOCL 1

// 103协议系列
#define XJ_HD103_PROTOCL  100   // 华东103
#define XJ_HB103_PROTOCL  101   // 华北103
#define XJ_DB103_PROTOCL  102   // 东北103
#define XJ_NF103_PROTOCL  103   // 南方103
#define XJ_HUB103_PROTOCL 104   // 湖北103
#define XJ_LL103_PROTOCL  105   // 辽宁103
#define XJ_ZJ103_PROTOCL  106   // 浙江103
#define XJ_BJ103_PROTOCL  107   // 北京103
#define XJ_FJ103_PROTOCL  108   // 福建103
#define XJ_JS103_PROTOCL  109   // 江苏103
#define XJ_NX103_PROTOCL  110   // 宁夏103
#define XJ_HN103_PROTOCL  120   // 河南103
#define XJ_SH103_PROTOCL  121   // 上海103

// 104协议系列
#define XJ_HD104_PROTOCL  200   // 华东104
#define XJ_HB104_PROTOCL  201   // 华北104
// ... 其他104协议

// 101协议系列
#define XJ_HD101_PROTOCL  300   // 华东101
#define XJ_SH101_PROTOCL  301   // 上海101
```

#### 数据结构

##### XJMSGFRM - 报文帧格式
```cpp
typedef struct xjmsgframe {
    int type;                               // 规约类型
    int direct;                             // 报文方向(0:接收,1:发送)
    char name[NAME_SIZE];                   // 标识名称
    char from_ip[40];                       // 发送方IP
    int from_port;                          // 发送方端口
    char to_ip[40];                         // 接收方IP
    int to_port;                            // 接收方端口
    int reserved;                           // 保留字段
    int save_msg_fmt;                       // 保存格式
    int buf_len;                            // 实际报文长度
    int des_len;                            // 实际描述长度
    char buffer[MSG_BUFFER_SIZE];           // 报文内容
    char descript[MSG_DES_BUFFER_SIZE];     // 报文描述
} XJMSGFRM;
```

##### XJMSGMGR_CONF - 报文管理器配置
```cpp
typedef struct xjmsgmgr_conf {
    char user[NAME_SIZE];                   // 使用者模块名称
    char listen_ip[40];                     // 侦听IP
    int listen_port;                        // 侦听端口
    char log_path[256];                     // 日志路径
    int log_level;                          // 日志级别
    bool bRecordMsg;                        // 是否记录报文
    int reserved;                           // 保留
} XJMSGMGR_CONF;
```

### 5. ZxLibApi_SttpProxy.h - STTP代理API

#### 数据结构

##### XJSubstationRunStatus - 子站运行状态
```cpp
typedef struct _STRUCT_SUBSTATION_STATUS {
    int iStatus;                            // 状态
    string strStationID;                    // 厂站ID
} XJSubstationRunStatus;
```

##### XJServerRunStatus - 服务器运行状态
```cpp
typedef struct _XJServerRunStatus {
    int iGroup;                             // 服务器分组号
    int iOnlineStatus;                      // 在线状态(-1:未知,0:备用,1:在线)
    int iLoadSoccer;                        // 负载评分
    string strServerID;                     // 服务器ID
    vector<XJSubstationRunStatus> listStationStatus; // 厂站状态列表
} XJServerRunStatus;

typedef vector<XJServerRunStatus> XJServerStatusSet;
```

#### 函数定义
```cpp
// 连接服务器
typedef XJHANDLE (*PFUNSTTPCONNECTSERVER)(int pClientModle, int& pConnected, 
                                          const char* pLogPath, int pRunModle);

// 关闭连接
typedef int (*PFUNSTTPCLOSECONNECTION)(XJHANDLE pClientHandle);

// 活动测试
typedef int (*PFUNSTTPACTIVETEST)(XJHANDLE pClientHandle);

// 读取数据
typedef int (*PFUNSTTPREAD)(XJHANDLE pClientHandle, STTP_FULL_DATA& pSttpData);

// 写入数据
typedef int (*PFUNSTTPWRITE)(XJHANDLE pClientHandle, const STTP_FULL_DATA& pSttpData);

// 设置选项
typedef int (*PFUNSTTPSETOPTION)(XJHANDLE pClientHandle, int pOptionName, 
                                 int pTime, int pReserved);

// 注册处理器
typedef int (*PFUNSTTPREGISTERHANDLER)(XJHANDLE pClientHandle, 
                                       PFUNCALLBACK pFunction, int pMask);

// 注册连接变化处理器
typedef int (*PFUNSTTPREGISTERCONNECTCHANGERHANDLER)(XJHANDLE pClientHandle,
                                                     PFUNONCONNECTCHANGE pOnConnectChange, 
                                                     void* pParam);

// 设置日志路径
typedef int (*PFUNSTTPSETLOGPATH)(XJHANDLE pClientHandle, const char* pLogPath);

// 连接目标服务器
typedef XJHANDLE (*PFUNSTTPCONNECTDESTINATION)(const STRUCT_DESTINATION_SERVER& pDestination,
                                               int pClientModle, int& pConnected, 
                                               const char* pLogPath, int pRunModle);

// 查询服务器状态
typedef int (*PFUNSTTPQUERYSERVERSTATUS)(XJHANDLE pClientHandle, 
                                         XJServerStatusSet& pSrvStatusSet);
```

### 6. ZxLibApi_SubStationInit.h - 子站初始化API

#### 常量定义

##### 数据库类型
```cpp
const int _DATABASE_SQL_SERVER = 1;    // SQL SERVER
const int _DATABASE_ORACLE = 2;        // ORACLE
const int _DATABASE_MYSQL = 3;         // MYSQL
```

##### 错误类型
```cpp
const int XJERROR_UNKNOWN_ERROR = 0;               // 未知错误
const int XJERROR_CONNECT_XJCOMMUSERVER_FAIL = -1; // 连接通讯服务器失败
const int XJERROR_CONNECT_XJDBSERVER_FAIL = -2;    // 连接数据平台失败
const int XJERROR_OPERATION_SUCCESS = 1;           // 操作成功
```

##### 访问类型
```cpp
const int XJACCESS_DATABASE = 0;       // 直接访问数据库
const int XJACCESS_DBSERVER = 1;       // 通过数据平台访问
```

##### 初始化类型

###### 厂站初始化类型
```cpp
const int STATION_INIT_ALL = 0;        // 整个站的信息
const int STATION_INIT_LINE = 1;       // 线路
const int STATION_INIT_SECDEV = 2;     // 二次设备
const int STATION_INIT_GROUP = 3;      // 组标题
const int STATION_INIT_BREAKER = 4;    // 开关
const int STATION_INIT_BUS = 5;        // 母线
const int STATION_INIT_TRANS = 6;      // 变压器
const int STATION_INIT_GEN = 7;        // 发电机
const int STATION_INIT_REACTOR = 8;    // 电抗器
const int STATION_INIT_CAP = 9;        // 电容器
const int STATION_INIT_SWITCH = 10;    // 刀闸
```

###### 设备初始化类型
```cpp
const int DEVICE_INIT_ALL = 0;         // 所有属性
const int DEVICE_INIT_SETTING = 1;     // 定值
const int DEVICE_INIT_AI = 2;          // 模拟量
const int DEVICE_INIT_SOFTBOARD = 3;   // 软压板
const int DEVICE_INIT_ZONE = 4;        // 定值区号
const int DEVICE_INIT_STAUTS = 5;      // 状态量
const int DEVICE_INIT_FAULT = 6;       // 故障量
const int DEVICE_INIT_GROUP = 7;       // 组标题
```

##### 同步方向
```cpp
const int STATION_DIRECTIOR_DOWN = 0;  // 主站下装信息到子站
const int STATION_DIRECTIOR_GET = 1;   // 主站从子站调用信息
```

##### 描述类型
```cpp
const int DEVICE_DES_ALL = 0;          // 初始化全部
const int DEVICE_DES_DESCRIBE = 1;     // 装置描述
const int DEVICE_DES_PROPERTY = 2;     // 装置属性结构
```

##### 默认值
```cpp
const int INIT_GROUP_NUM = 256;        // 默认组号(全部组)
const int INIT_CPU_NUM = 99;           // 默认CPU号(全部CPU)
```

#### 数据结构

##### XJ_DATABASE_PARAM - 数据库参数
```cpp
typedef struct _XJ_DATABASE_PARAM {
    bool bUseDbServer;                     // 是否使用数据平台
    int iDataBaseType;                     // 数据库类型
    int iDataBasePort;                     // 数据库端口
    char chDataBaseIp[MAX_FILE_LENGTH];    // 数据库IP
    char chDataBaseName[MAX_FILE_LENGTH];  // 数据库实例名
    char chUserName[MAX_FILE_LENGTH];      // 用户名
    char chPassword[MAX_FILE_LENGTH];      // 密码
} XJ_DATABASE_PARAM;
```

#### 函数定义
```cpp
// 启动库
typedef int (*StartLib)(int iModuleID, int iDbConnType, 
                       XJ_DATABASE_PARAM& database_param);

// 关闭库
typedef int (*EndLib)();

// 设置日志路径
typedef int (*SetLibLogPath)(const char* pLogPath);

// 接收初始化消息
typedef int (*RecvInitializeMsg)(STTP_FULL_DATA& sttp_retrun_msg);

// 厂站初始化
typedef int (*StationInitialize)(const char* pStationID, int iInitType, 
                                 int iInitDirection, int iGroupNum, int iCpuNum);

// 设备初始化
typedef int (*DeviceInitialize)(const char* pDeviceID, int iInitType, 
                               int iInitDes, int iGroupNum, int iCpuNum);

// 删除厂站信息
typedef int (*RemoveStationInfo)(const char* pStationID, bool bDelStationSelf);
```

## 继续分析更多API文件...
