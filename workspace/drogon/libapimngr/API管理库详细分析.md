# libapimngr - API管理库详细分析

## 概述

libapimngr是Dragon项目的核心API管理库，负责统一管理各种动态库接口、提供数据库访问封装、消息总线通信等基础服务。该模块是整个系统的基础设施层。

## 核心组件分析

### 1. 总线通信模块

#### IZxBusSwap.h - 总线交换接口定义
**功能**：定义了与总线数据收发、定制等一系列接口

**关键结构体**：
- `APP_NODE_INFO`: 应用结点基本信息
- `BUS_ADDITION_INFO`: 总线传输附加信息
- `BUS_TYPE`: 总线类型枚举(D5K/STTP)

**主要接口**：
- `InitAppNode()`: 初始化登录结点
- `StartBusSwap()`: 启动总线数据交换服务
- `SendSttpMsgData()`: 发送STTP消息
- `RegRecvSttpMsgCallBak()`: 注册接收回调

**执行流程**：
```
1. 创建总线实例 -> CreateBusIns()
2. 初始化节点信息 -> InitAppNode()
3. 注册消息回调 -> RegRecvSttpMsgCallBak()
4. 启动总线服务 -> StartBusSwap()
5. 消息收发处理 -> SendSttpMsgData() / 回调处理
6. 停止服务 -> StopBusSwap()
7. 销毁实例 -> DestroyBusIns()
```

#### ZxLoadBusSwapLib.h/.cpp - 总线库加载包装类
**功能**：实现对总线数据交互库的加载、方法调用及卸载

**核心方法**：
- `LoadShareLib()`: 根据配置加载对应的总线库(D5K/STTP)
- `CreateBusIns()`: 创建总线交换实例
- `DestroyBusIns()`: 销毁总线实例
- `ReleaseShareLib()`: 释放共享库

**工作流程**：
```
构造函数初始化 -> 
LoadShareLib() 读取bus_def.ini配置 ->
根据总线类型加载对应dll/so ->
获取函数指针(CreateBusIns/DestroyBusIns) ->
提供统一接口供上层调用 ->
析构时自动释放资源
```

### 2. 数据库访问模块

#### ZxLibMngr_DBAcess.h/.cpp - 数据库访问封装类
**功能**：封装数据库访问动态库，提供统一的数据库操作接口

**核心功能**：
- 静态库操作：`Select()`, `Update()`, `Insert()`, `Delete()`
- 实时库操作：`RDSelect()`, `RDInsert()`
- EMS库操作：`SelectData_ems()`, `UpdateData_ems()`
- 批量操作：`InsertMultiData()`
- 历史数据：`SelectTableDivisionHisData()`

**初始化流程**：
```
1. 构造函数 -> 初始化成员变量
2. LoadLib() -> 加载libZclD5kDbOptItf.so
3. 获取所有函数指针 -> Connect, Select, Update等
4. Connect() -> 连接数据库
5. 设置连接参数 -> SetConnNum(), SetLogPath()
```

**查询流程示例**：
```cpp
bool CXJDBFacade::Select(int pTableNo, SQL_DATA& pSqlData, char* pError, CMemSet* pMemSet)
{
    if(m_pFunSelect == NULL) return false;
    
    try {
        int nRet = m_pFunSelect(pTableNo, pSqlData, pError, pMemSet);
        return (nRet == 1);
    }
    catch(...) {
        strcpy(pError, "数据库查询异常");
        return false;
    }
}
```

### 3. STTP消息处理模块

#### ZxSttpMsgMaker.h/.cpp - STTP消息制作器
**功能**：负责生成各种类型的STTP协议消息

**主要消息类型**：
- `Make00215Msg()`: 数据确认消息
- `Make00901Msg()`: 通用响应消息  
- `Make20069Msg()`: 控制确认消息
- `Make20144Msg()`: 站状态变化消息
- `Make20146Msg()`: 站通信状态消息

**消息生成流程**：
```
1. 接收源消息或参数
2. 解析消息头信息(RII, MsgId等)
3. 构造目标消息结构
4. 填充消息体数据
5. 设置消息头和校验
6. 返回完整STTP消息
```

**典型实现示例**：
```cpp
void CSttpMsgMaker::Make00901Msg(STTPMSG& pDestinationMsg, const STTPMSG& pSourceMsg, 
                                 int pResult, const char* pHandleName, const char* pVarData)
{
    // 1. 复制消息头
    memcpy(&pDestinationMsg, &pSourceMsg, sizeof(STTPMSG_HEADER));
    
    // 2. 设置响应消息ID
    pDestinationMsg.head.msgid = 00901;
    
    // 3. 构造响应数据
    // 填充结果码、处理程序名称、附加数据等
    
    // 4. 更新消息长度和校验
}
```

### 4. 服务器在线管理接口

#### ZxLibApi_SrvOnlineMngr.h - 服务器在线管理API
**功能**：定义服务器切换和在线状态管理的接口

**关键结构体**：
- `stXJSrvOnlineManager`: 服务器在线管理参数
- `stXJSubstation`: 子站信息结构
- `stXJSubstationLoadStatus`: 子站加载状态

**核心接口**：
- `PFUNSTARTSRVONLINEMANAGER`: 启动在线管理器
- `PFUNREGISTERSRVSWITCHCALLBACK`: 注册服务器切换回调
- `PFUNSETSTATIONLOADSTATUS`: 设置子站加载状态

**状态管理**：
```cpp
// 服务器在线状态
#define SRV_ONLINE_UNKNOWN  -1  // 未知
#define SRV_ONLINE_STANDBY   0  // 备用
#define SRV_ONLINE_ACTIVE    1  // 在线

// 子站挂载状态  
#define SUB_LOADED_UNKNOWN  -1  // 未知
#define SUB_LOADED_OFF       0  // 未挂载
#define SUB_LOADED_ON        1  // 已挂载
```

## 接口使用流程

### 总线通信初始化流程
```cpp
// 1. 创建总线加载器
CZXLoadBusSwapLib* pLoadLib = new CZXLoadBusSwapLib(&logFile);

// 2. 加载总线库
if(pLoadLib->LoadShareLib()) {
    // 3. 创建总线实例
    IZxBusSwap* pBusSwap = pLoadLib->CreateBusIns(&msgFile);
    
    // 4. 配置节点信息
    APP_NODE_INFO node;
    node.nProcType = MODULE_TYPE_XXX;
    node.strProcName = "MyProcess";
    // ... 其他配置
    
    // 5. 初始化节点
    pBusSwap->InitAppNode(node);
    
    // 6. 注册回调
    pBusSwap->RegRecvSttpMsgCallBak(this, OnReceiveSttpMsg);
    
    // 7. 启动服务
    pBusSwap->StartBusSwap();
}
```

### 数据库访问流程
```cpp
// 1. 创建数据库访问实例
CXJDBFacade* pDB = new CXJDBFacade(&logFile);

// 2. 加载数据库接口库
pDB->LoadLib();

// 3. 连接数据库
pDB->Connect(MODULE_ID, errorMsg);

// 4. 执行数据库操作
SQL_DATA queryData;
CMemSet resultSet;
pDB->Select(TABLE_ID, queryData, errorMsg, &resultSet);

// 5. 处理查询结果
for(int i = 0; i < resultSet.GetCount(); i++) {
    // 处理每条记录
}
```

## 设计模式应用

### 1. 外观模式(Facade)
- libapimngr作为外观，隐藏底层动态库的复杂性
- 提供简化的统一接口供上层使用

### 2. 工厂模式(Factory) 
- 总线实例的创建通过工厂方法
- 根据配置动态决定创建哪种类型的总线

### 3. 策略模式(Strategy)
- 支持不同的总线类型(D5K/STTP)
- 运行时根据配置选择不同的实现策略

### 4. 观察者模式(Observer)
- 消息接收通过回调机制实现
- 支持多个观察者监听同一事件

## 异常处理机制

### 1. 动态库加载异常
```cpp
try {
    m_LibHandle = xj_load_library(dllName);
    if(m_LibHandle == NULL) {
        WriteLog("动态库加载失败", LOG_ERROR);
        return false;
    }
} catch(...) {
    WriteLog("动态库加载异常", LOG_ERROR);
    return false;
}
```

### 2. 数据库连接异常
- 连接失败时记录错误日志
- 支持连接池管理
- 自动重连机制

### 3. 消息处理异常
- Try-catch包装所有接口调用
- 详细的错误码和错误信息
- 异常情况下的优雅降级

## 性能优化策略

### 1. 连接池管理
- 数据库连接复用
- 连接数量可配置
- 连接状态监控

### 2. 内存管理
- RAII资源管理
- 智能指针使用
- 内存泄漏检测

### 3. 异步处理
- 消息接收异步回调
- 数据库操作支持批量处理
- 非阻塞IO操作

## 配置管理

### 1. bus_def.ini - 总线配置
```ini
[BUS_TYPE]
type=1  # 1:D5K总线, 2:STTP总线

[MSG_SEND_RECV_MODE]
mode=1  # 1:同步模式, 2:异步模式
```

### 2. 数据库连接配置
- 连接字符串配置
- 连接池参数设置
- 超时时间配置

这个API管理库是整个Dragon系统的基础，为上层业务模块提供了统一、稳定、高效的基础服务接口。
