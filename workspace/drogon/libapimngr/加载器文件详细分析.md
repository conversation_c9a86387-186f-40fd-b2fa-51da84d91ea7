# libapimngr - 加载器文件详细分析

## 概述
加载器文件负责动态加载各种共享库（DLL/SO），提供统一的加载、卸载和实例管理机制。

## 主要加载器列表

### 1. 总线交互加载器
- **ZxLoadBusSwapLib**: 总线数据交换库加载器
- **ZxLoadD5kModelSyncLib**: D5K模型同步库加载器
- **ZxLoadD5kPrimStateLib**: D5K一次设备状态库加载器

### 2. 其他加载器
- **ZxLibApi_LoadAcquirer**: 负载采集器加载

## 核心加载器详细分析

### 1. CZXLoadBusSwapLib - 总线交互库加载器

#### 类定义
```cpp
class CZXLoadBusSwapLib {
private:
    OS_MODULE_HANDLE m_LibHandle;                // 共享库句柄
    PFUNC_CREATEBUSINS m_pCreateBusSwapInsFunc;  // 创建实例函数
    PFUNC_DESTROYBUSINS m_pDestroyBusSwapInsFunc;// 销毁实例函数
    BUS_TYPE m_eBusType;                         // 总线类型
    CMessageLog* m_pLogRecord;                   // 日志对象
    string m_strDllName;                         // 共享库名称
    
public:
    CZXLoadBusSwapLib(CMessageLog* pLogRecord);
    virtual ~CZXLoadBusSwapLib();
    
    // 加载共享库
    bool LoadShareLib(BUS_TYPE eBusType = UNKNOWN_BUSSWAP_TYPE);
    
    // 获取总线类型
    BUS_TYPE GetBusShareLibType();
    
    // 释放共享库
    bool ReleaseShareLib();
    
    // 创建总线交换对象实例
    IZxBusSwap* CreateBusIns(CMessageLog* pMsgFile, char* pChLogPath = NULL);
    
    // 销毁总线交互对象实例
    bool DestroyBusIns(IZxBusSwap* pBusIns);
    
protected:
    void WriteLog(const char* pLog, int nLevel);
};
```

#### 共享库名称定义
```cpp
#ifdef OS_WINDOWS
    #define SHARE_LIB_D5K_BUS_SWAP   "ZclD5kBusSwapItf.dll"
    #define SHARE_LIB_STTP_BUS_SWAP  "ZclSttpBusSwapItf.dll"
#else
    #define SHARE_LIB_D5K_BUS_SWAP   "libZclD5kBusSwapItf.so"
    #define SHARE_LIB_STTP_BUS_SWAP  "libZclSttpBusSwapItf.so"
#endif
```

#### 使用示例
```cpp
// 第一步：创建加载器实例
CMessageLog logFile;
CZXLoadBusSwapLib* pLoadLib = new CZXLoadBusSwapLib(&logFile);

// 第二步：加载共享库并创建实例
if (pLoadLib->LoadShareLib()) {
    IZxBusSwap* pBusSwap = pLoadLib->CreateBusIns();
    
    // 第三步：初始化登录节点
    APP_NODE_INFO node;
    // ... 填充结构体
    if (pBusSwap->InitAppNode(node) < 0) {
        return;
    }
    
    // 注册回调
    pBusSwap->RegRecvSttpMsgCallBak(callback);
    
    // 第四步：启动
    pBusSwap->StartBusSwap();
    
    // ... 发送/接收数据
    
    // 第五步：停止并清理
    pBusSwap->StopBusSwap();
    pLoadLib->DestroyBusIns(pBusSwap);
}

delete pLoadLib;
```

### 2. CZXLoadD5kModelSyncLib - D5K模型同步库加载器

#### 类定义
```cpp
class CZXLoadD5kModelSyncLib {
private:
    OS_MODULE_HANDLE m_LibHandle;                          // 共享库句柄
    PFUNC_CREATEMODELSYNCINS m_pCreateD5kModelSyncInsFunc; // 创建函数
    PFUNC_DESTROYMODELSYNCINS m_pDestroyD5kModelSyncInsFunc;// 销毁函数
    CMessageLog* m_pLogRecord;                            // 日志对象
    string m_strDllName;                                  // 库名称
    
public:
    CZXLoadD5kModelSyncLib(CMessageLog* pLogRecord);
    virtual ~CZXLoadD5kModelSyncLib();
    
    // 加载共享库
    bool LoadShareLib();
    
    // 释放共享库
    bool ReleaseShareLib();
    
    // 创建模型同步实例
    IZxD5kModelSync* CreateD5kModelSyncIns(CMessageLog* pMsgFile, char* pChLogPath);
    
    // 销毁模型同步实例
    bool DestroyD5kModelSyncIns(IZxD5kModelSync* pIns);
    
protected:
    void WriteLog(const char* pLog, int nLevel);
};
```

#### 共享库名称定义
```cpp
#ifdef OS_WINDOWS
    #define SHARE_LIB_D5K_MODEL_SYNC "ZdsD5kModelSync.dll"
#else
    #define SHARE_LIB_D5K_MODEL_SYNC "libZdsD5kModelSync.so"
#endif
```

#### 使用流程
```cpp
// 第一步：创建加载器
CMessageLog logFile;
CZXLoadD5kModelSyncLib* pLoadLib = new CZXLoadD5kModelSyncLib(&logFile);

// 第二步：加载库并创建实例
if (pLoadLib->LoadShareLib()) {
    IZxD5kModelSync* pModelSync = pLoadLib->CreateD5kModelSyncIns(&logFile, logPath);
    
    // 第三步：初始化
    if (pModelSync->Init() >= 0) {
        // 同步间隔信息
        pModelSync->SyncBayInfo();
        
        // 同步开关信息
        pModelSync->SyncSwitchByBayId(bayId);
        
        // 控制线程状态同步
        pModelSync->CtrlThreadStateSync(state);
    }
    
    // 第四步：释放资源
    pModelSync->Free();
    pLoadLib->DestroyD5kModelSyncIns(pModelSync);
}

delete pLoadLib;
```

### 3. CZXLoadD5kPrimStateLib - D5K一次设备状态库加载器

#### 功能说明
```cpp
class CZXLoadD5kPrimStateLib {
public:
    // 加载状态获取库
    bool LoadShareLib();
    
    // 创建状态获取实例
    IZxD5kPrimState* CreateD5kPrimStateIns(CMessageLog* pMsgFile, char* pChLogPath);
    
    // 销毁状态获取实例
    bool DestroyD5kPrimStateIns(IZxD5kPrimState* pStateObj);
    
    // 使用示例
    void Example() {
        CMessageLog logFile;
        CZXLoadD5kPrimStateLib* pLoadLib = new CZXLoadD5kPrimStateLib(&logFile);
        
        if (pLoadLib->LoadShareLib()) {
            IZxD5kPrimState* pStateObj = pLoadLib->CreateD5kPrimStateIns();
            
            // 初始化
            if (pStateObj->Init() >= 0) {
                // 注册回调
                pStateObj->RegD5kPrimStateCallBak(callback);
                
                // 获取一次设备状态
                pStateObj->PrimStateGet();
                
                // ... 处理状态数据
            }
            
            // 释放
            pStateObj->Free();
            pLoadLib->DestroyD5kPrimStateIns(pStateObj);
        }
        
        delete pLoadLib;
    }
};
```

## 加载器通用模式

### 1. 基础加载器模板
```cpp
template<typename InterfaceType>
class TLibLoader {
protected:
    OS_MODULE_HANDLE m_hLib;           // 库句柄
    string m_strLibName;               // 库名称
    CMessageLog* m_pLog;               // 日志对象
    
    // 函数指针
    typedef InterfaceType* (*CreateFunc)();
    typedef void (*DestroyFunc)(InterfaceType*);
    
    CreateFunc m_pfnCreate;
    DestroyFunc m_pfnDestroy;
    
public:
    TLibLoader(const char* libName, CMessageLog* pLog)
        : m_strLibName(libName), m_pLog(pLog), m_hLib(NULL) {
        m_pfnCreate = NULL;
        m_pfnDestroy = NULL;
    }
    
    virtual ~TLibLoader() {
        ReleaseLib();
    }
    
    // 加载库
    bool LoadLib() {
        m_hLib = OS_LoadLibrary(m_strLibName.c_str());
        if (!m_hLib) {
            m_pLog->FormatAdd(CLogFile::error, 
                "加载库失败: %s", m_strLibName.c_str());
            return false;
        }
        
        // 获取函数地址
        m_pfnCreate = (CreateFunc)OS_GetProcAddress(m_hLib, "CreateInstance");
        m_pfnDestroy = (DestroyFunc)OS_GetProcAddress(m_hLib, "DestroyInstance");
        
        if (!m_pfnCreate || !m_pfnDestroy) {
            m_pLog->Add("获取函数地址失败");
            ReleaseLib();
            return false;
        }
        
        return true;
    }
    
    // 释放库
    void ReleaseLib() {
        if (m_hLib) {
            OS_FreeLibrary(m_hLib);
            m_hLib = NULL;
            m_pfnCreate = NULL;
            m_pfnDestroy = NULL;
        }
    }
    
    // 创建实例
    InterfaceType* CreateInstance() {
        if (m_pfnCreate) {
            return m_pfnCreate();
        }
        return NULL;
    }
    
    // 销毁实例
    void DestroyInstance(InterfaceType* pInstance) {
        if (m_pfnDestroy && pInstance) {
            m_pfnDestroy(pInstance);
        }
    }
};
```

### 2. 工厂模式加载器
```cpp
class LibLoaderFactory {
private:
    map<string, ILibLoader*> m_mapLoaders;
    CMessageLog* m_pLog;
    
public:
    LibLoaderFactory(CMessageLog* pLog) : m_pLog(pLog) {}
    
    ~LibLoaderFactory() {
        for (auto& pair : m_mapLoaders) {
            delete pair.second;
        }
    }
    
    // 获取加载器
    ILibLoader* GetLoader(const string& libType) {
        auto it = m_mapLoaders.find(libType);
        if (it != m_mapLoaders.end()) {
            return it->second;
        }
        
        // 创建新的加载器
        ILibLoader* pLoader = CreateLoader(libType);
        if (pLoader) {
            m_mapLoaders[libType] = pLoader;
        }
        
        return pLoader;
    }
    
private:
    ILibLoader* CreateLoader(const string& libType) {
        if (libType == "BusSwap") {
            return new CZXLoadBusSwapLib(m_pLog);
        } else if (libType == "ModelSync") {
            return new CZXLoadD5kModelSyncLib(m_pLog);
        } else if (libType == "PrimState") {
            return new CZXLoadD5kPrimStateLib(m_pLog);
        }
        
        return NULL;
    }
};
```

## 错误处理

### 1. 加载失败处理
```cpp
enum LoadError {
    LOAD_SUCCESS = 0,
    LOAD_LIB_NOT_FOUND = -1,
    LOAD_FUNC_NOT_FOUND = -2,
    LOAD_VERSION_MISMATCH = -3,
    LOAD_INIT_FAILED = -4
};

class LoadException : public std::exception {
private:
    LoadError m_error;
    string m_msg;
    
public:
    LoadException(LoadError error, const string& msg)
        : m_error(error), m_msg(msg) {}
    
    const char* what() const throw() {
        return m_msg.c_str();
    }
    
    LoadError GetError() const {
        return m_error;
    }
};
```

### 2. 版本检查
```cpp
class VersionChecker {
public:
    static bool CheckVersion(OS_MODULE_HANDLE hLib) {
        typedef const char* (*GetVersionFunc)();
        GetVersionFunc pfnGetVersion = 
            (GetVersionFunc)OS_GetProcAddress(hLib, "GetVersion");
        
        if (!pfnGetVersion) {
            return false;
        }
        
        const char* version = pfnGetVersion();
        return IsCompatible(version);
    }
    
private:
    static bool IsCompatible(const char* version) {
        // 版本兼容性检查逻辑
        // ...
        return true;
    }
};
```

## 资源管理

### 1. RAII模式
```cpp
class LibGuard {
private:
    ILibLoader* m_pLoader;
    void* m_pInstance;
    
public:
    LibGuard(ILibLoader* pLoader) 
        : m_pLoader(pLoader), m_pInstance(NULL) {
        if (m_pLoader) {
            m_pLoader->LoadLib();
            m_pInstance = m_pLoader->CreateInstance();
        }
    }
    
    ~LibGuard() {
        if (m_pLoader && m_pInstance) {
            m_pLoader->DestroyInstance(m_pInstance);
            m_pLoader->ReleaseLib();
        }
    }
    
    void* GetInstance() {
        return m_pInstance;
    }
    
    bool IsValid() const {
        return m_pInstance != NULL;
    }
};
```

### 2. 引用计数
```cpp
class RefCountedLoader {
private:
    OS_MODULE_HANDLE m_hLib;
    int m_refCount;
    static map<string, RefCountedLoader*> s_loaders;
    
public:
    static RefCountedLoader* GetLoader(const string& libName) {
        auto it = s_loaders.find(libName);
        if (it != s_loaders.end()) {
            it->second->AddRef();
            return it->second;
        }
        
        RefCountedLoader* pLoader = new RefCountedLoader(libName);
        s_loaders[libName] = pLoader;
        return pLoader;
    }
    
    void AddRef() {
        ++m_refCount;
    }
    
    void Release() {
        if (--m_refCount == 0) {
            // 从映射中移除并删除
            // ...
            delete this;
        }
    }
};
```

## 配置管理

### 加载器配置示例
```ini
[LibLoader]
# 库路径配置
LibPath=/usr/local/lib/dragon

# 总线类型配置
BusType=STTP  ; D5K or STTP

# 加载选项
LazyLoad=true
CheckVersion=true
AutoReload=false

# 库映射
[LibMapping]
BusSwap_D5K=libZclD5kBusSwapItf.so
BusSwap_STTP=libZclSttpBusSwapItf.so
ModelSync=libZdsD5kModelSync.so
PrimState=libZdsD5kPrimState.so
```

## 使用最佳实践

### 1. 延迟加载
```cpp
class LazyLoader {
private:
    ILibLoader* m_pLoader;
    void* m_pInstance;
    bool m_bLoaded;
    
public:
    LazyLoader() : m_pLoader(NULL), m_pInstance(NULL), m_bLoaded(false) {}
    
    void* GetInstance() {
        if (!m_bLoaded) {
            Load();
        }
        return m_pInstance;
    }
    
private:
    void Load() {
        // 延迟加载逻辑
        m_bLoaded = true;
    }
};
```

### 2. 热更新支持
```cpp
class HotReloadLoader {
public:
    bool Reload() {
        // 保存当前状态
        SaveState();
        
        // 卸载旧库
        UnloadOldLib();
        
        // 加载新库
        if (!LoadNewLib()) {
            // 回滚
            RollbackToOldLib();
            return false;
        }
        
        // 恢复状态
        RestoreState();
        
        return true;
    }
};
```

## 注意事项

1. **路径管理**: 确保库文件路径正确
2. **版本兼容**: 检查接口版本兼容性
3. **异常处理**: 正确处理加载失败情况
4. **资源释放**: 确保正确释放库和实例
5. **线程安全**: 多线程环境下的加载器管理
6. **性能考虑**: 避免重复加载和卸载
