# libapimngr - 管理器实现文件详细分析

## 概述
libapimngr目录包含了系统各种管理器的实现，提供数据库访问、消息分析、数据处理等核心功能的封装。

## 主要管理器分类

### 1. 数据库访问管理器
- **ZxLibMngr_DBAcess**: 数据库访问管理
- **ZxLibMngr_DBAccessEx**: 扩展数据库访问
- **ZdsLibMngr_DeleteInfo**: 数据删除管理
- **ZasLibMngr_DBModelExp**: 数据库模型导出

### 2. STTP消息管理器
- **ZxLibMngr_SttpMsgAnalyze**: STTP消息分析
- **ZdsLibMngr_SttpHdl**: STTP处理
- **ZxSttpMsgMaker**: STTP消息生成

### 3. 数据分析管理器
- **ZxLibMngr_FaultpktAnalyze**: 故障包分析
- **ZxLibMngr_OscAnalyze**: 录波分析
- **ZxLibMngr_RelayAnalyze**: 保护分析

### 4. 其他管理器
- **ZxLibMngr_Heartbeat**: 心跳管理
- **ZxLibMngr_JvmAdapterMngr**: JVM适配器管理
- **ZxLibMngr_GetDataType**: 数据类型获取
- **ZxLibMngr_XmlToHdr**: XML转换管理

## 核心管理器详细分析

### 1. ZxLibMngr_DBAcess - 数据库访问管理器

#### 类定义
```cpp
class CXJDBFacade {
private:
    // 动态库句柄
    XJHANDLE m_hLibDBFacade;
    
    // 函数指针
    PFUNCONNECT m_pFunConnect;                 // 连接函数
    PFUNSETLOGCONFIG m_pFunSetLogConfig;       // 日志配置
    PFUNSELECT m_pFunSelect;                   // 查询
    PFUNMULTISELECT m_pFunMultiSelect;         // 多表查询
    PFUNUPDATE m_pFunUpdate;                   // 更新
    PFUNINSERT m_pFunInsert;                   // 插入
    PFUNDELETE m_pFunDelete;                   // 删除
    PFUNEXECUTESQL m_pFunExecuteSql;           // 执行SQL
    PFUNRDINSERT m_pFunRDInsert;               // 实时数据插入
    PFUNRDSELECT m_pFunRDSelect;               // 实时数据查询
    PFUNINSERTMULTIDATA m_pFunInsertMultiData; // 批量插入
    PFUNSETCONNNUM m_pFunSetConnNum;           // 设置连接数
    PFUNSETDOMAIN m_pFunSetDomain;             // 设置域
    
    // EMS相关函数
    PFUNSELECTDATA_EMS m_pFunSelectData_EMS;
    PFUNEXECUTESQL_EMS m_pFunExecuteSql_EMS;
    PFUNBLOBSELECTDATA_EMS m_pFunBlobSelectData_EMS;
    
    // 实时库操作
    PFUNSetRtdbConnFlag m_pFunSetRtdbConnFlag;
    PFUNSELECTDATA_RTTB m_pFunSelectData_rttb;
    PFUNUPDATEDATA_RTTB m_pFunUpdateData_rttb;
    PFUNINSERTDATA_RTTB m_pFunInsertData_rttb;
    PFUNDELETEDATA_RTTB m_pFunDeleteData_rttb;
    
public:
    CXJDBFacade();
    ~CXJDBFacade();
    
    // 初始化
    bool InitLibrary();
    
    // 连接管理
    int Connect(int nWorkPart, char* pError);
    int SetLogConfig(char* szLogPath, char* szLogLevel);
    bool SetConnNum(int nConnNum);
    bool SetDomain(int nDomain);
    
    // 基本操作
    int Select(int nTableID, SQL_DATA& query_sql, char* pErrorMsg, CMemSet* p_rst);
    int MultiSelect(char* szTableName, MutiSQL_DATA& query_sql, char* pErrorMsg);
    int Update(int nTableID, SQL_DATA& update_sql, char* pErrorMsg);
    int Insert(int nTableID, SQL_DATA& insert_sql, char* pErrorMsg);
    int Delete(int nTableID, SQL_DATA& delete_sql, char* pErrorMsg);
    int ExecuteSql(MutiSQL_DATA& execute_sql, char* pErrorMsg, CMemSet* p_rst);
    
    // 实时数据操作
    int RealDataInsert(STTP_FULL_DATA& sttp_data, char* pErrorMsg, int nSaveType);
    int RealDataSelect(int nTableID, SQL_DATA& query_sql, 
                      REALDATA_CONDITION& condition, char* pErrorMsg, CMemSet* p_rst);
    
    // 批量操作
    int InsertMultiData(int nTableID, STTP_FULL_DATA& sttp_data, char* pErrorMsg);
    
    // BLOB操作
    int BlobSelectData(int nTableID, BLOB_SQL_DATA& blob_sql, 
                      char* pErrorMsg, CMemSet* p_rst);
    int BlobInsertData(int nTableID, BLOB_SQL_DATA& blob_sql, char* pErrorMsg);
    int BlobUpdateData(int nTableID, BLOB_SQL_DATA& blob_sql, char* pErrorMsg);
    int BlobDeleteData(int nTableID, BLOB_SQL_DATA& blob_sql, char* pErrorMsg);
};
```

#### 动态库定义
```cpp
#ifdef OS_LINUX
#define LIB_DBFACADE_NAME "libZclD5kDbOptItf.so"
#endif

#ifdef OS_WINDOWS
#define LIB_DBFACADE_NAME "ZclD5kDbOptItf.dll"
#endif
```

#### 主要功能
- 数据库连接池管理
- SQL语句执行
- 事务处理
- 实时数据存储
- BLOB数据处理
- 多数据库支持（Oracle、MySQL、达梦、人大金仓）

### 2. ZxLibMngr_SttpMsgAnalyze - STTP消息分析器

#### 类定义
```cpp
class CSttpMsgAnalyze {
private:
    STTP_FULL_DATA* m_pSttpData;              // STTP数据指针
    XJHANDLE m_hLibXJSTTPAnalyzer;            // 动态库句柄
    P_FUNCDATATOSTTP m_pFunDataToSttp;        // 数据转STTP函数
    P_FUNCSTTPTODATA m_pFunSttpToData;        // STTP转数据函数
    
public:
    CSttpMsgAnalyze();
    CSttpMsgAnalyze(STTP_FULL_DATA* pData);
    virtual ~CSttpMsgAnalyze();
    
    // 附加数据
    void Attach(STTP_FULL_DATA* pData);
    
    // 转换功能
    int Data_To_Sttp(STTPMSG_QUEUE& pMsgQueue);
    int Sttp_To_Data(STTPMSG* pSttpMsg);
    int MultiSttp_To_Data(STTPMSG_QUEUE& pMsgQueue);
    
private:
    void InitLibrary();
};
```

#### 动态库定义
```cpp
#ifdef OS_LINUX
#define LIB_STTP_ANALYZER_NAME "libZclSttpExp.so"
#endif

#ifdef OS_WINDOWS
#define LIB_STTP_ANALYZER_NAME "ZclSttpExp.dll"
#endif
```

#### 主要功能
- STTP报文解析
- 数据结构转换
- 多报文批处理
- 报文验证

### 3. ZxLibMngr_GetDataType - 数据类型管理器

#### 功能说明
```cpp
class CGetDataType {
private:
    // 数据类型映射表
    map<int, DataTypeInfo> m_mapDataType;
    
    // 动态库句柄
    XJHANDLE m_hLib;
    
public:
    // 获取数据类型
    int GetDataType(int nPointID);
    
    // 获取数据类型名称
    string GetDataTypeName(int nType);
    
    // 数据类型转换
    int ConvertDataType(void* pSrc, int nSrcType, 
                       void* pDst, int nDstType);
    
    // 数据类型验证
    bool ValidateDataType(void* pData, int nType);
};
```

### 4. ZxLibMngr_Heartbeat - 心跳管理器

#### 功能说明
```cpp
class CHeartbeatManager {
private:
    // 心跳配置
    int m_nHeartbeatInterval;      // 心跳间隔
    int m_nTimeout;                 // 超时时间
    
    // 心跳状态
    map<string, HeartbeatInfo> m_mapHeartbeat;
    
public:
    // 启动心跳
    int StartHeartbeat(const HeartbeatConfig& config);
    
    // 停止心跳
    int StopHeartbeat();
    
    // 发送心跳
    int SendHeartbeat(const string& targetID);
    
    // 接收心跳
    int RecvHeartbeat(const string& sourceID, const HeartbeatData& data);
    
    // 检查心跳状态
    int CheckHeartbeatStatus();
    
    // 心跳超时处理
    void OnHeartbeatTimeout(const string& targetID);
};
```

### 5. ZxLibMngr_StatHisData - 历史数据统计管理器

#### 功能说明
```cpp
class CStatHisDataManager {
public:
    // 统计类型
    enum StatType {
        STAT_AVG,    // 平均值
        STAT_MAX,    // 最大值
        STAT_MIN,    // 最小值
        STAT_SUM,    // 总和
        STAT_COUNT   // 计数
    };
    
    // 统计历史数据
    int StatHistoryData(const StatConfig& config, StatResult& result);
    
    // 生成统计报表
    int GenerateStatReport(const ReportConfig& config, ReportData& report);
    
    // 数据聚合
    int AggregateData(const AggregateConfig& config, AggregateResult& result);
};
```

## 管理器通用接口模式

### 1. 初始化模式
```cpp
class IManager {
public:
    virtual bool Init(const Config& config) = 0;
    virtual bool LoadLibrary() = 0;
    virtual bool Start() = 0;
    virtual bool Stop() = 0;
    virtual void Release() = 0;
};
```

### 2. 动态库加载模式
```cpp
template<class T>
class DynamicLibManager {
protected:
    XJHANDLE m_hLib;
    
    bool LoadLib(const char* libName) {
        m_hLib = OS_LoadLibrary(libName);
        return m_hLib != NULL;
    }
    
    template<typename FuncType>
    FuncType GetFunction(const char* funcName) {
        return (FuncType)OS_GetProcAddress(m_hLib, funcName);
    }
    
    void UnloadLib() {
        if (m_hLib) {
            OS_FreeLibrary(m_hLib);
            m_hLib = NULL;
        }
    }
};
```

### 3. 单例模式
```cpp
template<class T>
class Singleton {
private:
    static T* m_pInstance;
    static CLock m_lock;
    
protected:
    Singleton() {}
    
public:
    static T* GetInstance() {
        if (m_pInstance == NULL) {
            CCsLocker locker(&m_lock);
            if (m_pInstance == NULL) {
                m_pInstance = new T();
            }
        }
        return m_pInstance;
    }
    
    static void Destroy() {
        CCsLocker locker(&m_lock);
        if (m_pInstance) {
            delete m_pInstance;
            m_pInstance = NULL;
        }
    }
};
```

## 错误处理机制

### 1. 错误码定义
```cpp
enum ErrorCode {
    ERR_SUCCESS = 0,           // 成功
    ERR_INVALID_PARAM = -1,    // 无效参数
    ERR_LIB_NOT_FOUND = -2,    // 库未找到
    ERR_FUNC_NOT_FOUND = -3,   // 函数未找到
    ERR_INIT_FAILED = -4,      // 初始化失败
    ERR_CONNECT_FAILED = -5,   // 连接失败
    ERR_TIMEOUT = -6,          // 超时
    ERR_UNKNOWN = -99          // 未知错误
};
```

### 2. 异常处理
```cpp
class ManagerException : public std::exception {
private:
    int m_errorCode;
    string m_errorMsg;
    
public:
    ManagerException(int code, const string& msg) 
        : m_errorCode(code), m_errorMsg(msg) {}
    
    const char* what() const throw() {
        return m_errorMsg.c_str();
    }
    
    int GetErrorCode() const {
        return m_errorCode;
    }
};
```

## 性能优化策略

### 1. 缓存机制
- 数据缓存
- 连接池
- 对象池

### 2. 异步处理
- 异步IO
- 消息队列
- 线程池

### 3. 批量操作
- 批量查询
- 批量插入
- 批量更新

## 配置管理

### 管理器配置示例
```ini
[DatabaseManager]
MaxConnections=100
ConnectionTimeout=30
QueryTimeout=60
EnableCache=true
CacheSize=1024

[SttpManager]
MaxMessageSize=8192
MessageTimeout=30
EnableCompression=true
CompressionLevel=6

[HeartbeatManager]
Interval=10
Timeout=30
MaxRetry=3
```

## 使用注意事项

1. **资源管理**: 确保正确释放动态库和内存资源
2. **线程安全**: 多线程环境下注意同步
3. **异常处理**: 正确处理各种异常情况
4. **性能监控**: 监控管理器性能指标
5. **版本兼容**: 注意动态库版本兼容性
