# Dragon项目整体架构分析

## 项目概述

Dragon是一个大型的电力系统通信服务器项目，主要用于电力调度中心的数据采集与监控系统。该项目采用模块化架构设计，包含多个子系统和协议实现。

## 项目结构分析

```
dragon/
├── libapimngr/                    # API管理库 - 统一接口管理
├── module/                        # 核心模块目录
│   ├── ZclMltSrvHbtMngr/         # 心跳管理模块 - 多服务器心跳维护
│   ├── ZclMltSrvOnlineMngr/      # 在线管理模块 - 服务器切换管理
│   └── ZcsServer/                # 通信服务器主模块
│       ├── common/               # 通用组件库
│       ├── server/               # 服务器核心组件
│       └── pro/                  # 协议实现层
│           ├── Zcs104VlanSrv/    # IEC 104 VLAN协议服务
│           ├── ZcsFoshan104SrvPro/ # 佛山104协议实现
│           ├── ZcsPro61850Cli/   # IEC 61850客户端协议
│           ├── ZcsProMainFlow/   # 主通信流程处理
│           ├── ZcsSttpSrvPro/    # STTP协议服务端
│           └── ZcsWebSrvPro/     # Web服务协议
├── dragon.md                      # 项目说明文档
└── todo.md                        # 开发任务列表
```

## 系统架构设计理念

### 1. 分层架构
- **接口层**: libapimngr提供统一的API接口
- **业务层**: module包含核心业务逻辑
- **协议层**: pro目录实现各种通信协议
- **服务层**: server提供基础服务支持

### 2. 模块化设计
- **心跳管理**: ZclMltSrvHbtMngr负责多服务器间的心跳维护
- **在线切换**: ZclMltSrvOnlineMngr管理服务器在线状态和自动切换
- **通信服务**: ZcsServer处理各种通信协议和数据传输

### 3. 高可用性设计
- 支持多服务器热备切换
- 心跳检测机制确保服务可用性
- 模块间解耦，单点故障不影响整体系统

## 核心功能模块

### libapimngr - API管理库
负责：
- 统一接口定义和管理
- 动态库加载和卸载
- 数据库访问封装
- 消息总线接口
- 日志和配置管理

### ZclMltSrvHbtMngr - 心跳管理模块
功能：
- UDP心跳包发送和接收
- 服务器状态监控
- 网络连接状态检测
- 心跳超时处理

### ZclMltSrvOnlineMngr - 在线管理模块
职责：
- 服务器在线状态管理
- 自动故障切换
- 厂站管辖权转移
- 服务器负载均衡

### ZcsServer - 通信服务器核心
包含：
- **common**: 通用工具和基础组件
- **server**: 服务器端核心逻辑
- **pro**: 各种协议的具体实现

## 协议支持

系统支持多种电力系统通信协议：
- **IEC 104**: 电力系统控制站间信息传输标准
- **IEC 61850**: 变电站自动化系统通信协议
- **STTP**: 简单传输协议
- **103**: 继电保护设备信息接口标准
- **私有协议**: 各厂商定制协议

## 部署架构

支持多种部署模式：
1. **单机部署**: 所有模块运行在一台服务器上
2. **多机热备**: 主备服务器自动切换
3. **分布式部署**: 不同模块部署在不同服务器上
4. **集群部署**: 多个服务器组成服务集群

## 技术特点

1. **跨平台**: 支持Linux和Windows
2. **高性能**: 多线程并发处理
3. **高可靠**: 多重冗余和故障恢复
4. **可扩展**: 模块化设计便于功能扩展
5. **标准化**: 遵循电力行业标准协议

## 应用场景

主要应用于：
- 电力调度中心
- 变电站自动化系统
- 配电网监控系统
- 电力市场交易系统
- 新能源接入系统
