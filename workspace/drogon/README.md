# Dragon项目文档结构分析

本文档目录包含了对Dragon电力系统通信服务器项目的详细分析。该项目是一个大型的电力调度通信系统，支持多种电力行业标准协议。

## 文档结构

```
drogon/
├── README.md                           # 本文档
├── 项目整体架构分析.md                    # 项目总体架构和设计理念
├── libapimngr/                         # API管理库分析
│   └── API管理库详细分析.md              # 统一接口管理和基础服务
├── module/                             # 核心模块分析
│   ├── ZclMltSrvHbtMngr/              # 心跳管理模块
│   │   └── 心跳管理模块详细分析.md       # 多服务器心跳维护机制
│   ├── ZclMltSrvOnlineMngr/           # 在线管理模块  
│   │   └── 在线管理模块详细分析.md       # 服务器切换和在线状态管理
│   └── ZcsServer/                      # 通信服务器核心
│       ├── 通信服务器核心分析.md         # 服务器架构和组件分析
│       └── pro/                        # 协议实现层
│           └── Zcs104VlanSrv/         # IEC 104 VLAN协议实现
│               └── IEC104_VLAN协议服务详细分析.md
```

## 项目概述

Dragon是一个专为电力系统设计的通信服务器项目，具有以下特点：

### 核心特性
- **多协议支持**: IEC 104、IEC 103、IEC 61850、STTP等
- **高可用性**: 多服务器热备切换机制
- **模块化设计**: 组件间解耦，支持独立部署
- **实时通信**: 毫秒级数据传输和处理
- **安全机制**: 支持SM2国产密码算法

### 应用场景
- 电力调度中心数据采集与监控
- 变电站自动化系统通信
- 配电网监控和控制
- 新能源接入系统
- 电力市场交易系统

## 阅读指南

### 1. 初学者路径
推荐按以下顺序阅读：
1. 项目整体架构分析.md - 了解系统总体设计
2. libapimngr/API管理库详细分析.md - 理解基础组件
3. ZcsServer/通信服务器核心分析.md - 掌握核心架构
4. 各协议实现分析 - 深入具体协议

### 2. 开发者路径  
如果你需要开发或维护特定模块：
1. 查看项目整体架构，了解模块间关系
2. 阅读相关模块的详细分析文档
3. 研究具体的实现代码和接口定义
4. 参考配置文件和部署说明

### 3. 运维人员路径
运维和部署相关信息：
1. 项目整体架构 - 了解部署架构
2. 心跳管理和在线管理模块 - 理解高可用机制
3. 各服务组件的配置和监控要点

## 技术栈

### 开发语言和框架
- **C/C++**: 核心服务实现
- **动态库技术**: 模块化加载
- **多线程编程**: 并发处理
- **网络编程**: TCP/UDP通信

### 支持协议
- **IEC 60870-5-104**: 控制站间信息传输
- **IEC 60870-5-103**: 继电保护设备接口
- **IEC 61850**: 变电站自动化通信
- **STTP**: 简单传输协议
- **MMS**: 制造报文规范

### 数据库和存储
- **关系数据库**: 配置和历史数据存储
- **实时数据库**: 实时数据处理
- **内存数据库**: 高速缓存

## 系统架构亮点

### 1. 分层设计
```
┌─────────────────┐
│   应用接口层     │ ← libapimngr提供统一API
├─────────────────┤
│   业务逻辑层     │ ← module包含核心业务
├─────────────────┤  
│   协议处理层     │ ← pro实现各种通信协议
├─────────────────┤
│   通信传输层     │ ← TCP/UDP网络通信
└─────────────────┘
```

### 2. 高可用架构
```
主服务器 ←→ 心跳通信 ←→ 备服务器
    ↓                    ↓
  厂站A,B              厂站C,D
    ↓                    ↓
 故障时自动切换 → 备服务器接管所有厂站
```

### 3. 消息总线设计
```
各协议前置 → 消息总线 → 数据处理中心
             ↓
           订阅中心 → 各业务模块
```

## 开发环境搭建

### 编译环境
- **Linux**: GCC 4.8+
- **Windows**: Visual Studio 2015+
- **数据库**: Oracle/MySQL/达梦
- **第三方库**: 各协议处理库

### 调试工具
- **日志系统**: 分级别的详细日志
- **网络抓包**: Wireshark协议分析
- **性能监控**: 系统资源和响应时间监控
- **数据库工具**: 数据查询和分析工具

## 维护指南

### 日常维护
1. **日志检查**: 定期查看错误和警告日志
2. **性能监控**: 监控CPU、内存、网络使用率
3. **数据库维护**: 清理历史数据，优化查询
4. **配置备份**: 定期备份配置文件

### 故障排查
1. **连接问题**: 检查网络连通性和端口配置
2. **协议错误**: 分析协议报文和状态转换
3. **性能问题**: 分析系统瓶颈和资源使用
4. **数据异常**: 检查数据完整性和一致性

### 升级部署
1. **版本兼容性**: 确认新版本兼容性
2. **配置迁移**: 更新配置文件格式
3. **数据迁移**: 升级数据库结构
4. **回滚方案**: 准备快速回滚策略

## 贡献指南

### 代码规范
- 遵循C++编程规范
- 添加详细的注释说明
- 编写单元测试用例
- 更新相关文档

### 文档维护
- 及时更新设计文档
- 补充代码示例
- 完善故障排查指南
- 更新配置说明

## 联系信息

如有问题或建议，请联系项目维护团队。

---

**注意**: 本文档基于Dragon项目的代码分析生成，请结合实际代码进行理解和应用。
