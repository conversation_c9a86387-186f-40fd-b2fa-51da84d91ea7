# Dragon项目总体架构与调用流程分析

## 项目概述

Dragon是一个大型的电力系统通信服务器项目，采用模块化设计，支持多种通信协议（IEC 60870系列、IEC 61850、STTP等），实现了高可用的分布式架构。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层（Applications）                  │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
│  │ SCADA系统 │  │ EMS系统  │  │ 调度中心 │  │ Web客户端│   │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘   │
└─────────────────────────────────────────────────────────────┘
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                    通信服务层（ZcsServer）                     │
│  ┌──────────────────────────────────────────────────────┐   │
│  │                    ZcsMain（主服务进程）                │   │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐         │   │
│  │  │客户端管理器│  │服务器管理器│  │订阅中心  │         │   │
│  │  │ZcsCliMngr │  │ZcsSrvMngr │  │ZcsSsbMngr│         │   │
│  │  └──────────┘  └──────────┘  └──────────┘         │   │
│  │  ┌──────────┐  ┌──────────────────────────────┐   │   │
│  │  │数据处理中心│  │     前置服务（ZcsFrontSrv）    │   │   │
│  │  │ZcsDataHdl │  │  ┌────────┐  ┌────────┐     │   │   │
│  │  └──────────┘  │  │103协议  │  │61850协议│     │   │   │
│  └──────────────────│  └────────┘  └────────┘     │───┘   │
│                      └──────────────────────────────┘       │
└─────────────────────────────────────────────────────────────┘
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                    协议层（Protocol Layer）                    │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
│  │IEC 60870-│  │IEC 61850 │  │  STTP    │  │  其他    │   │
│  │5-103/104 │  │          │  │          │  │  协议    │   │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘   │
└─────────────────────────────────────────────────────────────┘
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                    基础服务层（Base Services）                 │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
│  │心跳管理器 │  │在线管理器 │  │负载均衡器│  │消息总线  │   │
│  │HbtMngr   │  │OnlineMngr│  │LoadAcquir│  │BusSwap   │   │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘   │
└─────────────────────────────────────────────────────────────┘
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层（Data Layer）                        │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
│  │  Oracle  │  │  MySQL   │  │   达梦   │  │ 人大金仓 │   │
│  └──────────┘  └──────────┘  └──────────┘  └──────────┘   │
│  ┌──────────────────────────────────────────────────────┐   │
│  │              实时数据库（RTDB）                        │   │
│  └──────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块关系图

```mermaid
graph TB
    subgraph "主服务进程"
        A[ZcsMain] --> B[ServerEngine]
        B --> C[配置加载]
        B --> D[服务启动]
    end
    
    subgraph "客户端管理"
        E[ZcsCliMngr] --> F[ClientManager]
        F --> G[ClientSession池]
        G --> H[协议处理]
    end
    
    subgraph "服务器管理"
        I[ZcsSrvMngr] --> J[ServerManager]
        J --> K[会话管理]
        J --> L[命令处理]
    end
    
    subgraph "订阅中心"
        M[ZcsSsbMngr] --> N[SubscribeCenter]
        N --> O[发布者管理]
        N --> P[观察者管理]
    end
    
    subgraph "数据处理"
        Q[ZcsDataHdl] --> R[DataHandler]
        R --> S[消息队列]
        R --> T[数据存储]
    end
    
    A --> E
    A --> I
    A --> M
    A --> Q
    
    E -.-> M
    I -.-> M
    Q -.-> M
```

## 主要调用流程

### 1. 系统启动流程

```
1. main() 入口
   ├── 解析命令行参数
   ├── 初始化日志系统
   └── 创建ServerEngine

2. ServerEngine初始化
   ├── LoadConfig() - 加载配置文件
   ├── InitDBFacade() - 初始化数据库连接
   ├── LoadModules() - 加载各功能模块
   │   ├── 加载DataHandler（数据处理中心）
   │   ├── 加载SubscribeCenter（订阅中心）
   │   ├── 加载ClientManager（客户端管理器）
   │   ├── 加载ServerManager（服务器管理器）
   │   └── 加载SrvOnlineManager（在线管理器）
   └── StartServices() - 启动所有服务

3. 服务启动
   ├── DataHandler->Start()
   │   ├── 创建消息队列
   │   ├── 启动处理线程
   │   └── 连接数据库
   ├── SubscribeCenter->Start()
   │   ├── 初始化发布者视图
   │   ├── 初始化观察者视图
   │   └── 启动定时器线程
   ├── ClientManager->Start()
   │   ├── 创建会话池
   │   ├── 加载协议库
   │   └── 启动监听
   └── ServerManager->Start()
       ├── 启动103监听
       ├── 启动STTP监听
       └── 启动命令处理线程
```

### 2. 客户端连接流程

```
1. 客户端发起连接
   └── ServerManager::Accept()
       ├── 创建新会话
       ├── 验证客户端
       └── 分配会话线程

2. 会话处理
   └── SessionThread::Run()
       ├── 接收数据
       ├── 协议解析
       ├── 业务处理
       └── 返回响应

3. 数据流转
   └── Protocol->DataHandler
       ├── 数据入队
       ├── 数据处理
       ├── 数据存储
       └── 事件通知
```

### 3. 发布订阅流程

```
1. 发布者注册
   └── SubscribeCenter::RegisterPublisher()
       ├── 添加到发布者视图
       ├── 分配发布者ID
       └── 建立设备映射

2. 观察者订阅
   └── SubscribeCenter::AttachObserver()
       ├── 添加到观察者视图
       ├── 建立订阅关系
       └── 更新路由表

3. 消息发布
   └── Publisher::PostMessage()
       ├── 查找订阅者
       ├── 消息路由
       └── 消息分发
           └── Observer::Update()
```

### 4. 数据处理流程

```
1. 数据接收
   └── DataHandler::InsertData()
       ├── 数据验证
       ├── 格式转换
       └── 入队处理

2. 队列处理
   └── DataHandler::MainSaveLoop()
       ├── 从队列取数据
       ├── 数据分类
       │   ├── 实时数据
       │   ├── 历史数据
       │   └── 告警数据
       └── 分发处理

3. 数据存储
   └── DataHandler::SaveToDB()
       ├── 构建SQL
       ├── 执行存储
       └── 结果处理
```

### 5. 协议转换流程

```
1. 接收原始报文
   └── Protocol::RecvData()
       ├── 报文解析
       ├── 提取数据
       └── 格式验证

2. 协议转换
   └── ProtocolAdapter::Convert()
       ├── 映射转换
       ├── 数据转换
       └── 时标转换

3. 统一数据模型
   └── UnifiedDataModel
       ├── 标准化格式
       ├── 添加元数据
       └── 质量标记

4. 目标协议生成
   └── Protocol::CreateMessage()
       ├── 格式化数据
       ├── 添加协议头
       └── 计算校验
```

### 6. 高可用切换流程

```
1. 心跳监测
   └── HeartbeatManager::CheckStatus()
       ├── 发送心跳
       ├── 接收心跳
       └── 超时检测

2. 故障检测
   └── OnlineManager::DetectFailure()
       ├── 心跳超时
       ├── 网络异常
       └── 服务异常

3. 主备切换
   └── OnlineManager::SwitchServer()
       ├── 确认故障
       ├── 选举新主
       ├── 状态同步
       └── 服务接管

4. 客户端重连
   └── ClientManager::HandleReconnect()
       ├── 通知客户端
       ├── 重建连接
       └── 恢复会话
```

## 关键设计模式

### 1. 工厂模式
- 协议工厂：创建不同协议处理器
- 消息工厂：创建不同类型消息
- 会话工厂：创建客户端会话

### 2. 观察者模式
- 发布订阅中心：实现消息分发
- 事件通知：状态变化通知

### 3. 单例模式
- 配置管理器：全局配置访问
- 日志管理器：统一日志输出
- 数据库管理器：连接池管理

### 4. 策略模式
- 协议处理：不同协议处理策略
- 数据存储：不同存储策略
- 负载均衡：不同均衡策略

### 5. 代理模式
- 远程代理：STTP客户端代理
- 保护代理：权限控制
- 缓存代理：数据缓存

## 性能优化要点

### 1. 并发处理
- 多线程/多进程架构
- 线程池技术
- 异步IO操作

### 2. 内存管理
- 内存池技术
- 对象池复用
- 智能指针管理

### 3. 网络优化
- 连接池管理
- 批量数据传输
- 数据压缩

### 4. 数据库优化
- 连接池
- 批量操作
- 缓存机制

## 部署架构

### 1. 单机部署
```
服务器
├── ZcsMain（主进程）
├── ZcsFrontSrv（前置服务）
├── 数据库
└── 配置文件
```

### 2. 分布式部署
```
主服务器
├── ZcsMain
├── ZcsSrvMngr
└── ZcsSsbMngr

前置服务器1
├── ZcsFrontSrv(103)
└── ZcsCliMngr

前置服务器2
├── ZcsFrontSrv(61850)
└── ZcsCliMngr

数据服务器
├── 数据库集群
└── ZcsDataHdl
```

### 3. 高可用部署
```
主服务器（Active）
├── 全部服务
└── 心跳发送

备服务器（Standby）
├── 全部服务（待机）
└── 心跳监听

负载均衡器
├── 健康检查
└── 流量分发
```

## 配置管理

### 1. 配置文件结构
```
config/
├── server.ini      # 服务器配置
├── database.ini    # 数据库配置
├── protocol.ini    # 协议配置
├── log.ini        # 日志配置
└── ha.ini         # 高可用配置
```

### 2. 动态配置
- 支持热更新
- 配置版本管理
- 配置同步机制

## 监控与维护

### 1. 日志系统
- 分级日志（DEBUG/INFO/WARN/ERROR）
- 日志轮转
- 远程日志

### 2. 性能监控
- CPU/内存监控
- 网络流量监控
- 数据库性能监控

### 3. 告警机制
- 实时告警
- 告警级别
- 告警通知

## 安全机制

### 1. 认证授权
- 用户认证
- 权限控制
- 会话管理

### 2. 数据安全
- 传输加密
- 存储加密
- 数据备份

### 3. 网络安全
- 防火墙配置
- 访问控制
- 安全审计

## 扩展性设计

### 1. 协议扩展
- 插件式协议加载
- 协议适配器模式
- 动态协议配置

### 2. 功能扩展
- 模块化设计
- 动态库加载
- 接口标准化

### 3. 性能扩展
- 水平扩展
- 垂直扩展
- 弹性伸缩

## 总结

Dragon项目是一个设计精良的电力系统通信服务器，具有以下特点：

1. **模块化设计**：各功能模块独立，便于维护和扩展
2. **高可用性**：支持主备切换，确保服务连续性
3. **高性能**：采用多线程、异步IO等技术优化性能
4. **可扩展性**：支持协议扩展、功能扩展和性能扩展
5. **安全可靠**：完善的安全机制和错误处理
6. **易于维护**：完善的日志、监控和配置管理
