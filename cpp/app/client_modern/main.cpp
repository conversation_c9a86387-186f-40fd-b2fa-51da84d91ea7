/**
 * @file main.cpp
 * @brief IEC 60870-5-103 协议客户端主程序（现代化重构版本）
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <spdlog/spdlog.h>
#include <unistd.h>

#include "zexuan/protocol/protocol_client/protocol_client_impl.hpp"
#include "zexuan/protocol/protocol_client/client_types.hpp"

using namespace zexuan::protocol::client;

// 全局客户端实例
std::unique_ptr<ProtocolClientImpl> g_client;
std::atomic<bool> g_running{true};
std::atomic<bool> g_connected{false};

// 消息发送计数器
std::atomic<int> g_message_counter{0};

/**
 * @brief 连接状态变化回调
 */
void onConnectionStateChanged(ConnectionState state, const std::string& description) {
    spdlog::info("Connection state changed: {} -> {}", 
                static_cast<int>(state), description);
    
    // 模拟原有的连接状态日志格式
    if (state == ConnectionState::CONNECTED) {
        spdlog::info("LocalClient -> Server is UP");
        g_connected = true;
        spdlog::info("Protocol test client connected, will send 103 test data automatically");
    } else if (state == ConnectionState::DISCONNECTED) {
        spdlog::info("LocalClient -> Server is DOWN");
        g_connected = false;
    }
}

/**
 * @brief 消息响应回调（使用新架构的消息处理结果）
 */
void onMessageResponse(uint8_t message_type, const MessageResult& result) {
    if (result.success) {
        // 新架构已经处理了消息并输出了详细信息
        spdlog::debug("Message type {} processed successfully: {}", message_type, result.description);
    } else {
        spdlog::warn("Message type {} processing failed: {}", message_type, result.description);
    }
}

/**
 * @brief 消息发送定时器线程（每2秒发送一次，按顺序1,2,4,5）
 */
void messageTimerThread() {
    while (g_running && g_client) {
        if (g_connected && g_client->IsConnected()) {
            int message_type_index = (g_message_counter++ % 4);
            uint8_t message_type;
            
            switch (message_type_index) {
                case 0: message_type = 1; break; // Type 1
                case 1: message_type = 2; break; // Type 2  
                case 2: message_type = 4; break; // Type 4
                case 3: message_type = 5; break; // Type 5
                default: message_type = 1; break;
            }
            
            // 创建默认请求（使用handler的默认值）
            MessageRequest request;
            
            // Type 5需要特殊处理，设置正确的目标地址
            if (message_type == 5) {
                request.target = 10; // rename_client的地址
                request.text_content = "./files/"; // 设置要重命名的目录路径
            }
            
            auto result = g_client->SendMessage(message_type, request);
            if (!result) {
                spdlog::error("Failed to send message type {}", message_type);
            }
        }
        
        // 等待2秒，但要检查是否需要退出
        for (int i = 0; i < 20 && g_running; ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
}

int main(int argc, char* argv[]) {
    spdlog::info("pid = {}", getpid());
    
    if (argc > 2) {
        try {
            // 创建协议客户端
            std::string config_file = "./config/config.json";
            g_client = std::make_unique<ProtocolClientImpl>(config_file);
            
            // 设置回调函数
            g_client->SetConnectionStateCallback(onConnectionStateChanged);
            g_client->SetMessageResponseCallback(onMessageResponse);
            
            // 启动客户端
            if (!g_client->Start()) {
                spdlog::error("Failed to start protocol client");
                return 1;
            }
            
            // 连接到服务器（使用配置文件中的地址）
            spdlog::info("Connecting to server (using config file settings)");
            
            if (!g_client->Connect()) {
                spdlog::error("Failed to connect to server");
                return 1;
            }
            
            // 启动消息发送定时器线程
            std::thread timer_thread(messageTimerThread);
            
            // 等待一段时间让数据传输完成，然后可以手动输入更多数据
            spdlog::info("Waiting for response... You can also input additional messages:");
            
            std::string line;
            while (std::getline(std::cin, line) && g_running) {
                if (line == "quit" || line == "exit") {
                    break;
                }
                
                // 发送原始消息（如果实现了的话）
                if (g_client && g_connected) {
                    auto result = g_client->SendRawMessage(line);
                    if (result) {
                        printf(">>> Sent raw message: %s\n", line.c_str());
                    } else {
                        printf(">>> Failed to send raw message: %s\n", line.c_str());
                    }
                }
            }
            
            // 优雅关闭
            g_running = false;
            if (timer_thread.joinable()) {
                timer_thread.join();
            }
            
            if (g_client && g_client->IsRunning()) {
                g_client->Stop();
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));  // wait for disconnect
            
        } catch (const std::exception& e) {
            spdlog::error("Exception in main: {}", e.what());
            return 1;
        }
    } else {
        printf("Usage: %s host_ip port\n", argv[0]);
    }
    
    return 0;
}