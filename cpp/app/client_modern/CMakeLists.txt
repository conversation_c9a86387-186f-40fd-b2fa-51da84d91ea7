# 现代化协议客户端应用程序

# 收集客户端源文件
set(CLIENT_SOURCES
    ${CMAKE_SOURCE_DIR}/app/client_modern/main.cpp
)

# 创建可执行文件
add_executable(protocol_client ${CLIENT_SOURCES})

# 设置可执行文件输出目录
set_target_properties(protocol_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置包含目录
target_include_directories(protocol_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)

# 链接库 - 只链接需要的库，避免protocol_server的编译问题
target_link_libraries(protocol_client
    PRIVATE
        zexuan_base
        zexuan_utils
        zexuan_event
        spdlog::spdlog
)

# 手动添加protocol_client源文件（避免整个zexuan_protocol库的依赖）
target_sources(protocol_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/client_types.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/protocol_client.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/protocol_client_impl.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/client_controller.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/client_executor.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/tcp_connection_manager.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/message_frame_parser.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/message_type_handler_factory.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/base_message_handler.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/handlers/type1_handler.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/handlers/type2_handler.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/handlers/type3_handler.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/handlers/type4_handler.cpp
        ${CMAKE_SOURCE_DIR}/source/zexuan/protocol/protocol_client/handlers/type5_handler.cpp
)
