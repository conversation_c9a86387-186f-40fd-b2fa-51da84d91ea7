#pragma once

#include "zexuan/protocol/protocol_server/transform/asdu_base.hpp"

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        /**
         * @brief GW104 ASDU Type3 处理类（时间戳事件上报）
         * 用于Service自动上报时间戳事件
         */
        class AsduType3GWS : public AsduBase {
        public:
          AsduType3GWS() = default;
          virtual ~AsduType3GWS() = default;

          // Type3专门用于事件上报，不处理CommonMessage双向转换
          // 使用基类的默认实现（返回-1表示不支持）

          /**
           * @brief 将EventMessage转换为协议帧（事件上报专用）
           * @param event_msg 输入的EventMessage
           * @param frame 输出的协议帧
           * @return 成功返回0，失败返回错误码
           */
          virtual int ConvertFromEventMessage(const base::EventMessage& event_msg,
                                              base::ProtocolFrame& frame) override;

          /**
           * @brief 获取支持的ASDU类型
           * @return ASDU类型标识
           */
          virtual uint8_t GetSupportedType() const override {
            return 3;  // Type 3 = 时间戳事件上报
          }

          /**
           * @brief 获取ASDU类型描述
           * @return 类型描述字符串
           */
          virtual const char* GetTypeDescription() const override {
            return "GW104 ASDU Type3 - Timestamp Event Report";
          }

        private:
          // 简化版本，不需要复杂的私有方法
        };

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan