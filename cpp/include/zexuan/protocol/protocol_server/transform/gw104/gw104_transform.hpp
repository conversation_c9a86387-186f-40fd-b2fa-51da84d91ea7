#ifndef GW104_TRANSFORM_HPP
#define GW104_TRANSFORM_HPP

#include <map>
#include <memory>
#include <unordered_map>

#include "zexuan/base/base_types.hpp"
#include "zexuan/protocol/protocol_server/transform/asdu_base.hpp"
#include "zexuan/protocol/protocol_server/transform/protocol_transform.hpp"

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        // IEC104 相关常量
        constexpr uint8_t IEC104_START_BYTE = 0x68;
        constexpr uint8_t IEC104_APCI_LENGTH = 6;

        // IEC104 ASDU 类型
        enum class AsduType : uint8_t {
          M_SP_NA_1 = 1  // 单点信息
        };

        // IEC104 传输原因
        enum class CauseOfTransmission : uint8_t {
          PERIODIC = 1  // 周期、循环
        };

        /**
         * @brief IEC104 协议转换器
         * 参考原始的 GW104 实现
         */
        class GW104Transform : public ProtocolTransform {
        public:
          GW104Transform();
          virtual ~GW104Transform() = default;

          // 实现基类接口
          int ConvertProToCommonMsg(const base::ProtocolFrameList& frame_list,
                                    std::vector<base::CommonMessage>& common_list,
                                    base::ProtocolFrameList& result_frames) override;

          // 其他方法暂时空实现
          int ConvertProToEventMsg(const base::ProtocolFrame& frame,
                                   std::vector<base::EventMessage>& event_list) override;

          int ConvertEventMsgToPro(const base::EventMessage& event_msg,
                                   base::ProtocolFrameList& frame_list) override;

          int ConvertCommonMsgToPro(const base::CommonMessage& common_msg,
                                    base::ProtocolFrameList& cmd_frames,
                                    base::ProtocolFrameList& result_frames) override;

          // 协议转换方法
          base::ProtocolConvertType GetConvertTypeByFrame(
              const base::ProtocolFrame& frame) override;

          /**
           * @brief 直接从本地获取数据并生成响应（参考原始 DirectResFromLocal）
           * @param request_frame 请求协议帧
           * @param response_frames 输出的响应协议帧列表
           * @return 转换结果，0表示成功
           */
          int DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                      base::ProtocolFrameList& response_frames) override;

        private:
          bool ValidateFrame(const std::vector<uint8_t>& data);

          // ASDU处理器管理（参考原始基类指针和虚函数模式）
          /**
           * @brief 获取ASDU处理器
           * @param type ASDU类型
           * @return ASDU处理器指针，失败返回nullptr
           */
          AsduBase* GetAsduHandler(uint8_t type);

          /**
           * @brief 初始化ASDU处理器
           */
          void InitializeAsduHandlers();

          // 协议帧类型判断方法（参考原始 _GetAsduXXCvtType 系列方法）
          base::ProtocolConvertType GetAsdu1ConvertType(const base::ProtocolFrame& frame);
          base::ProtocolConvertType GetAsdu2ConvertType(const base::ProtocolFrame& frame);
          base::ProtocolConvertType GetAsdu5ConvertType(const base::ProtocolFrame& frame);
          base::ProtocolConvertType GetDefaultConvertType(const base::ProtocolFrame& frame);

          // ASDU处理器存储（使用基类指针，参考原始模式）
          std::unordered_map<uint8_t, std::unique_ptr<AsduBase>> asdu_handlers_;

          // 测试接口
        public:
          /**
           * @brief 测试接口 - 创建一个简单的总召唤响应
           * @param asdu_addr ASDU地址
           * @param frame_list 输出帧列表
           * @return 转换结果
           */
          int CreateTestInterrogationResponse(uint16_t asdu_addr,
                                              base::ProtocolFrameList& frame_list);

          /**
           * @brief 测试接口 - 解析简单的总召唤命令
           * @param frame_list 输入帧列表
           * @param common_list 输出消息列表
           * @return 转换结果
           */
          int ParseTestInterrogationCommand(const base::ProtocolFrameList& frame_list,
                                            std::vector<base::CommonMessage>& common_list);
        };

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan

#endif  // GW104_TRANSFORM_HPP
