#pragma once

#include "zexuan/base/base_types.hpp"

namespace zexuan {
  namespace protocol {
    namespace transform {

      // ASDU处理基类（通用于各种协议规范）
      class AsduBase {
      public:
        AsduBase() = default;
        virtual ~AsduBase() = default;

        // 虚函数接口 - 子类根据需要重写

        /**
         * @brief 解析协议帧为CommonMessage
         * @param frame 协议帧
         * @param common_msg 输出的CommonMessage
         * @return 成功返回0，失败返回错误码
         * @note 默认实现返回不支持，子类可以重写以支持协议帧解析
         */
        virtual int ParseToCommonMessage(const base::ProtocolFrame& frame,
                                         base::CommonMessage& common_msg) {
          // 默认实现：不支持协议帧解析
          return -1;  // 表示不支持
        }

        /**
         * @brief 将CommonMessage转换为协议帧
         * @param common_msg 输入的CommonMessage
         * @param frame 输出的协议帧
         * @return 成功返回0，失败返回错误码
         * @note 默认实现返回不支持，子类可以重写以支持CommonMessage转换
         */
        virtual int ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                             base::ProtocolFrame& frame) {
          // 默认实现：不支持CommonMessage转换
          return -1;  // 表示不支持
        }

        /**
         * @brief 将EventMessage转换为协议帧（事件上报专用）
         * @param event_msg 输入的EventMessage
         * @param frame 输出的协议帧
         * @return 成功返回0，失败返回错误码
         * @note 默认实现返回不支持，子类可以重写以支持事件转换
         */
        virtual int ConvertFromEventMessage(const base::EventMessage& event_msg,
                                            base::ProtocolFrame& frame) {
          // 默认实现：不支持EventMessage转换
          return -1;  // 表示不支持
        }

        /**
         * @brief 直接从本地获取数据并生成协议帧（本地处理专用）
         * @param request_frame 请求协议帧
         * @param response_frames 输出的响应协议帧列表
         * @return 成功返回0，失败返回错误码
         * @note 默认实现返回不支持，子类可以重写以支持本地直接处理
         */
        virtual int DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                            base::ProtocolFrameList& response_frames) {
          // 默认实现：不支持本地直接处理
          return -1;  // 表示不支持
        }

        /**
         * @brief 获取支持的ASDU类型
         * @return ASDU类型标识
         * @note 子类必须重写此方法
         */
        virtual uint8_t GetSupportedType() const = 0;

        /**
         * @brief 获取ASDU类型描述
         * @return 类型描述字符串
         * @note 子类必须重写此方法
         */
        virtual const char* GetTypeDescription() const = 0;
      };

    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan
