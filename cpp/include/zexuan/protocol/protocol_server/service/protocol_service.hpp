#ifndef PROTOCOL_SERVICE_HPP
#define PROTOCOL_SERVICE_HPP

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <vector>

#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/controller.hpp"
#include "zexuan/base/executor.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/event/event_loop.hpp"

namespace zexuan {
  namespace protocol {

    // 前向声明
    namespace transform {
      class ProtocolTransform;
    }

    namespace service {

      /**
       * @brief 协议服务类
       * 参考原始的 TNXEcMsgOperationObj 设计
       * 负责业务逻辑处理和消息转换
       */
      class ProtocolService : public base::LifecycleComponentBase {
      public:
        // 构造函数 - 支持多连接的动态ID分配
        explicit ProtocolService(const std::string& config_file_path,
                                std::shared_ptr<base::Mediator> mediator,
                                base::ObjectId service_instance_id = base::SERVICE_SUBJECT_ID);
        virtual ~ProtocolService();

        // 生命周期管理 - 实现ILifecycleComponent接口
        bool Start() override;
        bool Stop() override;

        // 设置Transform（用于协议转换）
        void SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform);

        // 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置

      private:
        // 基础成员
        std::shared_ptr<base::Mediator> mediator_;
        std::string config_file_path_;
        base::ObjectId service_instance_id_;  // 服务实例ID，用于Bus注册

        // Controller和Executor（参考原始设计）
        std::shared_ptr<base::Controller> controller_;
        std::shared_ptr<base::Executor> executor_;

        // 协议转换器
        std::unique_ptr<transform::ProtocolTransform> protocol_transform_;

        // TCP消息总线客户端
        std::shared_ptr<zexuan::event::EventLoop> bus_event_loop_;
        std::shared_ptr<zexuan::bus::TcpBusClient> bus_client_;
        std::thread bus_thread_;  // 运行总线事件循环的线程
        std::string bus_host_;
        uint16_t bus_port_;

        // 状态管理
        std::atomic<bool> is_initialized_{false};
        std::atomic<bool> is_running_{false};
        std::atomic<bool> should_stop_{false};

        // 线程管理（业务处理线程已移除）

        // 自动事件上报功能已移植到tcp_bus_client

        // 消息队列已移除，消息直接转发到总线

        // 事件通过Subject发送，无需回调成员变量

        // 消息处理回调
        void OnCommonMessage(const base::CommonMessage& message);
        void OnEventMessage(const base::EventMessage& message);

        // 总线消息处理回调
        void OnBusCommonMessage(const zexuan::base::CommonMessage& message);
        void OnBusEventMessage(const zexuan::base::EventMessage& message);
        void OnBusControlMessage(const base::ControlMessage& message);

        // 总线相关方法
        bool InitializeBusClient();
        void StartBusEventLoop();
        void StopBusEventLoop();

        // 生命周期管理辅助方法
        bool LoadConfiguration();
        bool CreateControllerAndExecutor();
      };

    }  // namespace service
  }  // namespace protocol
}  // namespace zexuan

#endif  // PROTOCOL_SERVICE_HPP