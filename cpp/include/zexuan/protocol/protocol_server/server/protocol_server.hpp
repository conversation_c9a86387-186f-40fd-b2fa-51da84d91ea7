#pragma once

#include <spdlog/spdlog.h>

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

// 引入现有的网络库
#include "zexuan/event/net/address.hpp"
#include "zexuan/event/net/buffer.hpp"
#include "zexuan/event/callbacks.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/tcp_connection.hpp"
#include "zexuan/event/net/tcp_server.hpp"

// 引入协议网关
#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/protocol/protocol_server/gateway/protocol_gateway.hpp"
#include "zexuan/base/types/basic_types.hpp"

namespace zexuan {
  namespace protocol {
    namespace server {

      /**
       * @brief 协议服务器类 - 基于现有网络库的简单封装
       *
       * 职责：
       * 1. 管理TCP连接和对应的ProtocolGateway
       * 2. 直接回调Gateway处理协议数据
       * 3. 利用现有的网络库处理TCP连接
       */
      class ProtocolServer : public base::LifecycleComponentBase {
      public:
        explicit ProtocolServer(event::EventLoop* event_loop, const std::string& config_file_path);
        virtual ~ProtocolServer();

        // 生命周期管理 - 实现ILifecycleComponent接口
        bool Start() override;
        bool Stop() override;
        size_t GetConnectionCount() const;
        std::vector<std::string> GetAllConnectionNames() const;

        // 统计信息
        struct Statistics {
          std::atomic<uint64_t> total_connections{0};
          std::atomic<uint64_t> active_connections{0};
          std::atomic<uint64_t> bytes_sent{0};
          std::atomic<uint64_t> bytes_received{0};
        };

        const Statistics& GetStatistics() const { return stats_; }
        void ResetStatistics();

        /**
         * @brief 发送数据到指定连接
         * @param conn 连接对象
         * @param data 数据
         * @return 是否发送成功
         */
        bool SendData(const event::TcpConnectionPtr& conn, const std::vector<uint8_t>& data);

      private:
        // 网络回调处理
        void OnConnection(const event::TcpConnectionPtr& conn);
        void OnMessage(const event::TcpConnectionPtr& conn, event::Buffer* buffer,
                       event::Timestamp receiveTime);
        void OnWriteComplete(const event::TcpConnectionPtr& conn);

        // 连接管理（简化：直接管理TcpConnection）
        void AddConnection(const event::TcpConnectionPtr& conn);
        void RemoveConnection(const event::TcpConnectionPtr& conn);

        // Gateway管理（直接与TcpConnection绑定）
        bool CreateGatewayForConnection(const event::TcpConnectionPtr& conn);
        void DestroyGatewayForConnection(const event::TcpConnectionPtr& conn);

        // Service ID管理
        base::ObjectId AllocateServiceId(const event::TcpConnectionPtr& conn);
        void ReleaseServiceId(const event::TcpConnectionPtr& conn);

        // 生命周期管理辅助方法
        bool LoadConfiguration();

        // 分帧处理（直接使用TcpConnection）
        bool ExtractOneFrame(event::Buffer* buffer, const event::TcpConnectionPtr& conn);
        void SendFrameToGateway(const event::TcpConnectionPtr& conn,
                                const std::vector<uint8_t>& frame_data);

      private:
        // 网络组件
        event::EventLoop* event_loop_;  // 外部传入，不拥有所有权
        std::unique_ptr<event::TcpServer> tcp_server_;

        // 连接管理（简化：直接管理连接，不需要ID映射）
        std::unordered_set<event::TcpConnectionPtr> connections_;
        mutable std::mutex connections_mutex_;

        // Gateway管理（直接与TcpConnection绑定）
        std::unordered_map<event::TcpConnectionPtr,
                           std::shared_ptr<protocol::gateway::ProtocolGateway>>
            gateways_;
        mutable std::mutex gateways_mutex_;

        // Service ID管理（为每个连接分配唯一的Service ID）
        std::unordered_map<event::TcpConnectionPtr, base::ObjectId> connection_service_ids_;
        std::set<base::ObjectId> allocated_service_ids_;
        base::ObjectId next_service_id_{base::DYNAMIC_SERVICE_ID_START};
        mutable std::mutex service_id_mutex_;

        // Mediator（用于Gateway间通信）
        std::shared_ptr<base::Mediator> mediator_;

        // 配置文件路径
        std::string config_file_path_;

        // 配置参数（从JSON读取）
        std::string listen_address_;
        uint16_t listen_port_;
        int max_connections_;
        int thread_pool_size_;

        // 状态
        std::atomic<bool> is_initialized_{false};
        std::atomic<bool> is_running_{false};

        // 统计信息
        Statistics stats_;
      };

    }  // namespace server
  }  // namespace protocol
}  // namespace zexuan
