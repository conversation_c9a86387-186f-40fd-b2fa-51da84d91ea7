#ifndef PROTOCOL_GATEWAY_SIMPLE_HPP
#define PROTOCOL_GATEWAY_SIMPLE_HPP

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <queue>
#include <thread>
#include <unordered_map>
#include <vector>

#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/controller.hpp"
#include "zexuan/base/executor.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"

namespace zexuan {
  namespace protocol {

    // 前向声明
    namespace transform {
      class ProtocolTransform;
    }
    namespace service {
      class ProtocolService;
    }

    namespace gateway {

      // 响应请求结构体（简化版本）
      struct ResponseRequest {
        std::vector<base::ProtocolFrame> frame_list;        // 协议帧列表
        std::vector<base::CommonMessage> nx_response_list;  // Service响应列表
        std::vector<std::string> sent_invoke_ids;           // 已发送的invoke_id列表
        bool is_ctrl_cmd = false;                           // 是否为控制命令
        bool commands_sent = false;                         // 是否已发送给Service
        std::chrono::system_clock::time_point create_time;  // 创建时间
        // 移除conn_id字段，因为每个Gateway只对应一个连接

        ResponseRequest() : create_time(std::chrono::system_clock::now()) {}
      };

      /**
       * @brief 协议网关类 - 简化版
       */
      class ProtocolGateway : public base::LifecycleComponentBase {
      public:
        // 构造函数 - 支持多连接的Service ID传递
        ProtocolGateway(const std::string& config_file_path,
                        std::shared_ptr<base::Mediator> mediator,
                        base::ObjectId service_id = base::SERVICE_SUBJECT_ID);

        // 析构函数
        virtual ~ProtocolGateway();

        // 生命周期管理 - 实现ILifecycleComponent接口
        bool Start() override;
        bool Stop() override;
        bool IsRunning() const { return is_running_.load(); }

        // 消息处理接口（直接回调机制）
        void OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data);
        void AddServiceResponseToRequest(const base::CommonMessage& result_msg);
        void OnServiceEvent(const base::EventMessage& event_msg);

        // 发送回调设置（每个Gateway对应一个连接，不需要conn_id）
        void SetSendCallback(std::function<bool(const std::vector<uint8_t>&)> callback);

      protected:
        // 核心线程方法（通俗易懂的命名）
        void ProtocolFrameProcessorLoop();  // 协议帧处理线程 - 处理网络收到的协议帧
        void LocalTaskProcessorLoop();      // 本地任务处理线程 - 处理本地响应任务
        void ResponseMatcherLoop();         // 响应匹配线程 - 处理响应合并和匹配
        void EventProcessorLoop();          // 事件处理线程 - 处理事件上报

        // 协议转换方法
        std::vector<uint8_t> ConvertEventToProtocolBytes(const base::EventMessage& event);

        // 协议帧解析方法
        bool ParseProtocolFrame(const std::vector<uint8_t>& data, base::ProtocolFrame& frame);
        bool IsLastFrame(const base::ProtocolFrame& frame);

        // 新增的协议帧处理方法（参考原始 ProOperation）
        void HandleSingleFrameCommand(const base::ProtocolFrame& frame);
        void HandleControlCommand(const base::ProtocolFrame& frame);
        void HandleLocalResponse(const base::ProtocolFrame& frame);
        void HandleEventFrame(const base::ProtocolFrame& frame);

        // 响应处理方法
        bool AddFrameToResponseRequest(const base::ProtocolFrame& frame, bool is_ctrl_cmd = true);
        std::string GenerateResponseRequestKey(const base::ProtocolFrame& frame);

        // 核心处理方法（参考原始实现）
        void CallResponseHandle();
        bool ProcessControlCommand(const std::string& request_key, ResponseRequest& request);
        bool PackageResultHandle(const std::string& request_key, ResponseRequest& request);
        bool PackageTimeoutHandle(const std::string& request_key, ResponseRequest& request);

        // 辅助方法
        void ProcessCompleteResponse(const std::string& request_key, ResponseRequest& request);
        void CleanupInvokeIdMappings(const ResponseRequest& request);

        // 组件创建方法
        bool CreateProtocolTransform();
        bool CreateProtocolService();

        // 生命周期管理辅助方法
        bool LoadConfiguration();
        bool CreateControllerAndExecutor();

      private:
        // 基础成员
        std::shared_ptr<base::Mediator> mediator_;
        std::string config_file_path_;
        base::ObjectId service_id_;  // 传递给Service的唯一ID

        // 配置参数（从JSON读取）
        uint32_t request_timeout_seconds_;
        uint32_t thread_sleep_ms_;
        uint32_t max_pending_requests_;
        std::string protocol_type_;

        // 核心组件
        std::unique_ptr<transform::ProtocolTransform> protocol_transform_;
        std::unique_ptr<service::ProtocolService> protocol_service_;

        // Controller和Executor组合
        std::shared_ptr<base::Controller> controller_;
        std::shared_ptr<base::Executor> executor_;

        // 发送回调（每个Gateway对应一个连接）
        std::function<bool(const std::vector<uint8_t>&)> send_callback_;

        // 状态管理
        std::atomic<bool> is_running_{false};
        std::atomic<bool> should_stop_{false};

        // ========== 4个核心队列（整齐排列）==========

        // 1. 协议命令队列 - 网络收到的原始协议帧（对应原始 m_ProCmdDeque）
        std::queue<base::ProtocolFrame> protocol_cmd_queue_;
        std::mutex protocol_cmd_queue_mutex_;
        std::condition_variable protocol_cmd_queue_cv_;

        // 2. 响应匹配队列 - 响应请求管理（对应原始 m_AskResPackegDeque）
        std::map<std::string, ResponseRequest> response_requests_;
        std::mutex response_requests_mutex_;

        // invoke_id 到 request_key 的映射表（优化响应匹配性能）
        std::unordered_map<std::string, std::string> invoke_id_to_request_key_;
        // 注意：invoke_id_to_request_key_ 与 response_requests_ 共享同一个锁

        // 3. 事件队列 - 事件上报（对应原始 m_NxEventDeque）
        std::queue<base::EventMessage> event_queue_;
        std::mutex event_queue_mutex_;
        std::condition_variable event_queue_cv_;

        // 4. 本地任务队列 - 本地响应任务
        std::queue<base::ProtocolFrame> local_task_queue_;
        std::mutex local_task_queue_mutex_;
        std::condition_variable local_task_queue_cv_;

        // ========== 其他管理数据 ==========

        // ========== 4个核心线程 ==========

        std::thread protocol_frame_processor_thread_;  // 协议帧处理线程
        std::thread local_task_processor_thread_;      // 本地任务处理线程
        std::thread response_matcher_thread_;          // 响应匹配线程
        std::thread event_processor_thread_;           // 事件处理线程
      };

    }  // namespace gateway
  }  // namespace protocol
}  // namespace zexuan

#endif  // PROTOCOL_GATEWAY_SIMPLE_HPP
