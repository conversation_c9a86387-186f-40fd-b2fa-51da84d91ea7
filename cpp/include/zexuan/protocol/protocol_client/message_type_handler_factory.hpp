/**
 * @file message_type_handler_factory.hpp
 * @brief 消息类型处理器工厂
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_FACTORY_HPP
#define ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_FACTORY_HPP

#include <cstdint>
#include <memory>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <functional>

#include "message_type_handler.hpp"
#include "client_types.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 消息类型处理器工厂
     * 
     * 负责管理和创建各种消息类型处理器
     * 支持动态注册和查询处理器
     */
    class MessageTypeHandlerFactory {
    public:
        /**
         * @brief 构造函数
         */
        MessageTypeHandlerFactory();

        /**
         * @brief 析构函数
         */
        ~MessageTypeHandlerFactory();

        /**
         * @brief 注册消息类型处理器
         * @param handler 消息处理器（工厂将接管其所有权）
         * @return 注册结果
         */
        bool RegisterHandler(std::unique_ptr<IMessageTypeHandler> handler);

        /**
         * @brief 注销消息类型处理器
         * @param message_type 消息类型
         * @return 注销结果
         */
        bool UnregisterHandler(uint8_t message_type);

        /**
         * @brief 检查是否支持指定消息类型
         * @param message_type 消息类型
         * @return true: 支持, false: 不支持
         */
        bool SupportsMessageType(uint8_t message_type) const;

        /**
         * @brief 获取指定消息类型的处理器
         * @param message_type 消息类型
         * @return 处理器指针，如果不存在则返回nullptr
         */
        IMessageTypeHandler* GetHandler(uint8_t message_type) const;

        /**
         * @brief 获取所有支持的消息类型
         * @return 消息类型列表
         */
        std::vector<uint8_t> GetSupportedTypes() const;

        /**
         * @brief 检查是否有指定消息类型的处理器
         * @param message_type 消息类型
         * @return true: 有, false: 没有
         */
        bool HasHandler(uint8_t message_type) const;

        /**
         * @brief 获取已注册的处理器数量
         * @return 处理器数量
         */
        size_t GetRegisteredTypesCount() const;

        /**
         * @brief 创建消息
         * @param message_type 消息类型
         * @param request 消息请求参数
         * @return 创建的消息对象，失败返回错误
         */
        base::Result<base::Message> CreateMessage(uint8_t message_type, const MessageRequest& request) const;

        /**
         * @brief 处理响应消息
         * @param message 收到的响应消息
         * @return 处理结果
         */
        base::Result<MessageResult> ProcessResponse(const base::Message& message) const;

        /**
         * @brief 验证消息请求参数
         * @param message_type 消息类型
         * @param request 请求参数
         * @return 验证结果
         */
        bool ValidateRequest(uint8_t message_type, const MessageRequest& request) const;

        /**
         * @brief 获取工厂统计信息
         */
        struct Statistics {
            uint32_t registered_handlers = 0;   ///< 注册的处理器数量
            uint64_t messages_created = 0;       ///< 创建的消息数量
            uint64_t responses_processed = 0;    ///< 处理的响应数量
            uint64_t validation_failures = 0;   ///< 验证失败次数
        };
        Statistics GetStatistics() const;

        /**
         * @brief 清空所有注册的处理器
         */
        void ClearAllHandlers();

        /**
         * @brief 注册内置处理器
         * @return 注册结果
         */
        bool RegisterBuiltinHandlers();

    private:
        // 处理器映射表 (消息类型 -> 处理器)
        mutable std::mutex handlers_mutex_;
        std::unordered_map<uint8_t, std::unique_ptr<IMessageTypeHandler>> handlers_;

        // 统计信息
        mutable std::mutex stats_mutex_;
        mutable Statistics statistics_;

        // 私有方法
        void UpdateStatistics(const std::function<void(Statistics&)>& updater) const;
        bool IsValidMessageType(uint8_t message_type) const;
    };

    /**
     * @brief 全局消息处理器工厂实例
     * 方便在不同组件间共享处理器
     */
    MessageTypeHandlerFactory& GetGlobalHandlerFactory();

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_FACTORY_HPP
