/**
 * @file client_controller.hpp
 * @brief 协议客户端控制器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_CLIENT_CONTROLLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_CLIENT_CONTROLLER_HPP

#include <functional>
#include <memory>

#include "zexuan/base/controller.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/protocol/protocol_client/client/client_types.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    class ProtocolClientImpl;

    /**
     * @brief 客户端控制器
     * 
     * 继承zexuan::base::Controller，负责：
     * 1. 接收外部命令（如果有）
     * 2. 处理事件消息
     * 3. 与主客户端实例协调工作
     */
    class ClientController : public base::Controller {
    public:
        /**
         * @brief 构造函数
         * @param object_id 控制器ID
         * @param mediator 中介者
         * @param client 主客户端实例（弱引用）
         */
        ClientController(base::ObjectId object_id, 
                        std::shared_ptr<base::Mediator> mediator,
                        ProtocolClientImpl* client);

        /**
         * @brief 析构函数
         */
        ~ClientController() noexcept override;

        /**
         * @brief 重写命令处理（如果需要接收外部命令）
         * @param result_msg 结果消息
         * @param source_id 来源ID
         * @return 处理结果
         */
        base::Result<void> ReplyResult(const base::CommonMessage& result_msg, 
                                     base::ObjectId source_id) override;

        /**
         * @brief 重写事件处理
         * @param event_msg 事件消息
         * @param source_id 来源ID
         * @return 处理结果
         */
        base::Result<void> PushEventNotify(const base::EventMessage& event_msg, 
                                         base::ObjectId source_id) override;

    protected:
        /**
         * @brief 派生类特定的注册逻辑
         * @return 操作结果
         */
        base::VoidResult DoRegister() override;

        /**
         * @brief 派生类特定的注销逻辑  
         * @return 操作结果
         */
        base::VoidResult DoUnregister() noexcept override;

    private:
        ProtocolClientImpl* client_; ///< 主客户端实例（弱引用）
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_CLIENT_CONTROLLER_HPP
