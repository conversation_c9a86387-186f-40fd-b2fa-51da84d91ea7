/**
 * @file client_executor.hpp
 * @brief 协议客户端执行器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_CLIENT_EXECUTOR_HPP
#define ZEXUAN_PROTOCOL_CLIENT_CLIENT_EXECUTOR_HPP

#include <functional>
#include <memory>

#include "zexuan/base/executor.hpp"
#include "zexuan/base/mediator.hpp"
#include "client_types.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    class ProtocolClientImpl;

    /**
     * @brief 客户端执行器
     * 
     * 继承zexuan::base::Executor，负责：
     * 1. 处理接收到的命令
     * 2. 发送结果和事件通知
     * 3. 与主客户端实例协调工作
     */
    class ClientExecutor : public base::Executor {
    public:
        /**
         * @brief 构造函数
         * @param object_id 执行器ID
         * @param mediator 中介者
         * @param client 主客户端实例（弱引用）
         */
        ClientExecutor(base::ObjectId object_id, 
                      std::shared_ptr<base::Mediator> mediator,
                      ProtocolClientImpl* client);

        /**
         * @brief 析构函数
         */
        ~ClientExecutor() noexcept override;

        /**
         * @brief 重写命令处理
         * @param command_msg 命令消息
         * @param source_id 来源ID
         * @return 处理结果
         */
        base::Result<void> PushCommand(const base::CommonMessage& command_msg, 
                                     base::ObjectId source_id) override;

    protected:
        /**
         * @brief 派生类特定的注册逻辑
         * @return 操作结果
         */
        base::VoidResult DoRegister() override;

        /**
         * @brief 派生类特定的注销逻辑
         * @return 操作结果
         */
        base::VoidResult DoUnregister() noexcept override;

    private:
        ProtocolClientImpl* client_; ///< 主客户端实例（弱引用）
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_CLIENT_EXECUTOR_HPP
