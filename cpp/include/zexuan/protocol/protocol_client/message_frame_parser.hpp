/**
 * @file message_frame_parser.hpp
 * @brief IEC 60870-5-103 消息帧解析器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_MESSAGE_FRAME_PARSER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_MESSAGE_FRAME_PARSER_HPP

#include <cstdint>
#include <functional>
#include <vector>
#include <mutex>

#include "zexuan/base/message.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 完整帧回调函数类型
     * @param message 解析完成的消息对象
     */
    using CompleteFrameCallback = std::function<void(const base::Message& message)>;

    /**
     * @brief 原始帧回调函数类型（用于无法解析的帧）
     * @param frame_data 原始帧数据
     */
    using RawFrameCallback = std::function<void(const std::vector<uint8_t>& frame_data)>;

    /**
     * @brief IEC 60870-5-103 消息帧解析器
     * 
     * 负责从TCP数据流中提取完整的IEC 60870-5-103消息帧
     * 支持分帧处理和协议解析
     */
    class MessageFrameParser {
    public:
        /**
         * @brief 构造函数
         */
        MessageFrameParser();

        /**
         * @brief 析构函数
         */
        ~MessageFrameParser();

        /**
         * @brief 处理接收到的数据
         * @param data 接收到的数据
         * @param size 数据大小
         */
        void ProcessData(const uint8_t* data, size_t size);

        /**
         * @brief 设置完整帧回调
         * @param callback 回调函数
         */
        void SetCompleteFrameCallback(CompleteFrameCallback callback);

        /**
         * @brief 设置原始帧回调（用于无法解析的帧）
         * @param callback 回调函数
         */
        void SetRawFrameCallback(RawFrameCallback callback);

        /**
         * @brief 清空接收缓冲区
         */
        void ClearBuffer();

        /**
         * @brief 获取缓冲区统计信息
         */
        struct Statistics {
            size_t buffer_size = 0;           ///< 当前缓冲区大小
            uint64_t frames_processed = 0;    ///< 已处理帧数
            uint64_t bytes_processed = 0;     ///< 已处理字节数
            uint64_t parse_errors = 0;        ///< 解析错误次数
            uint64_t invalid_frames = 0;      ///< 无效帧数
        };
        Statistics GetStatistics() const;

    private:
        // 接收缓冲区
        std::vector<uint8_t> receive_buffer_;
        mutable std::mutex buffer_mutex_;

        // 回调函数
        std::mutex callback_mutex_;
        CompleteFrameCallback complete_frame_callback_;
        RawFrameCallback raw_frame_callback_;

        // 统计信息
        mutable std::mutex stats_mutex_;
        Statistics statistics_;

        // IEC 60870-5-103 协议常量
        static constexpr uint8_t START_CHAR = 0x68;     ///< 起始字符
        static constexpr size_t MIN_FRAME_SIZE = 3;     ///< 最小帧大小 (起始+长度2字节)
        static constexpr size_t MAX_ASDU_LENGTH = 1024; ///< 最大ASDU长度

        // 私有方法
        std::vector<uint8_t> ExtractCompleteFrame();
        void ProcessCompleteFrame(const std::vector<uint8_t>& frame_data);
        bool ValidateFrame(const std::vector<uint8_t>& frame_data) const;
        void UpdateStatistics(const std::function<void(Statistics&)>& updater);
        void CallCompleteFrameCallback(const base::Message& message);
        void CallRawFrameCallback(const std::vector<uint8_t>& frame_data);
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_MESSAGE_FRAME_PARSER_HPP
