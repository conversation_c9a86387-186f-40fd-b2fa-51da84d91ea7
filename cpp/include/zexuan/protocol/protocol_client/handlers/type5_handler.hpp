/**
 * @file type5_handler.hpp
 * @brief Type 5 重命名请求消息处理器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE5_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE5_HANDLER_HPP

#include "../base_message_handler.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief Type 5 重命名请求消息处理器
     * 
     * 处理IEC 60870-5-103协议中的Type 5（重命名请求）消息
     * 对应原始ChatClient中的sendRenameRequest功能
     */
    class Type5Handler : public SingleTypeMessageHandler {
    public:
        /**
         * @brief 构造函数
         */
        Type5Handler();

        /**
         * @brief 析构函数
         */
        ~Type5Handler() override = default;

    protected:
        // 实现SingleTypeMessageHandler的纯虚函数
        bool ValidateTypeSpecificRequest(const MessageRequest& request) const override;
        base::Result<base::Message> CreateTypeSpecificMessage(const MessageRequest& request) const override;
        base::Result<MessageResult> ProcessTypeSpecificResponse(const base::Message& response) const override;

    private:
        // Type 5 消息的默认参数
        static constexpr uint8_t DEFAULT_VSQ = 0x81;    ///< 默认可变结构限定词（单信息）
        static constexpr uint8_t DEFAULT_COT = 0x06;    ///< 默认传送原因（激活）
        static constexpr uint8_t DEFAULT_FUN = 0x01;    ///< 默认功能类型（文件重命名）
        static constexpr uint8_t DEFAULT_INF = 0x01;    ///< 默认信息序号（重命名信息）
        
        // 默认重命名路径
        static const std::string DEFAULT_RENAME_PATH;
        static constexpr uint8_t DEFAULT_TARGET_ADDRESS = 10; ///< 默认目标地址（rename_client）

        /**
         * @brief 验证路径格式
         * @param path 路径字符串
         * @return 验证结果
         */
        bool ValidatePath(const std::string& path) const;

        /**
         * @brief 解析重命名响应结果
         * @param response_cot 传送原因
         * @param content 响应内容
         * @return 结果描述
         */
        std::string ParseRenameResult(uint8_t response_cot, const std::string& content) const;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE5_HANDLER_HPP
