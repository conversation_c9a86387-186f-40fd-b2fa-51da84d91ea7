#ifndef ZEXUAN_EVENT_SIGNAL_HANDLER_HPP
#define ZEXUAN_EVENT_SIGNAL_HANDLER_HPP

#include <functional>
#include <map>
#include <memory>
#include <signal.h>
#include "zexuan/event/callbacks.hpp"
namespace zexuan {
  namespace event {

    class EventLoop;
    class Channel;

    class SignalHandler {
    public:
      using SignalCallback = std::function<void(int signal)>;

      explicit SignalHandler(EventLoop* loop);
      ~SignalHandler();

      // 禁用拷贝构造和赋值
      SignalHandler(const SignalHandler&) = delete;
      SignalHandler& operator=(const SignalHandler&) = delete;

      // 信号注册接口
      void registerSignal(int signal, SignalCallback cb);
      void unregisterSignal(int signal);

      // 便捷接口
      void registerShutdownSignals(SignalCallback cb);
      void registerUserSignals(SignalCallback cb);

    private:
      void handleRead();
      void updateSignalMask();
      void registerSignalInLoop(int signal, SignalCallback cb);
      void unregisterSignalInLoop(int signal);

      EventLoop* loop_;
      int signalfd_;
      std::unique_ptr<Channel> signalChannel_;
      std::map<int, SignalCallback> signalCallbacks_;
      sigset_t signalMask_;
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_SIGNAL_HANDLER_HPP
