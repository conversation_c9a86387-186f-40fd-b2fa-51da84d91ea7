# IEC 60870-5-103 协议客户端重构开发计划书

## 项目概览
**目标**: 重构现有ChatClient，构建基于Controller-Executor-Mediator架构的可扩展协议客户端
**范围**: 支持Type 1-255消息类型，实现插件化消息处理器架构
**关键技术**: C++17、Controller-Executor-Mediator模式、工厂模式、策略模式

---

## 阶段1：基础架构搭建

### 1.1 核心接口设计
- [ ] 👤 设计IMessageTypeHandler接口定义
  - 定义消息类型处理器的统一接口
  - 包含CanHandle、CreateMessage、ProcessResponse等核心方法
  - 确定消息请求和响应的数据结构

- [ ] 👤 设计IProtocolClient主接口
  - 定义客户端对外提供的核心功能接口
  - 包含Start、Stop、SendMessage、RegisterMessageHandler等方法
  - 确保与现有代码的兼容性

- [ ] 👤 设计配置和错误处理接口
  - 定义配置加载接口和配置数据结构
  - 设计统一的错误码和Result类型系统
  - 定义日志管理器的集成接口

### 1.2 基础类框架实现
- [ ] 创建ProtocolClient主类框架
  - 继承IProtocolClient接口
  - 集成Controller-Executor-Mediator架构
  - 实现基础的生命周期管理

- [ ] 实现ClientController类
  - 继承zexuan::base::Controller
  - 实现消息接收和事件处理逻辑
  - 设置消息回调机制

- [ ] 实现ClientExecutor类  
  - 继承zexuan::base::Executor
  - 实现命令处理和结果发送逻辑
  - 建立与Controller的通信机制

- [ ] 集成Mediator和生命周期管理
  - 将Controller和Executor注册到Mediator
  - 实现Start/Stop生命周期方法
  - 添加组件状态管理和错误处理

---

## 阶段2：配置与日志系统集成

### 2.1 配置系统实现
- [ ] 实现配置文件加载逻辑
  - 从config.json读取protocol.client.server_host和server_port
  - 添加配置验证和错误处理
  - 实现配置热重载机制（可选）

- [ ] 👤 定义客户端专属配置结构
  - 设计ClientConfig数据结构
  - 添加连接超时、重试次数等可配置参数
  - 确保配置的向后兼容性

### 2.2 日志系统集成
- [ ] 集成LoggerManager
  - 在客户端启动时初始化日志系统
  - 设置客户端专属的日志文件名
  - 配置日志等级和输出格式

- [ ] 实现组件日志规范
  - 为每个组件添加GetComponentName方法
  - 按照spdlog规范添加生命周期日志
  - 实现错误和调试信息的结构化日志记录

---

## 阶段3：TCP连接管理重构

### 3.1 连接管理器实现
- [ ] 创建TcpConnectionManager类
  - 封装现有的TcpClient功能
  - 实现连接状态管理和监控
  - 添加自动重连和连接健康检查

- [ ] 实现连接事件处理
  - 处理连接建立、断开事件
  - 实现连接状态变化的通知机制
  - 添加连接异常的恢复逻辑

- [ ] 重构消息发送接口
  - 将现有的write方法重构为异步发送
  - 实现发送队列和流控机制
  - 添加发送失败的重试逻辑

### 3.2 消息帧解析重构
- [ ] 创建MessageFrameParser类
  - 从现有的onMessage方法提取帧解析逻辑
  - 实现完整帧检测和分帧处理
  - 保持与现有IEC 60870-5-103协议的兼容性

- [ ] 实现帧缓冲管理
  - 重构receive_buffer_的管理逻辑
  - 实现高效的帧边界检测
  - 添加帧解析错误的处理机制

---

## 阶段4：消息处理器架构实现

### 4.1 消息处理器工厂
- [ ] 实现MessageTypeHandlerFactory类
  - 采用工厂模式管理消息处理器
  - 支持运行时注册新的消息类型处理器
  - 实现处理器的查找和创建逻辑

- [ ] 👤 设计处理器注册机制
  - 定义处理器注册的接口和流程
  - 支持插件式加载新的消息类型处理器
  - 确保处理器间的隔离和安全性

### 4.2 基础消息处理器实现
- [ ] 实现BaseMessageHandler抽象类
  - 提供消息处理器的通用实现框架
  - 实现消息创建和响应处理的模板方法
  - 添加通用的错误处理和日志记录

- [ ] 👤 定义消息处理的通用流程
  - 设计消息处理的标准化流程
  - 定义消息验证、处理、响应的标准接口
  - 确保所有处理器遵循统一的处理模式

---

## 阶段5：具体消息类型处理器实现

### 5.1 Type 1-5 消息处理器迁移
- [ ] 实现Type1Handler（单帧测试）
  - 从现有sendSingleFrameTest方法迁移逻辑
  - 实现基于IMessageTypeHandler接口的处理器
  - 保持原有的消息格式和参数设置

- [ ] 实现Type2Handler（多帧测试）
  - 从现有sendMultiFrameTest方法迁移逻辑
  - 实现多帧消息的构造和发送逻辑
  - 添加多帧处理的状态管理

- [ ] 实现Type3Handler（时间戳事件）
  - 从现有parseType3TimestampEvent方法迁移逻辑
  - 实现时间戳解析和事件处理逻辑
  - 保持IEC 60870-5-103时间格式兼容

- [ ] 实现Type4Handler（本地状态）
  - 从现有Type4相关方法迁移逻辑
  - 实现状态查询请求的构造和响应解析
  - 添加状态数据的结构化处理

- [ ] 实现Type5Handler（重命名请求）
  - 从现有sendRenameRequest方法迁移逻辑
  - 实现文件重命名请求的处理逻辑
  - 保持与现有目标地址映射的兼容性

### 5.2 处理器注册和集成
- [ ] 实现处理器的自动注册
  - 在客户端启动时自动注册Type1-5处理器
  - 实现处理器的依赖注入和初始化
  - 添加处理器注册失败的错误处理

- [ ] 👤 验证处理器架构的扩展性
  - 确认新增Type6处理器的实现流程
  - 验证处理器间的隔离和独立性
  - 确保处理器架构满足Type1-255的扩展需求

---

## 阶段6：系统集成与优化

### 6.1 组件集成
- [ ] 集成所有核心组件
  - 将TcpConnectionManager集成到ProtocolClient
  - 将MessageFrameParser集成到消息处理流程
  - 确保Controller-Executor-Mediator的正确交互

- [ ] 实现消息处理主流程
  - 建立从TCP接收到消息处理器的完整链路
  - 实现消息分发和处理结果的回调机制
  - 添加消息处理失败的降级处理

### 6.2 错误处理和稳定性
- [ ] 实现全面的错误处理
  - 为每个组件添加异常安全保证
  - 实现资源泄漏防护和自动清理
  - 添加组件故障的隔离和恢复机制

- [ ] 👤 性能优化和内存管理
  - 优化消息处理的内存分配策略
  - 实现对象池和缓存机制（如需要）
  - 确保重构后性能不低于现有实现的90%

---

## 阶段7：最终整合与部署

### 7.1 CMakeLists和构建系统
- [ ] 更新客户端CMakeLists.txt
  - 添加新增类的编译配置
  - 确保所有依赖库正确链接
  - 保持与现有构建系统的兼容性

- [ ] 👤 重构main.cpp主入口
  - 将现有的ChatClient替换为ProtocolClient
  - 保持命令行参数和使用方式的兼容性
  - 添加新客户端的初始化和配置逻辑

### 7.2 文档和代码清理
- [ ] 👤 代码审查和重构优化
  - 检查所有新增代码的规范性和可读性
  - 确保遵循项目的编码规范和风格
  - 移除已废弃的旧代码和注释

- [ ] 更新相关文档
  - 更新客户端使用说明和配置文档
  - 添加消息类型处理器的扩展指南
  - 完善架构设计和接口说明文档

---

## 关键里程碑

1. **架构验证点**: 阶段1-2完成后，验证基础架构的可行性
2. **功能对等点**: 阶段3-5完成后，确保新客户端功能与原客户端对等
3. **扩展验证点**: 阶段6完成后，验证消息类型扩展机制的有效性
4. **发布就绪点**: 阶段7完成后，新客户端可完全替换原实现

## 风险控制

- **架构风险**: 通过阶段性验证确保架构设计的正确性
- **性能风险**: 关键路径保持与原实现的性能对等
- **兼容性风险**: 保持配置格式和使用接口的向后兼容
- **复杂性风险**: 通过清晰的接口设计控制系统复杂度

---

**总预估任务数**: 31个任务（其中👤人工关键任务8个）
**建议开发顺序**: 按阶段顺序执行，确保每个阶段的输出为下一阶段提供稳固基础
