InotifyHandler 和 EventfdHandler 移植开发计划书
1. 项目概述与目标
1.1 项目背景
当前 zexuan::event 框架已经成功实现了 TimerQueue 和 SignalHandler 组件，采用了统一的架构模式（特殊fd + Channel + EventLoop集成）。为了扩展框架功能，需要移植 InotifyHandler 和 EventfdHandler 两个新组件。

1.2 项目目标
主要目标：参考现有 timer 和 signal 模块设计，实现 InotifyHandler 和 EventfdHandler
架构一致性：采用相同的架构模式，确保与现有框架无缝集成
功能完整性：提供完整的跨线程调用支持、错误处理和资源管理
替换准备：EventfdHandler 为后续替换现有 eventfd 使用做准备
1.3 核心价值
统一文件系统监听能力，支持配置热更新、日志监控等场景
标准化 eventfd 使用，为现有代码重构做准备
保持框架架构的一致性和可维护性
2. 用户画像与场景
2.1 目标用户
框架开发者：需要使用文件监听和事件通知功能的开发人员
应用开发者：基于 zexuan::event 框架开发高性能网络应用的工程师
2.2 使用场景
InotifyHandler 场景：

配置文件热更新监听
日志文件轮转监控
临时文件创建监听
EventfdHandler 场景：

线程间事件通知
异步任务完成通知
替换现有 EventLoop 中的 eventfd 使用
3. 核心功能需求
3.1 InotifyHandler 功能需求
3.1.1 基础功能
文件监听：支持监听单个文件的修改、删除等事件
目录监听：支持监听目录中文件的创建、删除、移动等事件
事件类型：支持 IN_CREATE、IN_DELETE、IN_MODIFY、IN_CLOSE_WRITE 等基础事件
回调机制：提供事件回调函数注册
3.1.2 EventLoop 集成
成员变量：作为 EventLoop 的成员变量
便捷接口：提供类似 watchFile(path, callback) 的便捷接口
跨线程调用：支持从任意线程调用，通过 runInLoop 机制保证线程安全

3.2 EventfdHandler 功能需求
3.2.1 基础功能
事件通知：支持线程间事件通知
计数器语义：支持 eventfd 的计数器特性
非阻塞操作：支持非阻塞读写操作
3.2.2 EventLoop 集成
成员变量：作为 EventLoop 的成员变量
便捷接口：提供 notify() 和 setNotifyCallback() 等接口
替换准备：为替换现有 wakeupFd_ 做准备


RAII 支持：自动管理文件描述符
错误处理：使用 spdlog 详细记录错误信息
优雅清理：析构时自动清理所有监听和资源
跨线程调用：支持从任意线程调用 notify() runinloop()
原子操作：确保事件通知的原子性


EventLoop
├── TimerQueue (existing)
├── SignalHandler (existing)
├── InotifyHandler (new)
└── EventfdHandler (new)


类设计模式
参考现有组件，采用统一的设计模式：

Handler 类：封装系统调用和业务逻辑
Channel 集成：通过 Channel 集成到 EventLoop
回调机制：提供用户回调函数注册
线程安全：通过 runInLoop 保证线程安全