// TCP 相关
TcpServer, TcpClient, TcpConnection, TcpAcceptor, TcpConnector

// Unix Domain Socket 相关  
UdsServer, UdsClient, UdsConnection, UdsAcceptor, UdsConnector

// signalfd - 将信号转换为文件描述符事件
int signalfd = ::signalfd(-1, &mask, SFD_NONBLOCK | SFD_CLOEXEC);
// 可以监听 SIGINT, SIGTERM, SIGUSR1 等

// inotify - 监听文件/目录变化
int inotifyfd = ::inotify_init1(IN_NONBLOCK | IN_CLOEXEC);
// 监听文件创建、修改、删除等

// 子进程退出通知
// 可以用 signalfd 监听 SIGCHLD
// 或者用 pidfd (Linux 5.3+)

// eventfd - 用户态事件通知
int eventfd = ::eventfd(0, EFD_NONBLOCK | EFD_CLOEXEC);
// 线程间通信、异步任务完成通知等

// 命名管道或匿名管道
int pipefd[2];
::pipe2(pipefd, O_NONBLOCK | O_CLOEXEC);

// 串口、GPIO、输入设备等
// 任何可以产生文件描述符的设备