#include "zexuan/protocol/protocol_server/transform/gw104/asdu_type5_gws.hpp"
#include "zexuan/base/message.hpp"

#include <spdlog/spdlog.h>
#include <cstring>

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        int AsduType5GWS::ParseToCommonMessage(const base::ProtocolFrame& frame,
                                               base::CommonMessage& common_msg) {
          spdlog::debug("ASDU Type5: Parsing ProtocolFrame to CommonMessage (Rename Request)");

          // 解析重命名请求
          std::string directory_path;
          int result = parseRenameRequestData(frame.data, directory_path);
          if (result != 0) {
            spdlog::error("ASDU Type5: Failed to parse rename request data");
            return result;
          }

          // 设置CommonMessage
          common_msg.type = base::MessageType::COMMAND;
          common_msg.source_id = frame.frame_id;
          common_msg.target_id = frame.target_addr;  // 使用ProtocolFrame中的target_addr
          common_msg.b_lastmsg = true;

          // 将目录路径作为数据传递
          common_msg.data.assign(directory_path.begin(), directory_path.end());

          spdlog::debug("ASDU Type5: Successfully converted frame to CommonMessage, directory_path={}, target_id={}",
                        directory_path, common_msg.target_id);
          return 0;
        }

        int AsduType5GWS::ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                                   base::ProtocolFrame& frame) {
          spdlog::debug("ASDU Type5: Converting CommonMessage to ProtocolFrame (Rename Response)");

          // 从CommonMessage的数据中提取响应信息
          std::string response_message(common_msg.data.begin(), common_msg.data.end());

          // 判断是否为成功响应（简单的字符串匹配）
          bool success = (response_message.find("SUCCESS") != std::string::npos) ||
                        (response_message.find("RENAME_SUCCESS") != std::string::npos);

          // 构建响应数据
          std::vector<uint8_t> response_data;
          int result = buildRenameResponseData(success, response_message, response_data);
          if (result != 0) {
            spdlog::error("ASDU Type5: Failed to build rename response data");
            return result;
          }

          // 设置ProtocolFrame
          frame.data = response_data;
          frame.type = GetSupportedType();
          frame.frame_id = common_msg.target_id;
          frame.cot = success ? 0x07 : 0x0B;  // 0x07 = 激活确认, 0x0B = 否定确认

          spdlog::debug("ASDU Type5: Successfully converted CommonMessage to frame, success={}, data_size={}",
                        success, frame.data.size());
          return 0;
        }

        int AsduType5GWS::parseRenameRequestData(const std::vector<uint8_t>& data, std::string& directory_path) {
          if (data.empty()) {
            spdlog::error("ASDU Type5: Empty rename request data");
            return -1;
          }

          // 使用Message类正确解析协议帧数据
          base::Message msg;
          size_t parsed = msg.deserialize(data);

          if (parsed == 0) {
            spdlog::error("ASDU Type5: Failed to deserialize message data");
            return -1;
          }

          // 从Message中提取文本内容（目录路径）
          directory_path = msg.getTextContent();

          // 移除尾部的换行符和空白字符
          while (!directory_path.empty() &&
                 (directory_path.back() == '\n' || directory_path.back() == '\r' ||
                  directory_path.back() == ' ' || directory_path.back() == '\t')) {
            directory_path.pop_back();
          }

          if (directory_path.empty()) {
            spdlog::error("ASDU Type5: Empty directory path in rename request");
            return -1;
          }

          spdlog::debug("ASDU Type5: Parsed directory path: '{}'", directory_path);
          return 0;
        }

        int AsduType5GWS::buildRenameResponseData(bool success, const std::string& message, std::vector<uint8_t>& data) {
          // 构建IEC103 ASDU Type5响应数据
          data.clear();

          // ASDU头部
          data.push_back(0x05);  // TYP = 5 (重命名)
          data.push_back(0x81);  // VSQ = 单个信息元素
          data.push_back(success ? 0x07 : 0x0B);  // COT = 激活确认/否定确认
          data.push_back(0x00);  // ADDR低字节
          data.push_back(0x00);  // ADDR高字节

          // 响应状态
          data.push_back(success ? 0x01 : 0x00);  // 成功/失败标志

          // 响应消息长度
          uint16_t msg_len = static_cast<uint16_t>(message.length());
          data.push_back(msg_len & 0xFF);         // 长度低字节
          data.push_back((msg_len >> 8) & 0xFF);  // 长度高字节

          // 响应消息内容
          data.insert(data.end(), message.begin(), message.end());

          spdlog::debug("ASDU Type5: Built response data, success={}, message_len={}, total_size={}",
                        success, msg_len, data.size());
          return 0;
        }

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan