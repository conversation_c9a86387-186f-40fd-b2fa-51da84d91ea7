/**
 * @file type5_handler.cpp
 * @brief Type 5 重命名请求消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type5_handler.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace protocol {
namespace client {

    // 静态常量定义
    const std::string Type5Handler::DEFAULT_RENAME_PATH = "/tmp/files/default_rename_target";

    Type5Handler::Type5Handler() 
        : SingleTypeMessageHandler("Type5Handler", 5) {
        spdlog::debug("Type5Handler: Created for rename request messages");
    }

    bool Type5Handler::ValidateTypeSpecificRequest(const MessageRequest& request) const {
        // Type 5 消息特定的验证逻辑

        // 检查目标地址（重命名请求应该发送给rename_client，地址为10）
        if (request.target != 10 && request.target != 0) {
            spdlog::warn("Type5Handler: Target address {} is not the expected rename_client address (10)", request.target);
        }

        // 检查是否有重命名路径
        if (request.text_content.empty()) {
            spdlog::debug("Type5Handler: No rename path provided, will use default");
        } else {
            // 验证路径格式
            if (!ValidatePath(request.text_content)) {
                spdlog::warn("Type5Handler: Invalid path format: {}", request.text_content);
                return false;
            }
        }

        return true;
    }

    base::Result<base::Message> Type5Handler::CreateTypeSpecificMessage(const MessageRequest& request) const {
        spdlog::debug("Type5Handler: Creating Type 5 rename request message");

        // 创建消息对象
        base::Message message;

        // 使用请求参数，如果未指定则使用默认值
        MessageRequest effective_request = request;
        
        // 设置Type 5的默认值
        if (effective_request.vsq == 0x00) {
            effective_request.vsq = DEFAULT_VSQ;
        }
        if (effective_request.cot == 0x00) {
            effective_request.cot = DEFAULT_COT;
        }
        if (effective_request.fun == 0x00) {
            effective_request.fun = DEFAULT_FUN;
        }
        if (effective_request.inf == 0x00) {
            effective_request.inf = DEFAULT_INF;
        }

        // 设置目标地址（如果未指定）
        if (effective_request.target == 0x00) {
            effective_request.target = DEFAULT_TARGET_ADDRESS;
        }

        // 设置重命名路径
        if (effective_request.text_content.empty()) {
            effective_request.text_content = DEFAULT_RENAME_PATH;
            spdlog::debug("Type5Handler: Using default rename path: {}", DEFAULT_RENAME_PATH);
        }

        // 设置消息字段
        SetBasicMessageFields(message, 5, effective_request);

        spdlog::info("Type5Handler: Created rename request message to target {} for path: {}", 
                    effective_request.target, effective_request.text_content);

        return message;
    }

    base::Result<MessageResult> Type5Handler::ProcessTypeSpecificResponse(const base::Message& response) const {
        spdlog::debug("Type5Handler: Processing Type 5 rename response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();
        
        // 提取响应内容
        std::string response_content = ExtractTextContent(response);
        std::vector<uint8_t> response_data = ExtractVariableData(response);

        MessageResult result;
        result.additional_data = response_data;

        // 分析响应
        if (response_type == 5) {
            result.success = true;
            result.description = ParseRenameResult(response_cot, response_content);
        } else {
            result.success = false;
            result.description = "Unexpected response type " + std::to_string(response_type) + " for rename request";
        }

        return result;
    }

    // 私有方法实现
    bool Type5Handler::ValidatePath(const std::string& path) const {
        if (path.empty()) {
            return false;
        }

        // 检查路径长度
        if (path.length() > 255) {
            return false;
        }

        return true;
    }

    std::string Type5Handler::ParseRenameResult(uint8_t response_cot, const std::string& content) const {
        std::string result;

        switch (response_cot) {
            case 0x07: // 激活确认
                result = "Rename request confirmed";
                break;
            case 0x08: // 停止激活确认
                result = "Rename request stopped";
                break;
            case 0x2D: // 命令传送的否定确认
                result = "Rename request rejected";
                break;
            default:
                result = "Rename response with COT " + std::to_string(response_cot);
                break;
        }

        return result;
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
