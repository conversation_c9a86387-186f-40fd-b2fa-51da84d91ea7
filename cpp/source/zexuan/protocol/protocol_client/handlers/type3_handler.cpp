/**
 * @file type3_handler.cpp
 * @brief Type 3 时间戳事件消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type3_handler.hpp"
#include <spdlog/spdlog.h>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace zexuan {
namespace protocol {
namespace client {

    Type3Handler::Type3Handler() 
        : SingleTypeMessageHandler("Type3Handler", 3) {
        spdlog::debug("Type3Handler: Created for timestamp event messages");
    }

    bool Type3Handler::ValidateTypeSpecificRequest(const MessageRequest& request) const {
        // Type 3 消息特定的验证逻辑

        // 对于Type 3，我们通常不主动发送，而是接收
        // 但如果需要发送，应该包含时间戳数据
        if (!request.variable_data.empty() && request.variable_data.size() != IEC103_TIMESTAMP_SIZE) {
            spdlog::debug("Type3Handler: Invalid timestamp data size - expected {}, got {}", 
                         IEC103_TIMESTAMP_SIZE, request.variable_data.size());
            return false;
        }

        return true;
    }

    base::Result<base::Message> Type3Handler::CreateTypeSpecificMessage(const MessageRequest& request) const {
        spdlog::debug("Type3Handler: Creating Type 3 timestamp event message");

        // 创建消息对象
        base::Message message;

        // 使用请求参数，如果未指定则使用默认值
        MessageRequest effective_request = request;
        
        // 设置Type 3的默认值
        if (effective_request.vsq == 0x00) {
            effective_request.vsq = DEFAULT_VSQ;
        }
        if (effective_request.cot == 0x00) {
            effective_request.cot = DEFAULT_COT;
        }
        if (effective_request.fun == 0x00) {
            effective_request.fun = DEFAULT_FUN;
        }
        if (effective_request.inf == 0x00) {
            effective_request.inf = DEFAULT_INF;
        }

        // 如果没有提供时间戳数据，创建当前时间的时间戳
        if (effective_request.variable_data.empty()) {
            effective_request.variable_data = CreateCurrentIEC103Timestamp();
            effective_request.text_content.clear(); // 清除文本内容，使用二进制时间戳
        }

        // 设置消息字段
        SetBasicMessageFields(message, 3, effective_request);

        spdlog::info("Type3Handler: Created timestamp event message to target {}", request.target);
        spdlog::debug("Type3Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                     effective_request.vsq, effective_request.cot, 
                     effective_request.fun, effective_request.inf);

        return message;
    }

    base::Result<MessageResult> Type3Handler::ProcessTypeSpecificResponse(const base::Message& response) const {
        spdlog::debug("Type3Handler: Processing Type 3 timestamp event");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();
        uint8_t response_inf = response.getInf();
        
        // 打印响应详情
        spdlog::info("Type3Handler: Received timestamp event - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
                    "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                    response.getTyp(), response.getVsq(), response.getCot(),
                    response.getSource(), response.getTarget(), response.getFun(), response.getInf());

        // 提取时间戳数据
        std::vector<uint8_t> timestamp_data = ExtractVariableData(response);

        MessageResult result;
        result.success = true;
        result.additional_data = timestamp_data;

        if (timestamp_data.size() >= IEC103_TIMESTAMP_SIZE) {
            // 解析IEC 60870-5-103时间戳
            auto [timestamp, time_str] = ParseIEC103Timestamp(timestamp_data);
            
            result.description = "Timestamp Event: " + time_str;
            spdlog::info("Type3Handler: {}", result.description);
            spdlog::info("Type3Handler: Event Type: {:02X} (from INF field)", response_inf);

            // 如果有额外的事件数据
            if (timestamp_data.size() > IEC103_TIMESTAMP_SIZE) {
                size_t extra_data_size = timestamp_data.size() - IEC103_TIMESTAMP_SIZE;
                result.description += " (+" + std::to_string(extra_data_size) + " bytes event data)";
                
                spdlog::debug("Type3Handler: Additional Event Data ({} bytes):", extra_data_size);
                std::string hex_data;
                for (size_t i = IEC103_TIMESTAMP_SIZE; i < timestamp_data.size(); ++i) {
                    char buf[4];
                    snprintf(buf, sizeof(buf), "%02X ", timestamp_data[i]);
                    hex_data += buf;
                }
                spdlog::debug("Type3Handler: Extra data: {}", hex_data);
            }
        } else {
            result.description = "Invalid timestamp event (insufficient data)";
            result.success = false;
            spdlog::error("Type3Handler: Invalid timestamp data (size: {}, expected >= {})", 
                         timestamp_data.size(), IEC103_TIMESTAMP_SIZE);
        }

        return result;
    }

    // 私有方法实现
    std::pair<std::chrono::system_clock::time_point, std::string> Type3Handler::ParseIEC103Timestamp(
        const std::vector<uint8_t>& timestamp_data) const {
        
        if (timestamp_data.size() < IEC103_TIMESTAMP_SIZE) {
            return {std::chrono::system_clock::now(), "Invalid timestamp"};
        }

        // 解析IEC 60870-5-103时间格式（7字节）
        uint16_t milliseconds = timestamp_data[0] | (static_cast<uint16_t>(timestamp_data[1]) << 8);
        uint8_t minute = timestamp_data[2];
        uint8_t hour = timestamp_data[3];
        uint8_t day = timestamp_data[4];
        uint8_t month = timestamp_data[5];
        uint8_t year = timestamp_data[6];

        // 格式化时间字符串
        std::string time_str = FormatTimestamp(year, month, day, hour, minute, milliseconds);

        // 创建时间点（简化处理，这里我们主要关注显示）
        std::tm tm = {};
        tm.tm_year = 2000 + year - 1900; // tm_year is years since 1900
        tm.tm_mon = month - 1;            // tm_mon is 0-11
        tm.tm_mday = day;
        tm.tm_hour = hour;
        tm.tm_min = minute;
        tm.tm_sec = milliseconds / 1000;

        auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));
        time_point += std::chrono::milliseconds(milliseconds % 1000);

        return {time_point, time_str};
    }

    std::vector<uint8_t> Type3Handler::CreateCurrentIEC103Timestamp() const {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::tm* tm = std::localtime(&time_t);

        std::vector<uint8_t> timestamp(IEC103_TIMESTAMP_SIZE);
        
        // 毫秒（低字节在前）
        uint16_t ms = static_cast<uint16_t>(milliseconds.count());
        timestamp[0] = ms & 0xFF;
        timestamp[1] = (ms >> 8) & 0xFF;
        
        // 分钟
        timestamp[2] = static_cast<uint8_t>(tm->tm_min);
        
        // 小时
        timestamp[3] = static_cast<uint8_t>(tm->tm_hour);
        
        // 日期
        timestamp[4] = static_cast<uint8_t>(tm->tm_mday);
        
        // 月份
        timestamp[5] = static_cast<uint8_t>(tm->tm_mon + 1);
        
        // 年份（相对于2000年）
        timestamp[6] = static_cast<uint8_t>(tm->tm_year + 1900 - 2000);

        return timestamp;
    }

    std::string Type3Handler::FormatTimestamp(uint8_t year, uint8_t month, uint8_t day, 
                                            uint8_t hour, uint8_t minute, uint16_t milliseconds) const {
        std::ostringstream oss;
        oss << "20" << std::setfill('0') << std::setw(2) << static_cast<int>(year)
            << "-" << std::setfill('0') << std::setw(2) << static_cast<int>(month)
            << "-" << std::setfill('0') << std::setw(2) << static_cast<int>(day)
            << " " << std::setfill('0') << std::setw(2) << static_cast<int>(hour)
            << ":" << std::setfill('0') << std::setw(2) << static_cast<int>(minute)
            << "." << std::setfill('0') << std::setw(3) << static_cast<int>(milliseconds);
        return oss.str();
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
