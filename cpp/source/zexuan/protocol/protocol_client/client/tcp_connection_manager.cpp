/**
 * @file tcp_connection_manager.cpp
 * @brief TCP连接管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/tcp_connection_manager.hpp"
#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>

namespace zexuan {
namespace protocol {
namespace client {

    TcpConnectionManager::TcpConnectionManager(const ClientConfig& config)
        : config_(config) {
        spdlog::debug("TcpConnectionManager: Created with server {}:{}", 
                     config_.server_host, config_.server_port);
    }

    TcpConnectionManager::~TcpConnectionManager() {
        if (IsRunning()) {
            spdlog::warn("TcpConnectionManager: Not stopped before destruction, forcing stop");
            Stop();
        }
        spdlog::debug("TcpConnectionManager: Destroyed");
    }

    bool TcpConnectionManager::Start() {
        if (IsRunning()) {
            spdlog::warn("TcpConnectionManager: Already running");
            return true;
        }

        try {
            spdlog::info("TcpConnectionManager: Starting connection manager");

            // 创建事件循环线程
            event_loop_thread_ = std::make_unique<event::EventLoopThread>();
            event_loop_ = event_loop_thread_->startLoop();
            
            if (!event_loop_) {
                spdlog::error("TcpConnectionManager: Failed to start event loop");
                return false;
            }

            // 在事件循环中注册信号处理
            SetupSignalHandling();

            // 创建TCP客户端
            event::Address server_addr(config_.server_host, config_.server_port);
            tcp_client_ = std::make_unique<event::TcpClient>(event_loop_, server_addr, "ProtocolClient");

            // 设置回调
            tcp_client_->setConnectionCallback(
                std::bind(&TcpConnectionManager::OnConnection, this, std::placeholders::_1));
            tcp_client_->setMessageCallback(
                std::bind(&TcpConnectionManager::OnMessage, this, 
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

            // 启用重试
            if (config_.enable_auto_reconnect) {
                tcp_client_->enableRetry();
            }

            is_running_ = true;
            should_stop_reconnect_ = false;
            UpdateConnectionState(ConnectionState::DISCONNECTED, "Connection manager started");

            spdlog::info("TcpConnectionManager: Started successfully");
            return true;

        } catch (const std::exception& e) {
            spdlog::error("TcpConnectionManager: Exception during start: {}", e.what());
            return false;
        }
    }

    bool TcpConnectionManager::Stop() {
        if (!IsRunning()) {
            return true;
        }

        spdlog::info("TcpConnectionManager: Stopping connection manager");

        // 停止自动重连
        should_stop_reconnect_ = true;
        auto_reconnect_enabled_ = false;

        // 断开连接
        if (IsConnected()) {
            Disconnect();
        }

        // 等待重连线程结束
        if (reconnect_thread_.joinable()) {
            reconnect_thread_.join();
        }

        // 清理TCP客户端
        if (tcp_client_) {
            tcp_client_.reset();
        }

        // 停止事件循环
        if (event_loop_thread_) {
            event_loop_thread_.reset();
            event_loop_ = nullptr;
        }

        is_running_ = false;
        UpdateConnectionState(ConnectionState::DISCONNECTED, "Connection manager stopped");

        spdlog::info("TcpConnectionManager: Stopped successfully");
        return true;
    }

    bool TcpConnectionManager::IsRunning() const {
        return is_running_.load();
    }

    bool TcpConnectionManager::Connect() {
        if (!IsRunning()) {
            spdlog::error("TcpConnectionManager: Cannot connect - manager not running");
            return false;
        }

        if (IsConnected()) {
            spdlog::warn("TcpConnectionManager: Already connected");
            return true;
        }

        try {
            spdlog::info("TcpConnectionManager: Connecting to {}:{}", 
                        config_.server_host, config_.server_port);
            
            UpdateConnectionState(ConnectionState::CONNECTING, "Initiating connection");
            UpdateStatistics([](Statistics& stats) { stats.connection_attempts++; });

            tcp_client_->connect();
            return true;

        } catch (const std::exception& e) {
            spdlog::error("TcpConnectionManager: Exception during connect: {}", e.what());
            UpdateConnectionState(ConnectionState::ERROR, "Connection failed: " + std::string(e.what()));
            return false;
        }
    }

    void TcpConnectionManager::Disconnect() {
        if (!IsConnected()) {
            spdlog::debug("TcpConnectionManager: Already disconnected");
            return;
        }

        spdlog::info("TcpConnectionManager: Disconnecting from server");
        UpdateConnectionState(ConnectionState::DISCONNECTED, "User requested disconnection");

        std::lock_guard<std::mutex> lock(connection_mutex_);
        if (connection_) {
            connection_->shutdown();
            connection_.reset();
        }

        if (tcp_client_) {
            tcp_client_->disconnect();
        }
    }

    bool TcpConnectionManager::IsConnected() const {
        return connection_state_.load() == ConnectionState::CONNECTED;
    }

    ConnectionState TcpConnectionManager::GetConnectionState() const {
        return connection_state_.load();
    }

    bool TcpConnectionManager::SendData(const uint8_t* data, size_t size) {
        if (!IsConnected()) {
            spdlog::error("TcpConnectionManager: Cannot send data - not connected");
            return false;
        }

        std::lock_guard<std::mutex> lock(connection_mutex_);
        if (!connection_) {
            spdlog::error("TcpConnectionManager: No valid connection");
            return false;
        }

        try {
            std::string message(reinterpret_cast<const char*>(data), size);
            connection_->send(message);
            
            UpdateStatistics([size](Statistics& stats) { stats.bytes_sent += size; });
            
            spdlog::debug("TcpConnectionManager: Sent {} bytes", size);
            return true;

        } catch (const std::exception& e) {
            spdlog::error("TcpConnectionManager: Exception during send: {}", e.what());
            return false;
        }
    }

    bool TcpConnectionManager::SendMessage(const std::string& message) {
        return SendData(reinterpret_cast<const uint8_t*>(message.data()), message.size());
    }

    void TcpConnectionManager::SetConnectionEventCallback(ConnectionEventCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        connection_event_callback_ = callback;
    }

    void TcpConnectionManager::SetDataReceivedCallback(DataReceivedCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        data_received_callback_ = callback;
    }

    TcpConnectionManager::Statistics TcpConnectionManager::GetStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    // 私有方法实现
    void TcpConnectionManager::OnConnection(const event::TcpConnectionPtr& conn) {
        spdlog::info("TcpConnectionManager: Connection state changed: {} -> {} is {}",
                    conn->localAddress().toIpPort(),
                    conn->peerAddress().toIpPort(),
                    conn->connected() ? "UP" : "DOWN");

        std::lock_guard<std::mutex> lock(connection_mutex_);
        if (conn->connected()) {
            connection_ = conn;
            UpdateConnectionState(ConnectionState::CONNECTED, "Connection established");
        } else {
            connection_.reset();
            
            // 如果启用了自动重连且不是用户主动断开
            if (config_.enable_auto_reconnect && auto_reconnect_enabled_ && IsRunning()) {
                UpdateConnectionState(ConnectionState::RECONNECTING, "Connection lost, will retry");
                
                // 启动自动重连线程（如果尚未启动）
                if (!reconnect_thread_.joinable()) {
                    reconnect_thread_ = std::thread(&TcpConnectionManager::AutoReconnectLoop, this);
                }
            } else {
                UpdateConnectionState(ConnectionState::DISCONNECTED, "Connection closed");
            }
        }
    }

    void TcpConnectionManager::OnMessage(const event::TcpConnectionPtr& conn,
                                       event::Buffer* buf,
                                       std::chrono::system_clock::time_point receive_time) {
        size_t readable_bytes = buf->readableBytes();
        if (readable_bytes == 0) {
            return;
        }

        spdlog::debug("TcpConnectionManager: Received {} bytes", readable_bytes);

        // 获取数据
        const uint8_t* data = reinterpret_cast<const uint8_t*>(buf->peek());
        
        // 调用数据接收回调
        {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            if (data_received_callback_) {
                try {
                    data_received_callback_(data, readable_bytes);
                } catch (const std::exception& e) {
                    spdlog::error("TcpConnectionManager: Exception in data received callback: {}", e.what());
                }
            }
        }

        // 更新统计信息
        UpdateStatistics([readable_bytes](Statistics& stats) { 
            stats.bytes_received += readable_bytes; 
        });

        // 消费掉已读取的数据
        buf->retrieveAll();
    }

    void TcpConnectionManager::UpdateConnectionState(ConnectionState new_state, const std::string& description) {
        ConnectionState old_state = connection_state_.exchange(new_state);
        if (old_state != new_state) {
            spdlog::info("TcpConnectionManager: State changed: {} -> {}, {}", 
                        static_cast<int>(old_state), static_cast<int>(new_state), description);

            // 调用连接事件回调
            std::lock_guard<std::mutex> lock(callback_mutex_);
            if (connection_event_callback_) {
                try {
                    connection_event_callback_(new_state, description);
                } catch (const std::exception& e) {
                    spdlog::error("TcpConnectionManager: Exception in connection event callback: {}", e.what());
                }
            }
        }
    }

    void TcpConnectionManager::AutoReconnectLoop() {
        spdlog::debug("TcpConnectionManager: Auto-reconnect thread started");

        uint32_t attempt = 0;
        while (!should_stop_reconnect_ && IsRunning() && auto_reconnect_enabled_) {
            if (IsConnected()) {
                // 已经连接，退出重连循环
                break;
            }

            attempt++;
            if (attempt > config_.max_reconnect_attempts) {
                spdlog::error("TcpConnectionManager: Max reconnect attempts ({}) reached", 
                            config_.max_reconnect_attempts);
                UpdateConnectionState(ConnectionState::ERROR, "Max reconnect attempts reached");
                break;
            }

            spdlog::info("TcpConnectionManager: Reconnect attempt {}/{}", 
                        attempt, config_.max_reconnect_attempts);
            
            UpdateStatistics([](Statistics& stats) { stats.reconnection_count++; });

            // 尝试重连
            if (Connect()) {
                // 等待连接结果
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                if (IsConnected()) {
                    spdlog::info("TcpConnectionManager: Reconnection successful");
                    break;
                }
            }

            // 等待重连间隔
            auto sleep_duration = std::chrono::milliseconds(config_.reconnect_interval_ms);
            auto start_time = std::chrono::steady_clock::now();
            
            while (!should_stop_reconnect_ && 
                   std::chrono::steady_clock::now() - start_time < sleep_duration) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        spdlog::debug("TcpConnectionManager: Auto-reconnect thread stopped");
    }

    void TcpConnectionManager::UpdateStatistics(const std::function<void(Statistics&)>& updater) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        updater(statistics_);
    }

    void TcpConnectionManager::SetupSignalHandling() {
        if (!event_loop_) {
            return;
        }

        // 注册SIGINT和SIGTERM信号处理
        // 这里使用事件循环的runAfter方法来定期检查是否需要退出
        // 或者使用EventLoop的quit()方法来优雅退出
        spdlog::debug("TcpConnectionManager: Signal handling setup (handled by event loop)");
        
        // 注意：实际的信号处理应该在应用层面实现
        // 这里只是确保连接管理器能够响应优雅关闭请求
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
