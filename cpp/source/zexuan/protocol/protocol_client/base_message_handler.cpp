/**
 * @file base_message_handler.cpp
 * @brief 基础消息处理器抽象类实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/base_message_handler.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace protocol {
namespace client {

    // BaseMessageHandler 实现
    BaseMessageHandler::BaseMessageHandler(const std::string& handler_name, 
                                         const std::vector<uint8_t>& supported_types)
        : handler_name_(handler_name), supported_types_(supported_types) {
        
        if (handler_name_.empty()) {
            handler_name_ = "UnnamedHandler";
        }
        
        if (supported_types_.empty()) {
            spdlog::warn("{}: No supported message types specified", handler_name_);
        }
        
        spdlog::debug("{}: Created with {} supported types", handler_name_, supported_types_.size());
    }

    bool BaseMessageHandler::CanHandle(uint8_t message_type) const {
        return IsMessageTypeSupported(message_type);
    }

    std::string BaseMessageHandler::GetHandlerName() const {
        return handler_name_;
    }

    std::vector<uint8_t> BaseMessageHandler::GetSupportedTypes() const {
        return supported_types_;
    }

    bool BaseMessageHandler::ValidateRequest(uint8_t message_type, const MessageRequest& request) const {
        // 检查消息类型是否支持
        if (!IsMessageTypeSupported(message_type)) {
            spdlog::debug("{}: Message type {} not supported", handler_name_, message_type);
            return false;
        }

        // 验证基本请求参数
        return ValidateBasicRequest(request);
    }

    // 受保护的方法实现
    bool BaseMessageHandler::ValidateBasicRequest(const MessageRequest& request) const {
        // 验证基本字段的有效性
        
        // VSQ (可变结构限定词) 通常应该有有效值
        if (request.vsq == 0) {
            spdlog::debug("{}: Invalid VSQ value (0)", handler_name_);
            return false;
        }

        // COT (传送原因) 应该在有效范围内
        if (request.cot == 0 || request.cot > 0xFF) {
            spdlog::debug("{}: Invalid COT value ({})", handler_name_, request.cot);
            return false;
        }

        // 源地址和目标地址应该不同（通常情况下）
        if (request.source == request.target && request.source != 0) {
            spdlog::debug("{}: Source and target addresses are the same ({})", 
                         handler_name_, request.source);
            // 这不是严格的错误，只是警告
        }

        return true;
    }

    void BaseMessageHandler::SetBasicMessageFields(base::Message& message, 
                                                  uint8_t message_type,
                                                  const MessageRequest& request) const {
        message.setTyp(message_type);
        message.setVsq(request.vsq);
        message.setCot(request.cot);
        message.setSource(request.source);
        message.setTarget(request.target);
        message.setFun(request.fun);
        message.setInf(request.inf);

        // 设置可变结构体数据
        if (!request.variable_data.empty()) {
            message.setVariableStructure(request.variable_data);
        } else if (!request.text_content.empty()) {
            message.setTextContent(request.text_content);
        }

        spdlog::debug("{}: Set basic message fields: TYP={:02X}, VSQ={:02X}, COT={:02X}, "
                     "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                     handler_name_, message_type, request.vsq, request.cot,
                     request.source, request.target, request.fun, request.inf);
    }

    MessageResult BaseMessageHandler::CreateBasicResult(bool success, const std::string& description) const {
        MessageResult result;
        result.success = success;
        result.description = description.empty() ? 
            (success ? "Operation completed successfully" : "Operation failed") : description;
        return result;
    }

    std::string BaseMessageHandler::ExtractTextContent(const base::Message& message) const {
        return message.getTextContent();
    }

    std::vector<uint8_t> BaseMessageHandler::ExtractVariableData(const base::Message& message) const {
        return message.getVariableStructure();
    }

    bool BaseMessageHandler::IsMessageTypeSupported(uint8_t message_type) const {
        return std::find(supported_types_.begin(), supported_types_.end(), message_type) != supported_types_.end();
    }

    // SingleTypeMessageHandler 实现
    SingleTypeMessageHandler::SingleTypeMessageHandler(const std::string& handler_name, uint8_t message_type)
        : BaseMessageHandler(handler_name, {message_type}), message_type_(message_type) {
        
        spdlog::debug("{}: Created for single message type {}", handler_name, message_type);
    }

    bool SingleTypeMessageHandler::ValidateRequest(uint8_t message_type, const MessageRequest& request) const {
        // 首先检查消息类型是否匹配
        if (message_type != message_type_) {
            spdlog::debug("{}: Message type mismatch - expected {}, got {}", 
                         GetHandlerName(), message_type_, message_type);
            return false;
        }

        // 验证基本请求
        if (!ValidateBasicRequest(request)) {
            return false;
        }

        // 调用特定类型的验证
        return ValidateTypeSpecificRequest(request);
    }

    base::Result<base::Message> SingleTypeMessageHandler::CreateMessage(uint8_t message_type, 
                                                                       const MessageRequest& request) const {
        // 检查消息类型
        if (message_type != message_type_) {
            spdlog::error("{}: Cannot create message type {} - only supports type {}", 
                         GetHandlerName(), message_type, message_type_);
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        // 调用特定类型的创建方法
        return CreateTypeSpecificMessage(request);
    }

    base::Result<MessageResult> SingleTypeMessageHandler::ProcessResponse(const base::Message& response) const {
        // 检查响应消息类型
        uint8_t response_type = response.getTyp();
        if (response_type != message_type_) {
            spdlog::debug("{}: Response type {} does not match expected type {}", 
                         GetHandlerName(), response_type, message_type_);
            // 对于响应，我们可能需要更宽松的处理，因为响应类型可能与请求类型不同
            // 这里我们仍然尝试处理，但记录警告
        }

        // 调用特定类型的处理方法
        return ProcessTypeSpecificResponse(response);
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
