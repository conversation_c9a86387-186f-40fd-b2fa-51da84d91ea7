/**
 * @file protocol_client.cpp
 * @brief 协议客户端配置实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/protocol_client.hpp"
#include <nlohmann/json.hpp>
#include <fstream>
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    bool ClientConfig::LoadFromFile(const std::string& config_file_path) {
        try {
            std::ifstream config_file(config_file_path);
            if (!config_file.is_open()) {
                spdlog::error("ClientConfig: Cannot open config file: {}", config_file_path);
                return false;
            }

            nlohmann::json config;
            config_file >> config;

            // 检查必需的配置节
            if (!config.contains("protocol") || !config["protocol"].contains("client")) {
                spdlog::error("ClientConfig: Missing protocol.client section in config file: {}", config_file_path);
                return false;
            }

            auto client_config = config["protocol"]["client"];

            // 读取必需的配置项
            if (!client_config.contains("server_host")) {
                spdlog::error("ClientConfig: Missing required config: protocol.client.server_host");
                return false;
            }
            if (!client_config.contains("server_port")) {
                spdlog::error("ClientConfig: Missing required config: protocol.client.server_port");
                return false;
            }

            server_host = client_config["server_host"];
            server_port = client_config["server_port"];

            // 读取可选配置项（使用默认值）
            if (client_config.contains("connect_timeout_ms")) {
                connect_timeout_ms = client_config["connect_timeout_ms"];
            }
            if (client_config.contains("reconnect_interval_ms")) {
                reconnect_interval_ms = client_config["reconnect_interval_ms"];
            }
            if (client_config.contains("max_reconnect_attempts")) {
                max_reconnect_attempts = client_config["max_reconnect_attempts"];
            }
            if (client_config.contains("enable_auto_reconnect")) {
                enable_auto_reconnect = client_config["enable_auto_reconnect"];
            }
            if (client_config.contains("send_timeout_ms")) {
                send_timeout_ms = client_config["send_timeout_ms"];
            }

            spdlog::info("ClientConfig: Loaded configuration - server: {}:{}, timeout: {}ms, auto_reconnect: {}", 
                        server_host, server_port, connect_timeout_ms, enable_auto_reconnect);
            return true;

        } catch (const std::exception& e) {
            spdlog::error("ClientConfig: Failed to load config from {}: {}", config_file_path, e.what());
            return false;
        }
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
