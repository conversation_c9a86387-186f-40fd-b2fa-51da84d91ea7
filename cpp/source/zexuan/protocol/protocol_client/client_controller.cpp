/**
 * @file client_controller.cpp
 * @brief 协议客户端控制器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client_controller.hpp"
#include "zexuan/protocol/protocol_client/client/protocol_client_impl.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    ClientController::ClientController(base::ObjectId object_id, 
                                     std::shared_ptr<base::Mediator> mediator,
                                     ProtocolClientImpl* client)
        : Controller(object_id, mediator), client_(client) {
        spdlog::debug("{}: ClientController created with ID: {}", 
                     client_ ? client_->GetComponentName() : "UnknownClient", object_id);
    }

    ClientController::~ClientController() noexcept {
        spdlog::debug("{}: ClientController destroyed", 
                     client_ ? client_->GetComponentName() : "UnknownClient");
    }

    base::Result<void> ClientController::ReplyResult(const base::CommonMessage& result_msg, 
                                                    base::ObjectId source_id) {
        if (!client_) {
            spdlog::error("ClientController: Client instance is null");
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("{}: Received result message from source ID: {}, type: {}", 
                     client_->GetComponentName(), source_id, static_cast<int>(result_msg.type));

        // 目前客户端主要是主动发送消息，不需要处理外部结果
        // 如果后续有需要，可以在这里添加结果处理逻辑
        
        return base::Result<void>{};
    }

    base::Result<void> ClientController::PushEventNotify(const base::EventMessage& event_msg, 
                                                        base::ObjectId source_id) {
        if (!client_) {
            spdlog::error("ClientController: Client instance is null");
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("{}: Received event notification from source ID: {}, event_type: {}", 
                     client_->GetComponentName(), source_id, event_msg.event_type);

        // 目前客户端主要是主动发送消息，接收响应
        // 事件通知可能来自于服务端的主动推送，暂时记录日志即可
        spdlog::info("{}: Received event: type={}, description={}", 
                    client_->GetComponentName(), event_msg.event_type, event_msg.description);

        return base::Result<void>{};
    }

    base::VoidResult ClientController::DoRegister() {
        if (!client_) {
            spdlog::error("ClientController: Client instance is null during registration");
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("{}: ClientController registering", client_->GetComponentName());

        // 设置关注的事件类型（如果需要）
        // 目前客户端不需要特别关注特定事件类型
        
        return base::VoidResult{};
    }

    base::VoidResult ClientController::DoUnregister() noexcept {
        if (!client_) {
            return base::VoidResult{};
        }

        spdlog::debug("{}: ClientController unregistering", client_->GetComponentName());

        // 清理资源（如果有）
        
        return base::VoidResult{};
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
