/**
 * @file message_frame_parser.cpp
 * @brief IEC 60870-5-103 消息帧解析器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/message_frame_parser.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace protocol {
namespace client {

    MessageFrameParser::MessageFrameParser() {
        spdlog::debug("MessageFrameParser: Created");
    }

    MessageFrameParser::~MessageFrameParser() {
        spdlog::debug("MessageFrameParser: Destroyed");
    }

    void MessageFrameParser::ProcessData(const uint8_t* data, size_t size) {
        if (!data || size == 0) {
            return;
        }

        std::lock_guard<std::mutex> lock(buffer_mutex_);
        
        // 添加新数据到接收缓冲区
        receive_buffer_.insert(receive_buffer_.end(), data, data + size);
        
        spdlog::debug("MessageFrameParser: Received {} bytes, buffer total: {} bytes", 
                     size, receive_buffer_.size());
        
        UpdateStatistics([size](Statistics& stats) {
            stats.bytes_processed += size;
            stats.buffer_size = stats.buffer_size; // 将在下面更新
        });

        // 处理所有可能的完整帧
        while (true) {
            auto complete_frame = ExtractCompleteFrame();
            if (complete_frame.empty()) {
                break; // 没有完整帧
            }

            ProcessCompleteFrame(complete_frame);
            UpdateStatistics([](Statistics& stats) {
                stats.frames_processed++;
            });
        }

        // 更新缓冲区大小统计
        UpdateStatistics([this](Statistics& stats) {
            stats.buffer_size = receive_buffer_.size();
        });
    }

    void MessageFrameParser::SetCompleteFrameCallback(CompleteFrameCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        complete_frame_callback_ = callback;
    }

    void MessageFrameParser::SetRawFrameCallback(RawFrameCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        raw_frame_callback_ = callback;
    }

    void MessageFrameParser::ClearBuffer() {
        std::lock_guard<std::mutex> lock(buffer_mutex_);
        receive_buffer_.clear();
        spdlog::debug("MessageFrameParser: Buffer cleared");
    }

    MessageFrameParser::Statistics MessageFrameParser::GetStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    // 私有方法实现
    std::vector<uint8_t> MessageFrameParser::ExtractCompleteFrame() {
        // 注意：调用此方法时buffer_mutex_已被锁定

        if (receive_buffer_.size() < MIN_FRAME_SIZE) {
            return {}; // 至少需要帧头
        }

        // 查找帧起始符 0x68
        auto start_it = std::find(receive_buffer_.begin(), receive_buffer_.end(), START_CHAR);
        if (start_it == receive_buffer_.end()) {
            // 没有找到起始符，清空缓冲区
            size_t skip_size = receive_buffer_.size();
            receive_buffer_.clear();
            spdlog::debug("MessageFrameParser: No frame start found, clearing {} bytes", skip_size);
            UpdateStatistics([](Statistics& stats) {
                stats.parse_errors++;
            });
            return {};
        }

        // 删除起始符之前的垃圾数据
        if (start_it != receive_buffer_.begin()) {
            size_t skip = start_it - receive_buffer_.begin();
            receive_buffer_.erase(receive_buffer_.begin(), start_it);
            spdlog::debug("MessageFrameParser: Skipped {} bytes of garbage data", skip);
        }

        if (receive_buffer_.size() < MIN_FRAME_SIZE) {
            return {}; // 等待更多数据
        }

        // 解析帧长度 (IEC 60870-5-103格式：0x68 + 长度低字节 + 长度高字节)
        uint16_t asdu_len = receive_buffer_[1] | (static_cast<uint16_t>(receive_buffer_[2]) << 8);
        size_t total_frame_len = MIN_FRAME_SIZE + asdu_len;

        // 帧长度合法性检查
        if (asdu_len > MAX_ASDU_LENGTH) {
            receive_buffer_.erase(receive_buffer_.begin()); // 删除错误的起始符
            spdlog::debug("MessageFrameParser: Invalid frame length {}, skipping", asdu_len);
            UpdateStatistics([](Statistics& stats) {
                stats.invalid_frames++;
            });
            return {};
        }

        // 检查是否有完整帧
        if (receive_buffer_.size() < total_frame_len) {
            spdlog::debug("MessageFrameParser: Incomplete frame, need {} bytes, have {} bytes", 
                         total_frame_len, receive_buffer_.size());
            return {}; // 等待更多数据
        }

        // 提取完整帧
        std::vector<uint8_t> complete_frame(receive_buffer_.begin(), 
                                          receive_buffer_.begin() + total_frame_len);
        receive_buffer_.erase(receive_buffer_.begin(), receive_buffer_.begin() + total_frame_len);

        spdlog::debug("MessageFrameParser: Extracted complete frame of {} bytes", total_frame_len);
        return complete_frame;
    }

    void MessageFrameParser::ProcessCompleteFrame(const std::vector<uint8_t>& frame_data) {
        spdlog::debug("MessageFrameParser: Processing complete frame: {} bytes", frame_data.size());

        // 打印帧数据（调试用）
        if (spdlog::get_level() <= spdlog::level::debug) {
            std::string hex_str;
            for (uint8_t byte : frame_data) {
                char buf[8];
                snprintf(buf, sizeof(buf), "%02X ", byte);
                hex_str += buf;
            }
            spdlog::debug("MessageFrameParser: Frame data: {}", hex_str);
        }

        // 验证帧格式
        if (!ValidateFrame(frame_data)) {
            spdlog::warn("MessageFrameParser: Invalid frame format");
            CallRawFrameCallback(frame_data);
            UpdateStatistics([](Statistics& stats) {
                stats.invalid_frames++;
            });
            return;
        }

        // 尝试解析为 IEC 103 消息
        base::Message message;
        size_t parsed = message.deserialize(frame_data);

        if (parsed > 0) {
            spdlog::debug("MessageFrameParser: Parsed IEC103 message: TYP={:02X}, VSQ={:02X}, COT={:02X}, "
                         "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                         message.getTyp(), message.getVsq(), message.getCot(),
                         message.getSource(), message.getTarget(), message.getFun(), message.getInf());

            CallCompleteFrameCallback(message);
        } else {
            spdlog::warn("MessageFrameParser: Failed to parse message, treating as raw frame");
            CallRawFrameCallback(frame_data);
            UpdateStatistics([](Statistics& stats) {
                stats.parse_errors++;
            });
        }
    }

    bool MessageFrameParser::ValidateFrame(const std::vector<uint8_t>& frame_data) const {
        // 基本长度检查
        if (frame_data.size() < MIN_FRAME_SIZE) {
            return false;
        }

        // 起始符检查
        if (frame_data[0] != START_CHAR) {
            return false;
        }

        // 长度一致性检查
        uint16_t declared_length = frame_data[1] | (static_cast<uint16_t>(frame_data[2]) << 8);
        size_t expected_frame_size = MIN_FRAME_SIZE + declared_length;
        
        if (frame_data.size() != expected_frame_size) {
            spdlog::debug("MessageFrameParser: Frame size mismatch - expected {}, got {}", 
                         expected_frame_size, frame_data.size());
            return false;
        }

        return true;
    }

    void MessageFrameParser::UpdateStatistics(const std::function<void(Statistics&)>& updater) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        updater(statistics_);
        statistics_.buffer_size = receive_buffer_.size(); // 总是更新当前缓冲区大小
    }

    void MessageFrameParser::CallCompleteFrameCallback(const base::Message& message) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (complete_frame_callback_) {
            try {
                complete_frame_callback_(message);
            } catch (const std::exception& e) {
                spdlog::error("MessageFrameParser: Exception in complete frame callback: {}", e.what());
            }
        }
    }

    void MessageFrameParser::CallRawFrameCallback(const std::vector<uint8_t>& frame_data) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (raw_frame_callback_) {
            try {
                raw_frame_callback_(frame_data);
            } catch (const std::exception& e) {
                spdlog::error("MessageFrameParser: Exception in raw frame callback: {}", e.what());
            }
        }
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
