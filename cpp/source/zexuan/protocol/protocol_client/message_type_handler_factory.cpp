/**
 * @file message_type_handler_factory.cpp
 * @brief 消息类型处理器工厂实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/message_type_handler_factory.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace protocol {
namespace client {

    MessageTypeHandlerFactory::MessageTypeHandlerFactory() {
        spdlog::debug("MessageTypeHandlerFactory: Created");
    }

    MessageTypeHandlerFactory::~MessageTypeHandlerFactory() {
        spdlog::debug("MessageTypeHandlerFactory: Destroyed");
    }

    bool MessageTypeHandlerFactory::RegisterHandler(std::unique_ptr<IMessageTypeHandler> handler) {
        if (!handler) {
            spdlog::error("MessageTypeHandlerFactory: Cannot register null handler");
            return false;
        }

        auto supported_types = handler->GetSupportedTypes();
        if (supported_types.empty()) {
            spdlog::error("MessageTypeHandlerFactory: Handler {} supports no message types", 
                         handler->GetHandlerName());
            return false;
        }

        std::lock_guard<std::mutex> lock(handlers_mutex_);
        
        // 检查是否有冲突的消息类型
        for (uint8_t type : supported_types) {
            if (handlers_.find(type) != handlers_.end()) {
                spdlog::error("MessageTypeHandlerFactory: Message type {} already registered", type);
                return false;
            }
        }

        // 为每个支持的消息类型创建处理器的副本（或共享）
        // 由于每个处理器可能支持多种类型，我们需要特殊处理
        std::string handler_name = handler->GetHandlerName();
        
        // 注册第一个类型使用原始处理器
        uint8_t primary_type = supported_types[0];
        handlers_[primary_type] = std::move(handler);
        
        // 对于其他类型，我们需要创建引用到同一个处理器
        // 这里我们采用简化策略：每个处理器只处理一种主要类型
        // 如果需要支持多种类型，可以注册多个处理器实例
        
        UpdateStatistics([&supported_types](Statistics& stats) {
            stats.registered_handlers += static_cast<uint32_t>(supported_types.size());
        });

        spdlog::info("MessageTypeHandlerFactory: Registered handler '{}' for {} types", 
                    handler_name, supported_types.size());
        
        // 打印支持的类型
        for (uint8_t type : supported_types) {
            spdlog::debug("MessageTypeHandlerFactory: Handler '{}' supports type {}", 
                         handler_name, type);
        }

        return true;
    }

    bool MessageTypeHandlerFactory::UnregisterHandler(uint8_t message_type) {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        
        auto it = handlers_.find(message_type);
        if (it == handlers_.end()) {
            spdlog::warn("MessageTypeHandlerFactory: No handler registered for message type {}", 
                        message_type);
            return false;
        }

        std::string handler_name = it->second->GetHandlerName();
        handlers_.erase(it);
        
        UpdateStatistics([](Statistics& stats) {
            if (stats.registered_handlers > 0) {
                stats.registered_handlers--;
            }
        });

        spdlog::info("MessageTypeHandlerFactory: Unregistered handler '{}' for type {}", 
                    handler_name, message_type);
        return true;
    }

    bool MessageTypeHandlerFactory::SupportsMessageType(uint8_t message_type) const {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        return handlers_.find(message_type) != handlers_.end();
    }

    IMessageTypeHandler* MessageTypeHandlerFactory::GetHandler(uint8_t message_type) const {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        
        auto it = handlers_.find(message_type);
        if (it != handlers_.end()) {
            return it->second.get();
        }
        return nullptr;
    }

    std::vector<uint8_t> MessageTypeHandlerFactory::GetSupportedTypes() const {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        
        std::vector<uint8_t> types;
        types.reserve(handlers_.size());
        
        for (const auto& pair : handlers_) {
            types.push_back(pair.first);
        }
        
        std::sort(types.begin(), types.end());
        return types;
    }

    base::Result<base::Message> MessageTypeHandlerFactory::CreateMessage(
        uint8_t message_type, const MessageRequest& request) const {
        
        if (!IsValidMessageType(message_type)) {
            spdlog::error("MessageTypeHandlerFactory: Invalid message type {}", message_type);
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        IMessageTypeHandler* handler = GetHandler(message_type);
        if (!handler) {
            spdlog::error("MessageTypeHandlerFactory: No handler for message type {}", message_type);
            return std::unexpected(base::ErrorCode::OBJECT_NOT_FOUND);
        }

        if (!handler->ValidateRequest(message_type, request)) {
            spdlog::error("MessageTypeHandlerFactory: Request validation failed for message type {}", 
                         message_type);
            UpdateStatistics([](Statistics& stats) {
                stats.validation_failures++;
            });
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        auto result = handler->CreateMessage(message_type, request);
        if (result) {
            UpdateStatistics([](Statistics& stats) {
                stats.messages_created++;
            });
            spdlog::debug("MessageTypeHandlerFactory: Created message type {} using handler '{}'", 
                         message_type, handler->GetHandlerName());
        } else {
            spdlog::error("MessageTypeHandlerFactory: Failed to create message type {} with handler '{}'", 
                         message_type, handler->GetHandlerName());
        }

        return result;
    }

    base::Result<MessageResult> MessageTypeHandlerFactory::ProcessResponse(const base::Message& message) const {
        uint8_t message_type = message.getTyp();
        
        IMessageTypeHandler* handler = GetHandler(message_type);
        if (!handler) {
            spdlog::warn("MessageTypeHandlerFactory: No handler for response message type {}", 
                        message_type);
            // 返回一个默认的成功结果
            MessageResult result(true, "No specific handler, treating as successful");
            return result;
        }

        auto result = handler->ProcessResponse(message);
        if (result) {
            UpdateStatistics([](Statistics& stats) {
                stats.responses_processed++;
            });
            spdlog::debug("MessageTypeHandlerFactory: Processed response type {} using handler '{}'", 
                         message_type, handler->GetHandlerName());
        } else {
            spdlog::error("MessageTypeHandlerFactory: Failed to process response type {} with handler '{}'", 
                         message_type, handler->GetHandlerName());
        }

        return result;
    }

    bool MessageTypeHandlerFactory::ValidateRequest(uint8_t message_type, const MessageRequest& request) const {
        if (!IsValidMessageType(message_type)) {
            return false;
        }

        IMessageTypeHandler* handler = GetHandler(message_type);
        if (!handler) {
            spdlog::warn("MessageTypeHandlerFactory: No handler for message type {} validation", 
                        message_type);
            return false;
        }

        bool result = handler->ValidateRequest(message_type, request);
        if (!result) {
            UpdateStatistics([](Statistics& stats) {
                stats.validation_failures++;
            });
        }

        return result;
    }

    MessageTypeHandlerFactory::Statistics MessageTypeHandlerFactory::GetStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    void MessageTypeHandlerFactory::ClearAllHandlers() {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        
        size_t count = handlers_.size();
        handlers_.clear();
        
        UpdateStatistics([](Statistics& stats) {
            stats.registered_handlers = 0;
        });

        spdlog::info("MessageTypeHandlerFactory: Cleared {} handlers", count);
    }

    bool MessageTypeHandlerFactory::RegisterBuiltinHandlers() {
        spdlog::info("MessageTypeHandlerFactory: Registering builtin handlers");
        
        // TODO: 在后续实现中注册内置的Type1-5处理器
        // 现在先返回true，表示成功（即使没有注册任何处理器）
        
        spdlog::debug("MessageTypeHandlerFactory: Builtin handlers registration placeholder");
        return true;
    }

    // 私有方法实现
    void MessageTypeHandlerFactory::UpdateStatistics(const std::function<void(Statistics&)>& updater) const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        updater(statistics_);
    }

    bool MessageTypeHandlerFactory::IsValidMessageType(uint8_t message_type) const {
        // IEC 60870-5-103 协议中，消息类型范围是 1-255
        // 类型 0 通常保留或用作无效类型
        return message_type >= 1 && message_type <= 255;
    }

    bool MessageTypeHandlerFactory::HasHandler(uint8_t message_type) const {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        return handlers_.find(message_type) != handlers_.end();
    }

    size_t MessageTypeHandlerFactory::GetRegisteredTypesCount() const {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        return handlers_.size();
    }

    // 全局工厂实例
    MessageTypeHandlerFactory& GetGlobalHandlerFactory() {
        static MessageTypeHandlerFactory instance;
        return instance;
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
