/**
 * @file client_executor.cpp
 * @brief 协议客户端执行器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client_executor.hpp"
#include "zexuan/protocol/protocol_client/client/protocol_client_impl.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    ClientExecutor::ClientExecutor(base::ObjectId object_id, 
                                  std::shared_ptr<base::Mediator> mediator,
                                  ProtocolClientImpl* client)
        : Executor(object_id, mediator), client_(client) {
        spdlog::debug("{}: ClientExecutor created with ID: {}", 
                     client_ ? client_->GetComponentName() : "UnknownClient", object_id);
    }

    ClientExecutor::~ClientExecutor() noexcept {
        spdlog::debug("{}: ClientExecutor destroyed", 
                     client_ ? client_->GetComponentName() : "UnknownClient");
    }

    base::Result<void> ClientExecutor::PushCommand(const base::CommonMessage& command_msg, 
                                                  base::ObjectId source_id) {
        if (!client_) {
            spdlog::error("ClientExecutor: Client instance is null");
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("{}: Received command from source ID: {}, type: {}", 
                     client_->GetComponentName(), source_id, static_cast<int>(command_msg.type));

        // 目前客户端主要是主动发送消息，不需要处理外部命令
        // 如果后续有需要（比如外部要求发送特定消息），可以在这里添加命令处理逻辑
        spdlog::info("{}: Received command: type={}, data_size={}", 
                    client_->GetComponentName(), static_cast<int>(command_msg.type), command_msg.data.size());

        // 暂时只记录接收到的命令
        return base::Result<void>{};
    }

    base::VoidResult ClientExecutor::DoRegister() {
        if (!client_) {
            spdlog::error("ClientExecutor: Client instance is null during registration");
            return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("{}: ClientExecutor registering", client_->GetComponentName());

        // 设置管理的设备（如果需要）
        // 目前客户端不管理具体设备，主要是作为协议客户端存在
        
        return base::VoidResult{};
    }

    base::VoidResult ClientExecutor::DoUnregister() noexcept {
        if (!client_) {
            return base::VoidResult{};
        }

        spdlog::debug("{}: ClientExecutor unregistering", client_->GetComponentName());

        // 清理资源（如果有）
        
        return base::VoidResult{};
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
