/********************************************************************
Dragon 2015-04-10 Beijing.
*********************************************************************/


#include "ZxMsg104SerialNum.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CXJMsg104SerialNum::CXJMsg104SerialNum()
: m_iSendSerial(0) , m_iSendACKNum(0) , m_iReceiveSerial(0) ,
 m_iReceiveACKNum(0),m_Lock("CXJMsg104SerialNum")
{
	time(&m_tLastTimeOfReceiveInf);
}

CXJMsg104SerialNum::~CXJMsg104SerialNum()
{
}

int CXJMsg104SerialNum::GetSendSerial()
{
	CLockUp locallock(&m_Lock);
	return m_iSendSerial;
}

void CXJMsg104SerialNum::SetSendSerial(int val)
{
	CLockUp locallock(&m_Lock);
	m_iSendSerial = val % 32768;
}


int CXJMsg104SerialNum::GetSendACKNum()
{
	CLockUp locallock(&m_Lock);
	return m_iSendACKNum;
}

void CXJMsg104SerialNum::SetSendACKNum(int val)
{
	CLockUp locallock(&m_Lock);
	m_iSendACKNum = val % 32768;
}

int CXJMsg104SerialNum::GetReceiveSerial()
{
	CLockUp locallock(&m_Lock);
	return m_iReceiveSerial;
}

void CXJMsg104SerialNum::SetReceiveSerial(int val)
{
	CLockUp locallock(&m_Lock);
	m_iReceiveSerial = val % 32768;
}

int CXJMsg104SerialNum::GetReceiveACKNum()
{
	CLockUp locallock(&m_Lock);
	return m_iReceiveACKNum;
}

void CXJMsg104SerialNum::SetReceiveACKNum(int val)
{
	CLockUp locallock(&m_Lock);
	m_iReceiveACKNum = val % 32768;
}

void CXJMsg104SerialNum::UpdateSendSerial()
{
	CLockUp locallock(&m_Lock);
	m_iSendSerial = (m_iSendSerial + 1) % 32768;
}

void CXJMsg104SerialNum::UpdateReceiveSerial()
{
	CLockUp locallock(&m_Lock);
	m_iReceiveSerial = (m_iReceiveSerial + 1) % 32768;	
	time(&m_tLastTimeOfReceiveInf);
}

void CXJMsg104SerialNum::AssureReceive()
{
	CLockUp locallock(&m_Lock);
	m_iReceiveACKNum = m_iReceiveSerial;
}

void CXJMsg104SerialNum::ResumeSerial()
{
	CLockUp locallock(&m_Lock);
	m_iSendSerial = 0;
	m_iSendACKNum = 0;
	m_iReceiveSerial = 0;
	m_iReceiveACKNum = 0;
	time(&m_tLastTimeOfReceiveInf);
}

bool CXJMsg104SerialNum::IsNeedForAssureReceive(int iSecend)
{
	CLockUp locallock(&m_Lock);
	if ((((m_iReceiveSerial - m_iReceiveACKNum) + 32768) % 32768) >= 8) {
		return true;
	}
	time_t NowTime;
	time(&NowTime);
	if ((m_iReceiveSerial != m_iReceiveACKNum) &&
		((NowTime - m_tLastTimeOfReceiveInf) > iSecend)) {
		return true;
	}
	return false;
}
