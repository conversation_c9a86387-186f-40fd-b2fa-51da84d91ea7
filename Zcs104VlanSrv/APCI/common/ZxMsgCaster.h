/********************************************************************
	created:	2010/09/24
	created:	24:9:2010   16:55
	filename: 	\VICENTE\CODE\SRC\XJCOMMUSERVER\COMMON\XJMsgCaster.h
	file path:	\VICENTE\CODE\SRC\XJCOMMUSERVER\COMMON
	file base:	XJMsgCaster
	file ext:	h
	author:		qingch
	
	purpose:	
*********************************************************************/

#ifndef XJMsgCaster_h__
#define XJMsgCaster_h__

#include "ZxLibMngr_MsgMonitor.h"
#include "ZxTime.h"
typedef vector<BYTE> MESSAGE103;

class CXJMsgCaster
{
public:
	CXJMsgCaster(string pModelName, int pCommuMode, string pCastIP, int pCastPort);
	CXJMsgCaster(string pStationID, string pCastIP, int pCastPort);
	virtual ~CXJMsgCaster();
	virtual bool Start(string& pError);
	virtual bool End();

	virtual void SetLocalIP(string val);
	virtual void SetLocalPort(int val);
	virtual void SetRemoteIP(string val);
	virtual void SetRemotePort(int val);
	virtual int  CastMsgInfo(int pType, int pLevel, int pWay, const string& pMsg, const string& pDesc, string& pError);
	virtual int  CastMsgInfo(int pType, int pLevel, int pWay, const string& pMsg, const string& pDesc, time_t pTime,string& pError);
	virtual int  Cast103MsgInfo(int pType, DWORD pSerialNO, int pWay, const MESSAGE103 & pMsg, const string& pDesc, string& pError);
	virtual int  Cast103MsgInfo(int pType, DWORD pSerialNO, int pWay, const string& pMsg, const string& pDesc, time_t pTime,string& pError);
	virtual void ConvertBitMsgToHexString(const vector<BYTE>& pMsg, string& pHexString);
protected:
	char* Itoa( int value, char* str, int radix );
	char* Strrev( char* szT );
private:
	bool					m_InitSuccess;

	string					m_strLocalIP;

	int						m_iLocalPort;

	string					m_strRemoteIP;

	int						m_iRemotePort;

	string					m_strCastIP;
	
	int						m_iCastPort;

	string					m_strModelName;

	string					m_strStationID;

	int						m_iCommuMode;

	CXJMsgMonitorWrapper	m_iMsgMonitor;
};


extern CXJMsgCaster* GetInstanceMsgCaster(string pStationID);

#endif // XJMsgCaster_h__

